# handle_lighter_project 函数优化总结

## 🎯 优化目标

为 `handle_lighter_project` 函数添加 ServiceMetrics 记录逻辑：

1. **查询 ServiceMetrics 记录是否存在**
2. **如果存在，累加积分和运行时长**
3. **如果不存在，创建新记录并记录积分和运行时长**

## 🔧 优化内容

### 原始逻辑

```python
def handle_lighter_project(lighter_project):
    """处理 Lighter 项目"""
    # 只处理积分发放逻辑
    # 每24小时（288次检查）发放0.01积分
    # 没有记录到 ServiceMetrics
```

### 优化后逻辑

```python
def handle_lighter_project(lighter_project):
    """处理 Lighter 项目"""
    # 1. 计算运行时长增量（每次5分钟 = 300秒）
    running_time_increment = 300
    
    # 2. 处理积分逻辑（每24小时发放0.01积分）
    points_increment = 0
    if lighter_project.continuous_running_checks >= 288 and lighter_project.continuous_running_checks % 288 == 0:
        # 发放积分并记录增量
        points_increment = int(0.01 * 100)  # 转换为整数存储
    
    # 3. 更新或创建 ServiceMetrics 记录
    _update_lighter_service_metrics(
        device_id=lighter_project.device_id,
        project_id=lighter_project.project_id,
        points_increment=points_increment,
        running_time_increment=running_time_increment
    )
```

## 📊 ServiceMetrics 记录逻辑

### 新增辅助函数

```python
def _update_lighter_service_metrics(device_id, project_id, points_increment, running_time_increment):
    """更新或创建 Lighter 项目的 ServiceMetrics 记录"""
    
    # 查询现有记录
    service_metrics = ServiceMetrics.query.filter_by(
        device_id=device_id,
        project_id=project_id,
        service_name=FixtureProjectName.LIGHTER_PROJECT_NAME
    ).first()
    
    if service_metrics:
        # 记录存在：累加积分和运行时长
        service_metrics.points += points_increment
        service_metrics.running_time += running_time_increment
        service_metrics.updated_at = current_time
    else:
        # 记录不存在：创建新记录
        service_metrics = ServiceMetrics(
            device_id=device_id,
            project_id=project_id,
            service_name=FixtureProjectName.LIGHTER_PROJECT_NAME,
            points=points_increment,
            running_time=running_time_increment,
            updated_at=current_time
        )
        db.session.add(service_metrics)
```

## 🔍 关键优化点

### 1. **运行时长记录**

```python
# 每次调用记录5分钟运行时长
running_time_increment = 300  # 5分钟 = 300秒

# 累加到 ServiceMetrics
service_metrics.running_time += running_time_increment
```

**说明**：
- 每次 monitoring 检查代表设备运行了5分钟
- 累计记录设备的总运行时长

### 2. **积分记录**

```python
# 只有在发放积分时才记录积分增量
if lighter_project.continuous_running_checks >= 288 and lighter_project.continuous_running_checks % 288 == 0:
    points_increment = int(0.01 * 100)  # 0.01积分转换为整数1
else:
    points_increment = 0

# 累加到 ServiceMetrics
service_metrics.points += points_increment
```

**说明**：
- 只有在实际发放积分时才记录积分增量
- 积分转换为整数存储（假设精度为2位小数）
- 如果积分发放失败，不记录积分增量

### 3. **记录创建和更新**

```python
if service_metrics:
    # 更新现有记录
    service_metrics.points += points_increment
    service_metrics.running_time += running_time_increment
    service_metrics.updated_at = current_time
else:
    # 创建新记录
    service_metrics = ServiceMetrics(...)
    db.session.add(service_metrics)
```

**说明**：
- 自动检测记录是否存在
- 存在则累加，不存在则创建
- 更新时间戳记录最后更新时间

## 📈 数据记录示例

### 场景1：设备首次运行

```
调用次数: 1
运行时长增量: 300秒
积分增量: 0 (未满24小时)

ServiceMetrics 记录:
- points: 0
- running_time: 300
- service_name: "lighter"
```

### 场景2：设备运行24小时后

```
调用次数: 288
运行时长增量: 300秒
积分增量: 1 (0.01 * 100)

ServiceMetrics 记录:
- points: 1
- running_time: 86400 (288 * 300)
- service_name: "lighter"
```

### 场景3：设备持续运行48小时后

```
调用次数: 576
运行时长增量: 300秒
积分增量: 1 (第二次发放)

ServiceMetrics 记录:
- points: 2 (累计)
- running_time: 172800 (576 * 300)
- service_name: "lighter"
```

## 🧪 测试验证

### 测试脚本

创建了 `test_monitoring_optimization.py` 中的 `test_service_metrics_functionality` 函数：

```python
def test_service_metrics_functionality(device, lighter_project, lighter_asset_type):
    """测试 ServiceMetrics 记录功能"""
    
    # 1. 创建 device_project
    # 2. 验证初始状态（无 ServiceMetrics 记录）
    # 3. 第一次调用 handle_lighter_project
    # 4. 验证创建了 ServiceMetrics 记录
    # 5. 模拟288次调用（24小时）
    # 6. 验证积分和运行时长正确累加
```

### 运行测试

```bash
python test_monitoring_optimization.py
```

### 预期结果

```
✅ 第一次调用后创建了 ServiceMetrics:
   - 积分: 0
   - 运行时长: 300秒
   - continuous_running_checks: 1

✅ 最终 ServiceMetrics 记录:
   - 积分: 1 (期望: 1)
   - 运行时长: 86400秒 (期望: 86400秒)
   - continuous_running_checks: 288

🎉 ServiceMetrics 记录功能测试通过！
```

## 🔧 关键代码变更

### 文件：`app/tasks/monitoring.py`

#### 变更1：添加 ServiceMetrics 导入
```python
from app.models.metrics import ServiceMetricsDetail, ServiceMetrics
```

#### 变更2：优化 handle_lighter_project 函数
```python
def handle_lighter_project(lighter_project):
    # 计算运行时长和积分增量
    running_time_increment = 300
    points_increment = 0
    
    # 原有积分逻辑...
    
    # 新增：更新 ServiceMetrics
    _update_lighter_service_metrics(
        device_id=lighter_project.device_id,
        project_id=lighter_project.project_id,
        points_increment=points_increment,
        running_time_increment=running_time_increment
    )
```

#### 变更3：新增 ServiceMetrics 更新函数
```python
def _update_lighter_service_metrics(device_id, project_id, points_increment, running_time_increment):
    """更新或创建 Lighter 项目的 ServiceMetrics 记录"""
    # 查询、创建或更新逻辑...
```

## 🎯 优化优势

### 1. **完整的数据记录**
- 记录设备的累计运行时长
- 记录设备获得的累计积分
- 提供完整的设备运行历史

### 2. **数据一致性**
- 积分发放和记录同步进行
- 运行时长准确累计
- 避免数据丢失或不一致

### 3. **便于统计分析**
- 可以查询设备的总运行时长
- 可以查询设备获得的总积分
- 支持设备运行效率分析

### 4. **错误处理增强**
- 积分发放失败时不记录积分增量
- 异常处理确保不影响主流程
- 详细的日志记录便于调试

## 📝 注意事项

1. **事务管理**：ServiceMetrics 更新不单独提交事务，由调用方统一管理
2. **数据精度**：积分转换为整数存储，需要注意精度处理
3. **性能考虑**：每次调用都会查询和更新数据库，注意性能影响
4. **数据清理**：长期运行可能产生大量数据，需要考虑数据清理策略

通过这些优化，`handle_lighter_project` 函数现在不仅处理积分发放，还完整记录设备的运行数据，为后续的数据分析和统计提供了基础！
