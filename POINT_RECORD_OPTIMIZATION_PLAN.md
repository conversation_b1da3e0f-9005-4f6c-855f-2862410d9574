# PointRecord 表优化方案

## 🎯 问题分析

### 当前问题
- **数据量过大**：PointRecord 表接近 900 万条记录
- **查询性能差**：频繁出现慢查询
- **分钟级记录**：历史积分记录是分钟级，数据冗余严重
- **重复查询**：用户积分账户和排名数据重复查询 PointRecord

### 使用场景分析
1. **用户积分账户**：查询用户总积分余额
2. **排名数据**：积分排行榜查询
3. **历史记录**：用户积分变动历史
4. **设备指标**：设备相关的积分统计
5. **项目指标**：项目相关的积分统计

## 🔧 优化策略

### 策略1：积分账户迁移到资产系统
**目标**：将用户积分账户和排名数据迁移到现有的 UserAsset 系统

**优势**：
- 利用现有的资产管理基础设施
- 统一的余额查询接口
- 更好的性能和索引优化
- 支持事务性操作

### 策略2：历史数据按时间维度归档
**目标**：将分钟级记录按天、周、月合并归档

**优势**：
- 大幅减少数据量
- 提高查询性能
- 保留历史趋势数据
- 支持不同时间粒度的分析

### 策略3：无感迁移
**目标**：确保用户在迁移过程中无感知

**方案**：
- 双写模式：新数据同时写入新旧系统
- 渐进式迁移：分批迁移历史数据
- 兼容性接口：保持 API 接口不变

## 📊 新表结构设计

### 1. 积分归档表 (point_records_archive)

```sql
CREATE TABLE point_records_archive (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    record_type VARCHAR(20) NOT NULL,
    archive_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    archive_date DATE NOT NULL,
    total_points DECIMAL(18,8) NOT NULL,
    record_count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_type_date (user_id, archive_type, archive_date),
    INDEX idx_archive_date (archive_date),
    INDEX idx_user_record_type (user_id, record_type),
    UNIQUE KEY uk_user_type_date (user_id, record_type, archive_type, archive_date)
);
```

### 2. 积分统计快照表 (point_statistics_snapshot)

```sql
CREATE TABLE point_statistics_snapshot (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    snapshot_date DATE NOT NULL,
    total_points DECIMAL(18,8) NOT NULL,
    task_points DECIMAL(18,8) NOT NULL DEFAULT 0,
    invite_points DECIMAL(18,8) NOT NULL DEFAULT 0,
    project_points DECIMAL(18,8) NOT NULL DEFAULT 0,
    daily_increase DECIMAL(18,8) NOT NULL DEFAULT 0,
    rank_position INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_date (user_id, snapshot_date),
    INDEX idx_snapshot_date (snapshot_date),
    INDEX idx_total_points (total_points DESC),
    UNIQUE KEY uk_user_date (user_id, snapshot_date)
);
```

## 🔄 迁移流程

### 阶段1：准备阶段
1. 创建新表结构
2. 创建迁移服务
3. 创建数据验证工具

### 阶段2：双写阶段
1. 修改积分服务，同时写入新旧系统
2. 验证数据一致性
3. 监控性能指标

### 阶段3：历史数据迁移
1. 分批迁移历史积分数据到 UserAsset
2. 生成历史归档数据
3. 验证迁移结果

### 阶段4：切换阶段
1. 修改查询接口使用新数据源
2. 停止写入旧系统
3. 监控系统稳定性

### 阶段5：清理阶段
1. 备份原始数据
2. 清理冗余数据
3. 优化索引和性能

## 📈 性能预期

### 查询性能提升
- **用户积分查询**：从 O(n) 降到 O(1)
- **排名查询**：利用 UserAsset 索引，性能提升 10x+
- **历史查询**：按时间维度聚合，数据量减少 90%+

### 存储空间优化
- **原始数据**：900万条 → 保留最近3个月
- **归档数据**：按天/周/月聚合，压缩比 95%+
- **总体减少**：预计减少 80%+ 存储空间

## 🛠️ 实施计划

### 第一步：创建归档表结构
- 设计并创建新的归档表
- 添加必要的索引
- 创建数据迁移脚本

### 第二步：实现积分账户迁移
- 修改 UbiPointsIntegrationService
- 确保积分数据同步到 UserAsset
- 更新排名查询逻辑

### 第三步：实现历史数据归档
- 创建归档服务
- 实现按时间维度聚合
- 支持增量归档

### 第四步：更新查询接口
- 修改用户面板接口
- 更新设备指标查询
- 调整项目指标统计

### 第五步：数据迁移和验证
- 执行历史数据迁移
- 验证数据一致性
- 性能测试和优化

## 🔍 风险控制

### 数据一致性风险
- **双写验证**：确保新旧系统数据一致
- **回滚机制**：支持快速回滚到原系统
- **数据校验**：定期校验数据完整性

### 性能风险
- **渐进式迁移**：避免一次性大量数据迁移
- **监控告警**：实时监控系统性能
- **降级方案**：准备降级到原系统的方案

### 业务连续性风险
- **兼容性接口**：保持 API 接口不变
- **灰度发布**：分批次发布新功能
- **快速修复**：准备快速修复机制

## 📋 验收标准

### 功能验收
- [ ] 用户积分查询正常
- [ ] 排名功能正常
- [ ] 历史记录查询正常
- [ ] 设备指标统计正常
- [ ] 项目指标统计正常

### 性能验收
- [ ] 积分查询响应时间 < 100ms
- [ ] 排名查询响应时间 < 200ms
- [ ] 历史查询响应时间 < 500ms
- [ ] 数据库 CPU 使用率降低 50%+

### 数据验收
- [ ] 迁移前后数据一致性 100%
- [ ] 归档数据完整性验证通过
- [ ] 存储空间减少 80%+

这个优化方案将显著提升系统性能，同时保证数据完整性和业务连续性！
