"""
区块链监听服务优化配置
用于减少 Web3 API Credits 消耗
"""

# 智能间隔配置
CHAIN_BLOCK_INTERVALS = {
    "ETH": {
        "base_interval": 12,    # 以太坊约12秒一个区块
        "max_interval": 60,     # 最大间隔60秒
        "min_interval": 5,      # 最小间隔5秒
        "sync_multiplier": 2,   # 同步完成后的倍数
    },
    "BSC": {
        "base_interval": 3,     # BSC约3秒一个区块
        "max_interval": 30,     # 最大间隔30秒
        "min_interval": 2,      # 最小间隔2秒
        "sync_multiplier": 2,
    },
    "ARB": {
        "base_interval": 1,     # Arbitrum约1秒一个区块
        "max_interval": 15,     # 最大间隔15秒
        "min_interval": 1,      # 最小间隔1秒
        "sync_multiplier": 3,
    },
    "SOL": {
        "base_interval": 1,     # Solana约400ms一个区块
        "max_interval": 10,     # 最大间隔10秒
        "min_interval": 1,      # 最小间隔1秒
        "sync_multiplier": 2,
    },
    "DEFAULT": {
        "base_interval": 15,    # 默认15秒
        "max_interval": 60,     # 最大间隔60秒
        "min_interval": 5,      # 最小间隔5秒
        "sync_multiplier": 2,
    }
}

# 缓存配置
CACHE_CONFIG = {
    "decimals_cache_size": 1000,      # 代币精度缓存大小
    "address_cache_size": 5000,       # 地址类型缓存大小
    "cache_ttl": 3600,                # 缓存TTL（秒）
}

# 批量处理配置
BATCH_CONFIG = {
    "max_blocks_per_batch": 10,       # 每批最大处理区块数
    "max_transactions_per_batch": 100, # 每批最大处理交易数
    "enable_parallel_processing": True, # 启用并行处理
}

# API 调用优化配置
API_OPTIMIZATION = {
    "enable_contract_cache": True,     # 启用合约地址缓存
    "enable_decimals_cache": True,     # 启用代币精度缓存
    "enable_balance_cache": False,     # 余额缓存（实时性要求高，默认关闭）
    "skip_zero_value_tx": True,        # 跳过零金额交易
    "skip_failed_tx": True,            # 跳过失败交易
}

# 启发式优化
HEURISTIC_CONFIG = {
    "contract_address_patterns": [
        "000000",  # 合约地址通常以多个0结尾
        "111111",  # 某些特殊合约模式
    ],
    "eoa_address_patterns": [
        # EOA地址的常见模式（可以根据实际情况调整）
    ],
    "known_contracts": {
        # 已知的热门合约地址（可以预填充）
        "ETH": [
            "******************************************",  # 示例USDT
            "******************************************",  # 示例USDC
        ],
        "BSC": [
            "******************************************",  # BSC-USD
        ],
    },
    "known_eoa": {
        # 已知的热门EOA地址
    }
}

# 监控和统计配置
MONITORING_CONFIG = {
    "enable_api_call_counting": True,   # 启用API调用计数
    "enable_performance_logging": True, # 启用性能日志
    "log_interval": 300,               # 日志输出间隔（秒）
    "enable_cache_hit_rate": True,     # 启用缓存命中率统计
}

# 错误处理和重试配置
ERROR_HANDLING = {
    "max_retries": 3,                  # 最大重试次数
    "retry_delay": 5,                  # 重试延迟（秒）
    "exponential_backoff": True,       # 指数退避
    "skip_on_persistent_error": True,  # 持续错误时跳过
}

def get_chain_config(chain_code: str) -> dict:
    """获取链的配置"""
    return CHAIN_BLOCK_INTERVALS.get(chain_code, CHAIN_BLOCK_INTERVALS["DEFAULT"])

def get_optimal_interval(chain_code: str, blocks_behind: int) -> int:
    """计算最优间隔"""
    config = get_chain_config(chain_code)
    
    if blocks_behind == 0:
        # 已同步到最新
        return min(config["base_interval"] * config["sync_multiplier"], config["max_interval"])
    elif blocks_behind <= 3:
        # 接近最新
        return config["base_interval"]
    else:
        # 落后较多，快速追赶
        return max(config["base_interval"] // 2, config["min_interval"])

def should_enable_optimization(optimization_type: str) -> bool:
    """检查是否启用某项优化"""
    return API_OPTIMIZATION.get(optimization_type, False)

def get_cache_config() -> dict:
    """获取缓存配置"""
    return CACHE_CONFIG

def get_monitoring_config() -> dict:
    """获取监控配置"""
    return MONITORING_CONFIG
