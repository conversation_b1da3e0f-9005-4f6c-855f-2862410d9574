import os
from datetime import timedelta

from celery.schedules import crontab

from app.middlewares.redis_subscriber import init_redis_subscriber


class BaseConfig:
    """Base configuration."""
    # 基础配置
    SECRET_KEY = os.environ.get("SECRET_KEY") or "dev"
    JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY") or "dev-jwt"
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(days=30)
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 加密配置
    ENCRYPTION_KEY = (
            os.environ.get("ENCRYPTION_KEY") or "12345678901234567890123456789012"
    )  # 32字节密钥

    # Redis configuration

    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = os.getenv('REDIS_PORT', '6379')
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')
    REDIS_DB = os.getenv('REDIS_DB', '0')

    # Construct Redis URL
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}" if REDIS_PASSWORD else f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

    CHAIN_EVM_TOKEN_CHECK = os.getenv('CHAIN_EVM_TOKEN_CHECK', '')

    # Celery configuration
    CELERY_CONFIG = {
        'broker_url': REDIS_URL,
        'result_backend': REDIS_URL,
        'broker_connection_retry_on_startup': True,
        'broker_connection_max_retries': 10,
        'broker_connection_timeout': 30,
        'imports': ('app.tasks.monitoring', 'app.tasks.metrics_tasks', 'app.tasks.check_first_project_running',
                   'app.tasks.device_project_validation', 'app.tasks.notify_tasks', 'app.tasks.point_rank',
                   'app.tasks.blockchain_tasks'),
        'task_serializer': 'json',
        'result_serializer': 'json',
        'accept_content': ['json'],
        'timezone': 'UTC',
        'enable_utc': True,
        'beat_schedule': {
            'check-device-status': {
                'task': 'app.tasks.monitoring.check_device_status',
                'schedule': timedelta(minutes=5),
            },
            'process-invite-records': {
                'task': 'app.tasks.check_first_project_running.process_invite_records',
                'schedule': timedelta(minutes=5)
            },
            'update-yesterday-metrics': {
                'task': 'update_yesterday_metrics',
                'schedule': crontab(hour='0', minute='3'),  # Run once per day
            },
            'aggregate-service-metrics': {
                'task': 'aggregate_service_metrics',
                'schedule': timedelta(minutes=30)  # 每半小时运行一次
            },
            'ubi-asset-async-task': {
                'task': 'app.tasks.point_rank.calc_point_rank',
                'schedule': timedelta(minutes=10),
            },
            # 区块链监听和结算任务
            'sync-all-blockchains': {
                'task': 'app.tasks.blockchain_tasks.sync_all_blockchains',
                'schedule': timedelta(minutes=2),  # 每2分钟检查一次
            },
            'process-pending-settlements': {
                'task': 'app.tasks.blockchain_tasks.process_pending_settlements',
                'schedule': timedelta(minutes=10),  # 每10分钟处理一次结算
            },
            'cleanup-old-sync-logs': {
                'task': 'app.tasks.blockchain_tasks.cleanup_old_sync_logs',
                'schedule': crontab(hour='2', minute='0'),  # 每天凌晨2点清理
            },
            'update-address-statistics': {
                'task': 'app.tasks.blockchain_tasks.update_address_statistics',
                'schedule': timedelta(hours=1),  # 每小时更新一次地址统计
            }
        }
    }

    @staticmethod
    def init_app(app):
        """Initialize application configuration."""
        pass


class DevelopmentConfig(BaseConfig):
    """Development configuration."""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL")

    @staticmethod
    def init_app(app):
        """Initialize application configuration."""
        init_redis_subscriber()

class TestingConfig(BaseConfig):
    """Testing configuration."""
    TESTING = True

    # Use memory broker for testing
    broker_url = 'memory://'
    result_backend = 'cache'
    cache_backend = 'memory'

    # Celery testing settings
    task_always_eager = True
    task_eager_propagates = True

    SQLALCHEMY_DATABASE_URI = os.environ.get("TEST_DATABASE_URL") or "sqlite:///:memory:"
    WTF_CSRF_ENABLED = False

    @classmethod
    def init_app(cls, app):
        BaseConfig.init_app(app)
        app.config['TESTING'] = True


class ProductionConfig(BaseConfig):
    """Production configuration."""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL")

    # 确保使用基类的 CELERY_CONFIG
    CELERY_CONFIG = {
        **BaseConfig.CELERY_CONFIG,
        # Production-specific overrides
        'task_track_started': True,
        'task_time_limit': 30 * 60,  # 30 minutes
        'worker_send_task_events': True,
        'task_send_sent_event': True,
        'broker_pool_limit': 50,
        'worker_max_tasks_per_child': 1000,
        'worker_prefetch_multiplier': 4,
        'result_expires': 60 * 60 * 24  # 24 hours
    }

    @classmethod
    def init_app(cls, app):
        BaseConfig.init_app(app)

        # 生产环境的日志处理
        import logging
        from logging.handlers import RotatingFileHandler

        if not os.path.exists("logs"):
            os.mkdir("logs")
        file_handler = RotatingFileHandler(
            "logs/box.log", maxBytes=10240, backupCount=10
        )
        file_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]"
            )
        )
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

        app.logger.setLevel(logging.INFO)
        app.logger.info("Box startup")
        init_redis_subscriber()



config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
