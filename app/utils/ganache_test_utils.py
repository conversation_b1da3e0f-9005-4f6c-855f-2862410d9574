"""
Ganache 测试工具类
用于在本地 Ganache 环境中执行 airdrop、mint 等测试操作
"""

import json
from decimal import Decimal
from typing import Optional, Dict, List, Any
from web3 import Web3
from eth_account import Account

try:
    from flask import current_app
except ImportError:
    # 如果不在 Flask 环境中，创建一个简单的日志对象
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")

    class MockApp:
        logger = MockLogger()

    current_app = MockApp()


class GanacheTestUtils:
    """Ganache 测试工具类"""
    
    def __init__(self, ganache_url: str = "http://127.0.0.1:7545"):
        """
        初始化 Ganache 连接

        Args:
            ganache_url: Ganache RPC URL
        """
        self.web3 = Web3(Web3.HTTPProvider(ganache_url))

        # Ganache 通常不需要 POA 中间件，直接连接即可

        # 验证连接
        if not self.web3.is_connected():
            raise ConnectionError(f"无法连接到 Ganache: {ganache_url}")

        print(f"✅ 已连接到 Ganache: {ganache_url}")
        print(f"📊 当前区块高度: {self.web3.eth.block_number}")

        # 获取默认账户（Ganache 预设账户）
        self.accounts = self.web3.eth.accounts
        self.default_account = self.accounts[0] if self.accounts else None

        print(f"💰 可用账户数量: {len(self.accounts)}")
        if self.default_account:
            balance = self.web3.eth.get_balance(self.default_account)
            print(f"🏦 默认账户余额: {self.web3.from_wei(balance, 'ether')} ETH")

    def get_account_info(self, address: Optional[str] = None) -> Dict[str, Any]:
        """
        获取账户信息
        
        Args:
            address: 账户地址，默认使用第一个账户
            
        Returns:
            账户信息字典
        """
        if not address:
            address = self.default_account
            
        if not address:
            raise ValueError("没有可用的账户地址")
            
        balance_wei = self.web3.eth.get_balance(address)
        balance_eth = self.web3.from_wei(balance_wei, 'ether')
        nonce = self.web3.eth.get_transaction_count(address)
        
        return {
            "address": address,
            "balance_wei": balance_wei,
            "balance_eth": str(balance_eth),
            "nonce": nonce
        }

    def airdrop_eth(self, to_address: str, amount_eth: float, from_address: Optional[str] = None) -> str:
        """
        空投 ETH 到指定地址
        
        Args:
            to_address: 接收地址
            amount_eth: ETH 数量
            from_address: 发送地址，默认使用第一个账户
            
        Returns:
            交易哈希
        """
        if not from_address:
            from_address = self.default_account
            
        if not from_address:
            raise ValueError("没有可用的发送账户")
            
        # 检查余额
        balance = self.web3.eth.get_balance(from_address)
        amount_wei = self.web3.to_wei(amount_eth, 'ether')
        
        if balance < amount_wei:
            raise ValueError(f"余额不足: 需要 {amount_eth} ETH，当前余额 {self.web3.from_wei(balance, 'ether')} ETH")
        
        # 构建交易
        transaction = {
            'to': to_address,
            'value': amount_wei,
            'gas': 21000,
            'gasPrice': self.web3.to_wei('20', 'gwei'),
            'nonce': self.web3.eth.get_transaction_count(from_address),
        }
        
        # 发送交易（Ganache 环境下账户已解锁）
        try:
            tx_hash = self.web3.eth.send_transaction(transaction)
            print(f"💸 空投交易已发送: {tx_hash.hex()}")
            
            # 等待交易确认
            receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash)
            print(f"✅ 空投成功: {amount_eth} ETH -> {to_address}")
            print(f"📋 Gas 使用: {receipt.gasUsed}")
            
            return tx_hash.hex()
            
        except Exception as e:
            print(f"❌ 空投失败: {e}")
            raise

    def get_simple_erc20_contract(self) -> Dict[str, Any]:
        """
        获取简单的 ERC20 合约 ABI 和字节码

        Returns:
            包含 ABI 和字节码的字典
        """
        # 简化的 ERC20 合约 ABI
        erc20_abi = [
            {
                "inputs": [
                    {"internalType": "string", "name": "_name", "type": "string"},
                    {"internalType": "string", "name": "_symbol", "type": "string"},
                    {"internalType": "uint256", "name": "_totalSupply", "type": "uint256"}
                ],
                "stateMutability": "nonpayable",
                "type": "constructor"
            },
            {
                "inputs": [
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "amount", "type": "uint256"}
                ],
                "name": "transfer",
                "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "name",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "symbol",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "decimals",
                "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "totalSupply",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]

        # 简化的 ERC20 合约字节码（这是一个非常基础的实现）
        # 实际项目中应该使用 OpenZeppelin 等标准库编译的合约
        erc20_bytecode = "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"

        return {
            "abi": erc20_abi,
            "bytecode": erc20_bytecode
        }

    def deploy_erc20_token(self,
                          name: str = "TestToken",
                          symbol: str = "TEST",
                          initial_supply: int = 1000000,
                          from_address: Optional[str] = None) -> Dict[str, Any]:
        """
        部署 ERC20 测试代币

        Args:
            name: 代币名称
            symbol: 代币符号
            initial_supply: 初始供应量（以 token 为单位，会自动转换为 wei）
            from_address: 部署账户

        Returns:
            部署信息字典
        """
        if not from_address:
            from_address = self.default_account

        print(f"🚀 部署 ERC20 代币: {name} ({symbol})")
        print(f"📊 初始供应量: {initial_supply} {symbol}")

        try:
            # 获取合约信息
            contract_info = self.get_simple_erc20_contract()

            # 创建合约实例
            contract = self.web3.eth.contract(
                abi=contract_info["abi"],
                bytecode=contract_info["bytecode"]
            )

            # 构建部署交易
            # 将初始供应量转换为 wei（18位小数）
            initial_supply_wei = initial_supply * (10 ** 18)

            transaction = contract.constructor(
                name,
                symbol,
                initial_supply_wei
            ).build_transaction({
                'from': from_address,
                'gas': 2000000,
                'gasPrice': self.web3.to_wei('20', 'gwei'),
                'nonce': self.web3.eth.get_transaction_count(from_address),
            })

            # 发送部署交易
            tx_hash = self.web3.eth.send_transaction(transaction)
            print(f"📋 部署交易已发送: {tx_hash.hex()}")

            # 等待交易确认
            receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash)
            contract_address = receipt.contractAddress

            print(f"✅ 合约部署成功!")
            print(f"📍 合约地址: {contract_address}")
            print(f"📋 Gas 使用: {receipt.gasUsed}")

            return {
                "contract_address": contract_address,
                "name": name,
                "symbol": symbol,
                "decimals": 18,  # 固定为18位小数
                "initial_supply": initial_supply,
                "initial_supply_wei": initial_supply_wei,
                "deployer": from_address,
                "tx_hash": tx_hash.hex(),
                "abi": contract_info["abi"],
                "gas_used": receipt.gasUsed
            }

        except Exception as e:
            print(f"❌ 部署失败: {e}")
            raise

    def transfer_tokens(self,
                       contract_address: str,
                       to_address: str,
                       amount: float,
                       from_address: Optional[str] = None) -> str:
        """
        转账代币

        Args:
            contract_address: 代币合约地址
            to_address: 接收地址
            amount: 转账数量（以 token 为单位）
            from_address: 发送账户

        Returns:
            交易哈希
        """
        if not from_address:
            from_address = self.default_account

        print(f"💸 转账代币:")
        print(f"📍 合约地址: {contract_address}")
        print(f"👤 从: {from_address}")
        print(f"👤 到: {to_address}")
        print(f"💰 数量: {amount} tokens")

        try:
            # 获取合约信息
            contract_info = self.get_simple_erc20_contract()

            # 创建合约实例
            contract = self.web3.eth.contract(
                address=contract_address,
                abi=contract_info["abi"]
            )

            # 转换为 wei（18位小数）
            amount_wei = int(amount * (10 ** 18))

            # 构建转账交易
            transaction = contract.functions.transfer(
                to_address,
                amount_wei
            ).build_transaction({
                'from': from_address,
                'gas': 100000,
                'gasPrice': self.web3.to_wei('20', 'gwei'),
                'nonce': self.web3.eth.get_transaction_count(from_address),
            })

            # 发送交易
            tx_hash = self.web3.eth.send_transaction(transaction)
            print(f"📋 转账交易已发送: {tx_hash.hex()}")

            # 等待交易确认
            receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash)
            print(f"✅ 转账成功!")
            print(f"📋 Gas 使用: {receipt.gasUsed}")

            return tx_hash.hex()

        except Exception as e:
            print(f"❌ 转账失败: {e}")
            raise

    def get_token_balance(self, contract_address: str, account_address: str) -> float:
        """
        查询代币余额

        Args:
            contract_address: 代币合约地址
            account_address: 账户地址

        Returns:
            代币余额（以 token 为单位）
        """
        try:
            # 获取合约信息
            contract_info = self.get_simple_erc20_contract()

            # 创建合约实例
            contract = self.web3.eth.contract(
                address=contract_address,
                abi=contract_info["abi"]
            )

            # 查询余额
            balance_wei = contract.functions.balanceOf(account_address).call()
            balance_tokens = balance_wei / (10 ** 18)

            return balance_tokens

        except Exception as e:
            print(f"❌ 查询余额失败: {e}")
            return 0.0

    def get_token_info(self, contract_address: str) -> Dict[str, Any]:
        """
        获取代币信息

        Args:
            contract_address: 代币合约地址

        Returns:
            代币信息字典
        """
        try:
            # 获取合约信息
            contract_info = self.get_simple_erc20_contract()

            # 创建合约实例
            contract = self.web3.eth.contract(
                address=contract_address,
                abi=contract_info["abi"]
            )

            # 查询代币信息
            name = contract.functions.name().call()
            symbol = contract.functions.symbol().call()
            decimals = contract.functions.decimals().call()
            total_supply_wei = contract.functions.totalSupply().call()
            total_supply = total_supply_wei / (10 ** decimals)

            return {
                "contract_address": contract_address,
                "name": name,
                "symbol": symbol,
                "decimals": decimals,
                "total_supply": total_supply,
                "total_supply_wei": total_supply_wei
            }

        except Exception as e:
            print(f"❌ 获取代币信息失败: {e}")
            return {}

    def get_all_accounts_info(self) -> List[Dict[str, Any]]:
        """
        获取所有 Ganache 账户信息
        
        Returns:
            账户信息列表
        """
        accounts_info = []
        
        for i, address in enumerate(self.accounts):
            info = self.get_account_info(address)
            info["index"] = i
            accounts_info.append(info)
            
        return accounts_info

    def fund_account(self, target_address: str, amount_eth: float = 10.0) -> str:
        """
        为指定账户充值 ETH（从第一个账户转账）
        
        Args:
            target_address: 目标地址
            amount_eth: 充值金额
            
        Returns:
            交易哈希
        """
        return self.airdrop_eth(target_address, amount_eth)

    def create_test_scenario(self) -> Dict[str, Any]:
        """
        创建测试场景：部署代币并进行一些测试操作
        
        Returns:
            测试场景信息
        """
        print("🎭 创建测试场景...")
        
        # 1. 获取账户信息
        accounts = self.get_all_accounts_info()[:5]  # 只取前5个账户
        
        # 2. 部署测试代币
        token_info = self.deploy_erc20_token(
            name="Ganache Test Token",
            symbol="GTT",
            decimals=18,
            initial_supply=1000000
        )
        
        # 3. 模拟一些转账
        test_transfers = []
        if len(accounts) >= 3:
            # 从账户0向账户1转账
            tx1 = self.airdrop_eth(accounts[1]["address"], 5.0)
            test_transfers.append({
                "from": accounts[0]["address"],
                "to": accounts[1]["address"],
                "amount": "5.0 ETH",
                "tx_hash": tx1
            })
            
            # 从账户0向账户2转账
            tx2 = self.airdrop_eth(accounts[2]["address"], 3.0)
            test_transfers.append({
                "from": accounts[0]["address"],
                "to": accounts[2]["address"],
                "amount": "3.0 ETH",
                "tx_hash": tx2
            })
        
        scenario = {
            "accounts": accounts,
            "token": token_info,
            "test_transfers": test_transfers,
            "block_number": self.web3.eth.block_number,
            "network_id": self.web3.eth.chain_id
        }
        
        print("✅ 测试场景创建完成!")
        return scenario


def create_ganache_utils(ganache_url: str = "http://127.0.0.1:7545") -> GanacheTestUtils:
    """
    创建 Ganache 工具实例
    
    Args:
        ganache_url: Ganache RPC URL
        
    Returns:
        GanacheTestUtils 实例
    """
    return GanacheTestUtils(ganache_url)


# 使用示例
if __name__ == "__main__":
    # 创建工具实例
    ganache = create_ganache_utils()
    
    # 获取账户信息
    accounts = ganache.get_all_accounts_info()
    print(f"📋 账户列表: {len(accounts)} 个账户")
    
    # 创建测试场景
    scenario = ganache.create_test_scenario()
    print(f"🎯 测试场景: {json.dumps(scenario, indent=2, default=str)}")
