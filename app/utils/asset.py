from decimal import Decimal

MAKER_FEE_RATE = Decimal('0.002')  # Maker 0.2%
TAKER_FEE_RATE = Decimal('0.003')  # Taker 0.3%


def format_decimal_by_precision(value, decimals):
    """
    根据指定的小数位数格式化 Decimal 值为字符串

    Args:
        value: Decimal 或可转换为 Decimal 的值
        decimals: 小数位数

    Returns:
        str: 格式化后的字符串，避免科学计数法

    Examples:
        format_decimal_by_precision(Decimal('0.000001'), 6) -> '0.000001'
        format_decimal_by_precision(Decimal('1000000'), 2) -> '1000000.00'
        format_decimal_by_precision(Decimal('0'), 8) -> '0.00000000'
    """
    if value is None:
        return '0'

    # 确保是 Decimal 类型
    if not isinstance(value, Decimal):
        value = Decimal(str(value))

    # 使用 quantize 方法格式化到指定小数位数
    # 这样可以避免科学计数法，并确保小数位数正确
    format_str = '0.' + '0' * decimals if decimals > 0 else '0'
    quantized_value = value.quantize(Decimal(format_str))

    return str(quantized_value)


def calculate_order_freeze(amount: Decimal, price: Decimal, fee_percent: Decimal, order_type: str) -> Decimal:
    """计算订单冻结金额"""
    if order_type == 'SELL':  # 卖单
        # 冻结基础资产数量 + 手续费（基础资产）
        freeze_amount = amount + (amount * fee_percent)
        return freeze_amount
    elif order_type == 'BUY':  # 买单
        # 计价资产的数量
        quote_asset_amount = amount * price
        # 冻结计价资产数量 + 手续费（计价资产）
        freeze_amount = quote_asset_amount + (quote_asset_amount * fee_percent)
        return freeze_amount
    else:
        raise ValueError("Invalid order_type. Must be 'BUY' or 'SELL'.")


def calculate_order_fee(amount: Decimal, price: Decimal, fee_percent: Decimal, order_type: str) -> Decimal:
    """计算订单手续费"""
    if order_type == 'SELL':  # 卖单，手续费为基础资产
        fee = amount * fee_percent
        return fee
    elif order_type == 'BUY':  # 买单，手续费为计价资产
        quote_asset_amount = amount * price
        fee = quote_asset_amount * fee_percent
        return fee
    else:
        raise ValueError("Invalid order_type. Must be 'BUY' or 'SELL'.")
