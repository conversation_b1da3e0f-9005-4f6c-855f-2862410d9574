"""
区块链监听服务性能监控器
用于跟踪 API 调用次数、缓存命中率等关键指标
"""

import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from flask import current_app


class BlockchainPerformanceMonitor:
    """区块链性能监控器"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = BlockchainPerformanceMonitor()
        return cls._instance
    
    def __init__(self):
        self.reset_stats()
        self.start_time = time.time()
        self.last_report_time = time.time()
        
    def reset_stats(self):
        """重置统计数据"""
        # API 调用统计
        self.api_calls = defaultdict(int)
        self.api_call_times = defaultdict(list)
        
        # 缓存统计
        self.cache_hits = defaultdict(int)
        self.cache_misses = defaultdict(int)
        
        # 区块处理统计
        self.blocks_processed = 0
        self.transactions_processed = 0
        self.transactions_filtered = 0
        
        # 错误统计
        self.errors = defaultdict(int)
        
        # 性能统计
        self.processing_times = deque(maxlen=1000)  # 保留最近1000次处理时间
        
    def record_api_call(self, api_type: str, duration: float = None):
        """记录 API 调用"""
        self.api_calls[api_type] += 1
        if duration is not None:
            self.api_call_times[api_type].append(duration)
            # 只保留最近100次调用时间
            if len(self.api_call_times[api_type]) > 100:
                self.api_call_times[api_type] = self.api_call_times[api_type][-100:]
    
    def record_cache_hit(self, cache_type: str):
        """记录缓存命中"""
        self.cache_hits[cache_type] += 1
    
    def record_cache_miss(self, cache_type: str):
        """记录缓存未命中"""
        self.cache_misses[cache_type] += 1
    
    def record_block_processed(self, transaction_count: int, filtered_count: int):
        """记录区块处理"""
        self.blocks_processed += 1
        self.transactions_processed += transaction_count
        self.transactions_filtered += filtered_count
    
    def record_error(self, error_type: str):
        """记录错误"""
        self.errors[error_type] += 1
    
    def record_processing_time(self, duration: float):
        """记录处理时间"""
        self.processing_times.append(duration)
    
    def get_cache_hit_rate(self, cache_type: str) -> float:
        """获取缓存命中率"""
        hits = self.cache_hits[cache_type]
        misses = self.cache_misses[cache_type]
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0
    
    def get_api_call_stats(self) -> Dict[str, Any]:
        """获取 API 调用统计"""
        stats = {}
        for api_type, count in self.api_calls.items():
            times = self.api_call_times.get(api_type, [])
            avg_time = sum(times) / len(times) if times else 0
            stats[api_type] = {
                "count": count,
                "avg_duration": round(avg_time, 3),
                "total_duration": round(sum(times), 3)
            }
        return stats
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        current_time = time.time()
        uptime = current_time - self.start_time
        
        # 计算处理速度
        blocks_per_minute = (self.blocks_processed / uptime * 60) if uptime > 0 else 0
        transactions_per_minute = (self.transactions_processed / uptime * 60) if uptime > 0 else 0
        
        # 计算平均处理时间
        avg_processing_time = (sum(self.processing_times) / len(self.processing_times)) if self.processing_times else 0
        
        # 计算过滤效率
        filter_rate = (self.transactions_filtered / self.transactions_processed * 100) if self.transactions_processed > 0 else 0
        
        return {
            "uptime_seconds": round(uptime, 2),
            "blocks_processed": self.blocks_processed,
            "transactions_processed": self.transactions_processed,
            "transactions_filtered": self.transactions_filtered,
            "filter_rate_percent": round(filter_rate, 2),
            "blocks_per_minute": round(blocks_per_minute, 2),
            "transactions_per_minute": round(transactions_per_minute, 2),
            "avg_processing_time_ms": round(avg_processing_time * 1000, 2),
            "total_api_calls": sum(self.api_calls.values()),
            "total_errors": sum(self.errors.values())
        }
    
    def get_cache_summary(self) -> Dict[str, Any]:
        """获取缓存摘要"""
        cache_summary = {}
        all_cache_types = set(self.cache_hits.keys()) | set(self.cache_misses.keys())
        
        for cache_type in all_cache_types:
            hits = self.cache_hits[cache_type]
            misses = self.cache_misses[cache_type]
            total = hits + misses
            hit_rate = (hits / total * 100) if total > 0 else 0
            
            cache_summary[cache_type] = {
                "hits": hits,
                "misses": misses,
                "total": total,
                "hit_rate_percent": round(hit_rate, 2)
            }
        
        return cache_summary
    
    def should_report(self, interval_seconds: int = 300) -> bool:
        """检查是否应该输出报告"""
        current_time = time.time()
        if current_time - self.last_report_time >= interval_seconds:
            self.last_report_time = current_time
            return True
        return False
    
    def generate_report(self) -> str:
        """生成性能报告"""
        performance = self.get_performance_summary()
        api_stats = self.get_api_call_stats()
        cache_stats = self.get_cache_summary()
        
        report = []
        report.append("=" * 60)
        report.append("🔍 区块链监听服务性能报告")
        report.append("=" * 60)
        
        # 基础性能指标
        report.append(f"⏱️  运行时间: {performance['uptime_seconds']} 秒")
        report.append(f"📦 处理区块: {performance['blocks_processed']} 个")
        report.append(f"💳 处理交易: {performance['transactions_processed']} 笔")
        report.append(f"🔽 过滤交易: {performance['transactions_filtered']} 笔 ({performance['filter_rate_percent']}%)")
        report.append(f"⚡ 处理速度: {performance['blocks_per_minute']} 区块/分钟")
        report.append(f"📊 平均处理时间: {performance['avg_processing_time_ms']} ms")
        
        # API 调用统计
        report.append(f"\n📡 API 调用统计 (总计: {performance['total_api_calls']} 次):")
        for api_type, stats in api_stats.items():
            report.append(f"  {api_type}: {stats['count']} 次 (平均 {stats['avg_duration']}s)")
        
        # 缓存统计
        if cache_stats:
            report.append(f"\n💾 缓存统计:")
            for cache_type, stats in cache_stats.items():
                report.append(f"  {cache_type}: {stats['hit_rate_percent']}% 命中率 ({stats['hits']}/{stats['total']})")
        
        # 错误统计
        if self.errors:
            report.append(f"\n❌ 错误统计:")
            for error_type, count in self.errors.items():
                report.append(f"  {error_type}: {count} 次")
        
        # Credits 节省估算
        estimated_savings = self._estimate_credits_savings()
        if estimated_savings > 0:
            report.append(f"\n💰 估算 Credits 节省: {estimated_savings} 次调用")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def _estimate_credits_savings(self) -> int:
        """估算节省的 Credits"""
        # 基于缓存命中次数估算节省的 API 调用
        savings = 0
        for cache_type, hits in self.cache_hits.items():
            if cache_type in ['decimals', 'contract_code']:
                savings += hits  # 每次缓存命中节省一次 API 调用
        
        return savings
    
    def log_report_if_needed(self, interval_seconds: int = 300):
        """如果需要，输出性能报告到日志"""
        if self.should_report(interval_seconds):
            try:
                report = self.generate_report()
                current_app.logger.info(f"\n{report}")
            except Exception as e:
                current_app.logger.error(f"生成性能报告失败: {e}")


# 全局监控器实例
performance_monitor = BlockchainPerformanceMonitor.get_instance()


# 装饰器：自动记录 API 调用
def monitor_api_call(api_type: str):
    """装饰器：监控 API 调用"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                performance_monitor.record_api_call(api_type, duration)
                return result
            except Exception as e:
                performance_monitor.record_error(f"{api_type}_error")
                raise
        return wrapper
    return decorator


# 异步版本的装饰器
def monitor_async_api_call(api_type: str):
    """装饰器：监控异步 API 调用"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                performance_monitor.record_api_call(api_type, duration)
                return result
            except Exception as e:
                performance_monitor.record_error(f"{api_type}_error")
                raise
        return wrapper
    return decorator
