"""项目相关的数据模型"""

from datetime import datetime, timezone

from sqlalchemy import JSON, Column, DateTime, ForeignKey, Integer, String, Text, func
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db
from app.models.device_project import DeviceProject


class Project(BaseModel):
    """项目模型"""

    __tablename__ = "projects"

    # 状态常量
    STATUS_DISABLED = 0  # 禁用
    STATUS_ENABLED = 1  # 启用

    SHOW_NO  = 0  # 不显示
    SHOW_YES = 1  # 显示

    SHOW_DAY = [1, 3, 7, 14, 30] # 显示时间 1天，3天，7天，14天，30天

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    service_config_id = Column(
        Integer, ForeignKey("service_configs.id"), nullable=False
    )
    service_compose = Column(JSON, default={}, nullable=False)  # 项目自身的服务配置
    form_schema = Column(JSON, default={})
    status = Column(Integer, default=STATUS_ENABLED)
    deleted_at = Column(DateTime, nullable=True)
    special_login_hint = Column(String(240), nullable=True, comment='特殊登录提示')
    show_new_label = Column(Integer, default=SHOW_NO, comment='显示新增标签')
    show_day = Column(Integer, default=0, comment='显示时长')
    show_date_end = Column(DateTime, nullable=True, comment='新增标签显示时长结束')

    # 关联关系
    service_config = relationship("ServiceConfig", backref="projects")
    files = relationship(
        "ProjectFile", back_populates="project", cascade="all, delete-orphan"
    )
    device_projects = relationship(
        "DeviceProject", back_populates="project", cascade="all, delete-orphan"
    )
    asset_types = relationship(
        "AssetType", back_populates="project", cascade="all, delete-orphan"
    )

    __mapper_args__ = {"confirm_deleted_rows": False}

    @property
    def status_text(self):
        """获取状态文本"""
        return "disabled" if self.status == self.STATUS_DISABLED else "enabled"

    @property
    def special_login_hint_api(self):
        return self.special_login_hint or ""

    @property
    def show_new_label_api(self):
        return self.show_new_label or self.SHOW_NO

    @property
    def show_day_api(self):
        return self.show_day or 0

    @property
    def show_date_end_api(self):
        if not self.show_date_end :
            return ''
        return self.show_date_end.isoformat()

    @property
    def b_new_label(self):
        if self.show_new_label_api and self.show_date_end :
            end_time = self.show_date_end
            if end_time.tzinfo is None:
                end_time = end_time.replace(tzinfo=timezone.utc)
            if datetime.now(timezone.utc) < end_time :
                return True
        return False

    def soft_delete(self):
        """软删除项目"""
        self.deleted_at = datetime.utcnow()
        db.session.commit()

    def restore(self):
        """恢复已删除的项目"""
        self.deleted_at = None
        db.session.commit()

    @classmethod
    def not_deleted(cls):
        """获取未删除的项目查询"""
        return cls.query.filter_by(deleted_at=None)

    def to_dict(self, user=None):
        """转换为字典

        Args:
            user: 当前用户，如果提供则根据用户角色返回不同的字段

        Returns:
            dict: 项目信息字典
        """
        # 基础字段，所有用户都可见
        data = {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "service_config_id": self.service_config_id,
            "form_schema": self.form_schema,
            "status": self.status,
            "status_text": self.status_text,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "special_login_hint": self.special_login_hint_api,
            "show_new_label": self.show_new_label_api,
            "show_day": self.show_day_api,
            "show_date_end": self.show_date_end_api,
            "b_new_label": self.b_new_label,
        }

        # 只有管理员可见的字段
        if user and user.is_admin:
            data.update(
                {
                    "service_compose": self.service_compose,
                    "files": [f.to_dict() for f in self.files],
                }
            )

        return data
