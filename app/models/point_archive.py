"""积分归档相关模型"""

from datetime import datetime, date
from decimal import Decimal
from enum import Enum
from sqlalchemy import Index, UniqueConstraint
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db


class ArchiveType(str, Enum):
    """归档类型枚举"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"


class PointRecordArchive(BaseModel):
    """积分记录归档表"""
    __tablename__ = 'point_records_archive'
    __table_args__ = (
        # 复合唯一索引：用户+记录类型+归档类型+归档日期
        UniqueConstraint('user_id', 'record_type', 'archive_type', 'archive_date', 
                        name='uk_user_type_date'),
        # 查询优化索引
        Index('idx_user_type_date', 'user_id', 'archive_type', 'archive_date'),
        Index('idx_archive_date', 'archive_date'),
        Index('idx_user_record_type', 'user_id', 'record_type'),
        Index('idx_archive_type_date', 'archive_type', 'archive_date'),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    record_type = db.Column(db.String(20), nullable=False, comment='记录类型：task, invite, project')
    archive_type = db.Column(db.Enum(ArchiveType), nullable=False, comment='归档类型：daily, weekly, monthly')
    archive_date = db.Column(db.Date, nullable=False, comment='归档日期')
    total_points = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='总积分')
    record_count = db.Column(db.Integer, nullable=False, default=0, comment='记录数量')
    
    # 扩展字段，用于存储额外的统计信息
    extra_data = db.Column(db.JSON, comment='扩展数据')

    # 关系定义
    user = relationship('User', backref='point_archives')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'record_type': self.record_type,
            'archive_type': self.archive_type.value,
            'archive_date': self.archive_date.isoformat(),
            'total_points': float(self.total_points),
            'record_count': self.record_count,
            'extra_data': self.extra_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class PointStatisticsSnapshot(BaseModel):
    """积分统计快照表"""
    __tablename__ = 'point_statistics_snapshot'
    __table_args__ = (
        # 用户+日期唯一索引
        UniqueConstraint('user_id', 'snapshot_date', name='uk_user_date'),
        # 查询优化索引
        Index('idx_user_date', 'user_id', 'snapshot_date'),
        Index('idx_snapshot_date', 'snapshot_date'),
        Index('idx_total_points', 'total_points', postgresql_using='btree'),
        Index('idx_rank_position', 'rank_position'),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    snapshot_date = db.Column(db.Date, nullable=False, comment='快照日期')
    
    # 积分统计
    total_points = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='总积分')
    task_points = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='任务积分')
    invite_points = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='邀请积分')
    project_points = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='项目积分')
    
    # 变化统计
    daily_increase = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='日增长')
    weekly_increase = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='周增长')
    monthly_increase = db.Column(db.DECIMAL(18, 8), nullable=False, default=0, comment='月增长')
    
    # 排名信息
    rank_position = db.Column(db.Integer, comment='排名位置')
    rank_change = db.Column(db.Integer, default=0, comment='排名变化')

    # 关系定义
    user = relationship('User', backref='point_snapshots')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'snapshot_date': self.snapshot_date.isoformat(),
            'total_points': float(self.total_points),
            'task_points': float(self.task_points),
            'invite_points': float(self.invite_points),
            'project_points': float(self.project_points),
            'daily_increase': float(self.daily_increase),
            'weekly_increase': float(self.weekly_increase),
            'monthly_increase': float(self.monthly_increase),
            'rank_position': self.rank_position,
            'rank_change': self.rank_change,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class PointMigrationLog(BaseModel):
    """积分迁移日志表"""
    __tablename__ = 'point_migration_log'
    __table_args__ = (
        Index('idx_migration_date', 'migration_date'),
        Index('idx_status', 'status'),
        Index('idx_migration_type', 'migration_type'),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    migration_type = db.Column(db.String(50), nullable=False, comment='迁移类型')
    migration_date = db.Column(db.Date, nullable=False, comment='迁移日期')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    status = db.Column(db.String(20), nullable=False, default='running', comment='状态：running, success, failed')
    
    # 统计信息
    total_records = db.Column(db.Integer, default=0, comment='总记录数')
    processed_records = db.Column(db.Integer, default=0, comment='已处理记录数')
    success_records = db.Column(db.Integer, default=0, comment='成功记录数')
    failed_records = db.Column(db.Integer, default=0, comment='失败记录数')
    
    # 错误信息
    error_message = db.Column(db.Text, comment='错误信息')
    error_details = db.Column(db.JSON, comment='错误详情')
    
    # 配置信息
    config = db.Column(db.JSON, comment='迁移配置')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'migration_type': self.migration_type,
            'migration_date': self.migration_date.isoformat(),
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'total_records': self.total_records,
            'processed_records': self.processed_records,
            'success_records': self.success_records,
            'failed_records': self.failed_records,
            'error_message': self.error_message,
            'error_details': self.error_details,
            'config': self.config,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def update_progress(self, processed: int, success: int, failed: int, error_msg: str = None):
        """更新迁移进度"""
        self.processed_records = processed
        self.success_records = success
        self.failed_records = failed
        if error_msg:
            self.error_message = error_msg
        
        # 计算完成百分比
        if self.total_records > 0:
            progress = (self.processed_records / self.total_records) * 100
            if progress >= 100:
                self.status = 'success' if self.failed_records == 0 else 'partial_success'

    def mark_completed(self, status: str = 'success', error_msg: str = None):
        """标记迁移完成"""
        self.end_time = datetime.utcnow()
        self.status = status
        if error_msg:
            self.error_message = error_msg

    def mark_failed(self, error_msg: str, error_details: dict = None):
        """标记迁移失败"""
        self.end_time = datetime.utcnow()
        self.status = 'failed'
        self.error_message = error_msg
        if error_details:
            self.error_details = error_details


class PointArchiveConfig(BaseModel):
    """积分归档配置表"""
    __tablename__ = 'point_archive_config'
    __table_args__ = (
        UniqueConstraint('config_key', name='uk_config_key'),
        Index('idx_config_type', 'config_type'),
    )

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    config_key = db.Column(db.String(100), nullable=False, unique=True, comment='配置键')
    config_type = db.Column(db.String(50), nullable=False, comment='配置类型')
    config_value = db.Column(db.JSON, nullable=False, comment='配置值')
    description = db.Column(db.String(255), comment='配置描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'config_key': self.config_key,
            'config_type': self.config_type,
            'config_value': self.config_value,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
