from datetime import datetime
from app.models.base import db, BaseModel


class DeviceRuntime(BaseModel):
    """设备运行时数据模型"""
    __tablename__ = 'device_runtime'

    id = db.Column(db.Integer, primary_key=True)
    agent_version = db.Column(db.String(100))
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    started_at = db.Column(db.DateTime, nullable=False)
    running_time = db.Column(db.Integer, nullable=False)  # 单位：秒
    lan_ip = db.Column(db.String(15), nullable=True)
    public_ip = db.Column(db.String(15), nullable=True)
    network = db.Column(db.JSON, nullable=True)      # 网络信息，包含 download_speed 和 upload_speed
    disk = db.Column(db.JSO<PERSON>, nullable=True)         # 磁盘信息，包含 total、used 和 free
    cpu = db.Column(db.<PERSON><PERSON><PERSON>, nullable=True)          # CPU信息，包含 usage 和 cores
    memory = db.Column(db.JSON, nullable=True)       # 内存信息，包含 total、used 和 free
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    def __init__(self, device_id: int, started_at: datetime, running_time: int,
                 lan_ip: str = None, public_ip: str = None,
                 network: dict = None, disk: dict = None,
                 cpu: dict = None, memory: dict = None,
                 updated_at: datetime = None):
        self.device_id = device_id
        self.started_at = started_at
        self.running_time = running_time
        self.lan_ip = lan_ip
        self.public_ip = public_ip
        self.network = network
        self.disk = disk
        self.cpu = cpu
        self.memory = memory
        self.updated_at = updated_at or datetime.utcnow()

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'started_at': self.started_at.isoformat() + 'Z' if self.started_at else None,
            'running_time': self.running_time,
            'lan_ip': self.lan_ip,
            'public_ip': self.public_ip,
            'network': self.network,
            'disk': self.disk,
            'cpu': self.cpu,
            'memory': self.memory,
            'updated_at': self.updated_at.isoformat() + 'Z' if self.updated_at else None
        }
