from datetime import datetime, timezone, timedelta

from sqlalchemy import <PERSON>umn, Foreign<PERSON>ey, <PERSON>olean, DateTime
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db
from app.enums.biz_enums import NotifyPriority, NotifyStatus, NotifyPopup

from typing import List

class Notify(BaseModel):
    """通知模型类"""
    __tablename__ = 'notify'
    __table_args__ = (db.Index('idx_notify_type', 'notify_type'),)

    POPUP_MODE_LIST     = [NotifyPopup.POPUP_MODE_NOW, NotifyPopup.POPUP_MODE_APP, NotifyPopup.POPUP_MODE_REFRESH]

    id = Column(db.Integer, primary_key=True, autoincrement=True)
    notify_type = Column(db.String(50), nullable=False, comment='通知类型')
    title = Column(db.JSO<PERSON>, nullable=False, comment='通知标题')
    content = Column(db.<PERSON><PERSON><PERSON>, nullable=False, comment='通知内容')
    notify_status = Column(db.Integer, default=NotifyStatus.DRAFT, comment='通知状态')
    notify_priority = Column(db.Integer, default=NotifyPriority.NORMAL, comment='通知优先级')
    extend_field = Column(db.JSON, comment='扩展字段')
    is_deleted = Column(Boolean, default=False)
    creator = Column(db.Integer, comment='创建人ID')
    updator = Column(db.Integer, comment='更新人ID')
    popup_show = Column(db.Integer, default=NotifyPopup.POPUP_SHOW_NO, comment='显示弹窗')
    popup_mode = Column(db.Integer, default=NotifyPopup.POPUP_MODE_NONE, comment='弹窗方式')
    popup_date_end = Column(DateTime, nullable=True, comment='弹窗结束时间')
    # 建立与 UserNotify 的一对多关系
    user_notifies = relationship('UserNotify', back_populates='notify', cascade='all, delete-orphan')

    @property
    def popup_show_api(self):
        return self.popup_show or NotifyPopup.POPUP_SHOW_NO

    @property
    def popup_mode_api(self):
        return self.popup_mode or NotifyPopup.POPUP_MODE_NONE

    @property
    def popup_date_end_api(self):
        if not self.popup_date_end :
            return ''
        return (self.popup_date_end + timedelta(hours=8)).isoformat()

    @property
    def b_popup_show_list(self):
        if self.popup_show_api and self.popup_mode_api and self.popup_date_end_api :
            end_time = self.popup_date_end
            if end_time.tzinfo is None:
                end_time = end_time.replace(tzinfo=timezone.utc)
            if datetime.now(timezone.utc) < end_time :
                return [mode_val for mode_val in self.POPUP_MODE_LIST if (self.popup_mode_api & mode_val) == mode_val]
        return []

    def to_dict(self) -> dict:
        """将模型实例转换为字典"""
        user_ids = self.extend_field.get('user_ids') if self.extend_field else []
        return {
            'id': self.id,
            'notify_type': self.notify_type,
            'title': self.title,
            'content': self.content,
            'notify_status': self.notify_status,
            'notify_priority': self.notify_priority,
            'is_deleted': self.is_deleted,
            'creator': self.creator,
            'updator': self.updator,
            'created_at': self.created_at.isoformat() + 'Z',
            'updated_at': self.updated_at.isoformat() + 'Z',
            'user_ids': user_ids,
            'popup_show': self.popup_show_api,
            'popup_mode': self.popup_mode_api,
            'popup_date_end': self.popup_date_end_api,
        }

    def to_locale_dict(self, locale: str) -> dict:
        """将模型实例转换为字典"""

        locale_title = find_by_locale(self.title, locale)
        locale_content = find_by_locale(self.content, locale)

        user_ids = self.extend_field.get('user_ids') if self.extend_field else []
        return {
            'id': self.id,
            'notify_type': self.notify_type,
            'title': locale_title,
            'content': locale_content,
            'notify_status': self.notify_status,
            'notify_priority': self.notify_priority,
            'is_deleted': self.is_deleted,
            'creator': self.creator,
            'updator': self.updator,
            'created_at': self.created_at.isoformat() + 'Z' if self.created_at else None,
            'updated_at': self.updated_at.isoformat() + 'Z' if self.updated_at else None,
            'user_ids': user_ids,
            'popup_show': self.popup_show_api,
            'popup_mode': self.popup_mode_api,
            'popup_date_end': self.popup_date_end_api,
        }

    @staticmethod
    def get_popup_mode (popup_mode:List[int]) -> int:
        val = NotifyPopup.POPUP_MODE_NONE

        if popup_mode :
            for mode_val in popup_mode :
                val |=  mode_val
        return val


class UserNotify(BaseModel):
    """用户通知模型类"""
    __tablename__ = 'user_notify'
    __table_args__ = (db.Index('idx_user_id_notify_id', 'user_id', 'notify_id'),)

    id = Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = Column(db.Integer, ForeignKey('users.id'), nullable=False, comment='用户ID')
    notify_id = Column(db.Integer, ForeignKey('notify.id'), nullable=False, comment='通知ID')
    is_read = Column(db.Boolean, comment='阅读状态')
    read_timestamp = Column(db.DateTime, nullable=True, comment='阅读时间')

    # 建立与 Notify 的多对一关系
    notify = relationship('Notify', back_populates='user_notifies')
    # 建立与 User 的多对一关系
    user = relationship('User', back_populates='user_notifies')

    def to_dict(self) -> dict:
        """将模型实例转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'notify_id': self.notify_id,
            'is_read': self.is_read,
            'read_timestamp': self.read_timestamp.isoformat() if self.read_timestamp else None,
            'notify': self.notify.to_dict() if self.notify else None
        }

    def to_locale_dict(self, locale: str) -> dict:
        """将模型实例转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'notify_id': self.notify_id,
            'is_read': self.is_read,
            'read_timestamp': self.read_timestamp.isoformat() if self.read_timestamp else None,
            'notify': self.notify.to_locale_dict(locale) if self.notify else None
        }

    def to_user_response_dict(self, locale: str):
        locale_title = find_by_locale(self.notify.title, locale)
        locale_content = find_by_locale(self.notify.content, locale)
        return {
            'id': self.id,
            'title': locale_title,
            'content': locale_content,
            'is_read': self.is_read,
            'notify_id': self.notify_id,
            'notify_priority': self.notify.notify_priority,
            'notify_type': self.notify.notify_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_timestamp': self.read_timestamp.isoformat() if self.read_timestamp else None,
            'popup_show': self.notify.b_popup_show_list if not self.is_read else [],
        }


def find_by_locale(i18n_dict, locale: str):
    locale_str = i18n_dict.get(locale)
    if not locale_str:
        for i18n_key, i18n_value in i18n_dict.items():
            if i18n_value:
                return i18n_value
    return locale_str
