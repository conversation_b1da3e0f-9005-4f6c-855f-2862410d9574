"""积分优化相关的后台任务

这些任务负责：
1. 自动归档历史积分数据
2. 生成积分统计快照
3. 渐进式数据迁移
4. 性能监控和告警
"""

import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Dict, List, Optional

from celery import shared_task
from celery.utils.log import get_task_logger
from sqlalchemy import func, and_

from app.models.base import db
from app.models.points import PointRecord
from app.models.point_archive import (
    PointRecordArchive, PointStatisticsSnapshot, 
    PointMigrationLog, ArchiveType
)
from app.models.asset import UserAsset
from app.models.user import User
from app.services.point_optimization_config import PointOptimizationConfig
from app.services.ubi_points_integration_service import UbiPointsIntegrationService

logger = get_task_logger(__name__)


@shared_task(bind=True, max_retries=3)
def auto_archive_daily_points(self, target_date_str: str = None):
    """自动归档日积分数据
    
    Args:
        target_date_str: 目标日期字符串 (YYYY-MM-DD)，默认为昨天
    """
    try:
        # 检查是否启用自动归档
        if not PointOptimizationConfig.should_auto_archive():
            logger.info("自动归档未启用，跳过任务")
            return {"status": "skipped", "reason": "auto_archive_disabled"}
        
        # 解析目标日期
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today() - timedelta(days=1)  # 昨天
        
        logger.info(f"开始归档日积分数据: {target_date}")
        
        # 获取归档配置
        archive_config = PointOptimizationConfig.get_archive_config()
        batch_size = archive_config.get("batch_size", 1000)
        
        # 创建迁移日志
        migration_log = PointMigrationLog(
            migration_type='auto_archive_daily',
            migration_date=target_date,
            start_time=datetime.utcnow(),
            config={'batch_size': batch_size, 'target_date': target_date.isoformat()}
        )
        db.session.add(migration_log)
        db.session.commit()
        
        # 查询需要归档的数据
        records_query = db.session.query(
            PointRecord.user_id,
            PointRecord.record_type,
            func.sum(PointRecord.points).label('total_points'),
            func.count(PointRecord.id).label('record_count')
        ).filter(
            func.date(PointRecord.created_at) == target_date,
            PointRecord.archive_status == 'pending'
        ).group_by(PointRecord.user_id, PointRecord.record_type)
        
        total_groups = records_query.count()
        migration_log.total_records = total_groups
        db.session.commit()
        
        if total_groups == 0:
            logger.info(f"没有需要归档的数据: {target_date}")
            migration_log.mark_completed('success')
            db.session.commit()
            return {"status": "success", "archived_groups": 0}
        
        processed = 0
        success = 0
        failed = 0
        
        # 分批处理
        for offset in range(0, total_groups, batch_size):
            batch_records = records_query.offset(offset).limit(batch_size).all()
            
            for record_data in batch_records:
                try:
                    user_id = record_data.user_id
                    record_type = record_data.record_type
                    total_points = Decimal(str(record_data.total_points or 0))
                    record_count = record_data.record_count
                    
                    # 检查是否已存在归档记录
                    existing_archive = PointRecordArchive.query.filter_by(
                        user_id=user_id,
                        record_type=record_type,
                        archive_type=ArchiveType.DAILY,
                        archive_date=target_date
                    ).first()
                    
                    if existing_archive:
                        # 更新现有归档记录
                        existing_archive.total_points += total_points
                        existing_archive.record_count += record_count
                        existing_archive.updated_at = datetime.utcnow()
                    else:
                        # 创建新归档记录
                        new_archive = PointRecordArchive(
                            user_id=user_id,
                            record_type=record_type,
                            archive_type=ArchiveType.DAILY,
                            archive_date=target_date,
                            total_points=total_points,
                            record_count=record_count
                        )
                        db.session.add(new_archive)
                    
                    success += 1
                    
                except Exception as e:
                    failed += 1
                    logger.error(f"归档记录失败 - 用户 {record_data.user_id}, 类型 {record_data.record_type}: {e}")
                
                processed += 1
            
            # 批量提交
            try:
                db.session.commit()
                migration_log.update_progress(processed, success, failed)
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                logger.error(f"批量提交失败: {e}")
                raise
        
        # 更新原始记录状态
        if success > 0:
            updated_count = db.session.query(PointRecord).filter(
                func.date(PointRecord.created_at) == target_date,
                PointRecord.archive_status == 'pending'
            ).update({
                'archive_status': 'archived',
                'archived_at': datetime.utcnow()
            }, synchronize_session=False)
            
            db.session.commit()
            logger.info(f"更新了 {updated_count} 条记录的归档状态")
        
        # 完成归档
        migration_log.mark_completed('success' if failed == 0 else 'partial_success')
        db.session.commit()
        
        result = {
            "status": "success",
            "target_date": target_date.isoformat(),
            "total_groups": total_groups,
            "processed": processed,
            "success": success,
            "failed": failed
        }
        
        logger.info(f"日归档完成: {result}")
        return result
        
    except Exception as e:
        logger.error(f"自动归档日积分数据失败: {e}")
        if 'migration_log' in locals():
            migration_log.mark_failed(str(e))
            db.session.commit()
        
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务失败，将在60秒后重试 (第{self.request.retries + 1}次)")
            raise self.retry(countdown=60, exc=e)
        else:
            logger.error(f"任务重试次数已达上限，放弃执行")
            return {"status": "failed", "error": str(e)}


@shared_task(bind=True, max_retries=2)
def generate_daily_point_snapshots(self, target_date_str: str = None):
    """生成每日积分统计快照
    
    Args:
        target_date_str: 目标日期字符串 (YYYY-MM-DD)，默认为昨天
    """
    try:
        # 解析目标日期
        if target_date_str:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today() - timedelta(days=1)  # 昨天
        
        logger.info(f"开始生成积分统计快照: {target_date}")
        
        # 获取UBI积分资产类型
        try:
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()
        except Exception as e:
            logger.error(f"获取UBI积分资产类型失败: {e}")
            return {"status": "failed", "error": "asset_type_not_found"}
        
        # 获取所有有积分的用户
        users_with_points = db.session.query(
            UserAsset.user_id,
            UserAsset.available_balance.label('total_points')
        ).filter(
            UserAsset.asset_type_id == asset_type.id,
            UserAsset.available_balance > 0
        ).all()
        
        if not users_with_points:
            logger.info("没有用户有积分，跳过快照生成")
            return {"status": "success", "snapshots_created": 0}
        
        # 计算排名
        sorted_users = sorted(users_with_points, key=lambda x: x.total_points, reverse=True)
        
        snapshots_created = 0
        snapshots_updated = 0
        
        for rank, user_data in enumerate(sorted_users, 1):
            try:
                user_id = user_data.user_id
                total_points = user_data.total_points
                
                # 计算各类型积分（从归档数据或实时数据）
                type_points = _calculate_user_points_by_type(user_id, target_date)
                
                # 计算增长数据
                increases = _calculate_point_increases(user_id, target_date)
                
                # 获取前一天的排名
                prev_snapshot = PointStatisticsSnapshot.query.filter_by(
                    user_id=user_id,
                    snapshot_date=target_date - timedelta(days=1)
                ).first()
                
                prev_rank = prev_snapshot.rank_position if prev_snapshot else None
                rank_change = (prev_rank - rank) if prev_rank else 0
                
                # 检查是否已存在快照
                existing_snapshot = PointStatisticsSnapshot.query.filter_by(
                    user_id=user_id,
                    snapshot_date=target_date
                ).first()
                
                if existing_snapshot:
                    # 更新现有快照
                    existing_snapshot.total_points = total_points
                    existing_snapshot.task_points = type_points['task']
                    existing_snapshot.invite_points = type_points['invite']
                    existing_snapshot.project_points = type_points['project']
                    existing_snapshot.daily_increase = increases['daily']
                    existing_snapshot.weekly_increase = increases['weekly']
                    existing_snapshot.monthly_increase = increases['monthly']
                    existing_snapshot.rank_position = rank
                    existing_snapshot.rank_change = rank_change
                    existing_snapshot.updated_at = datetime.utcnow()
                    snapshots_updated += 1
                else:
                    # 创建新快照
                    new_snapshot = PointStatisticsSnapshot(
                        user_id=user_id,
                        snapshot_date=target_date,
                        total_points=total_points,
                        task_points=type_points['task'],
                        invite_points=type_points['invite'],
                        project_points=type_points['project'],
                        daily_increase=increases['daily'],
                        weekly_increase=increases['weekly'],
                        monthly_increase=increases['monthly'],
                        rank_position=rank,
                        rank_change=rank_change
                    )
                    db.session.add(new_snapshot)
                    snapshots_created += 1
                    
            except Exception as e:
                logger.error(f"处理用户 {user_data.user_id} 快照失败: {e}")
                continue
        
        db.session.commit()
        
        result = {
            "status": "success",
            "target_date": target_date.isoformat(),
            "total_users": len(sorted_users),
            "snapshots_created": snapshots_created,
            "snapshots_updated": snapshots_updated
        }
        
        logger.info(f"快照生成完成: {result}")
        return result
        
    except Exception as e:
        logger.error(f"生成积分统计快照失败: {e}")
        db.session.rollback()
        
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务失败，将在120秒后重试 (第{self.request.retries + 1}次)")
            raise self.retry(countdown=120, exc=e)
        else:
            return {"status": "failed", "error": str(e)}


def _calculate_user_points_by_type(user_id: int, target_date: date) -> Dict[str, Decimal]:
    """计算用户各类型积分"""
    result = {'task': Decimal('0'), 'invite': Decimal('0'), 'project': Decimal('0')}
    
    # 优先从归档数据计算
    archive_points = db.session.query(
        PointRecordArchive.record_type,
        func.sum(PointRecordArchive.total_points).label('total')
    ).filter(
        PointRecordArchive.user_id == user_id,
        PointRecordArchive.archive_date <= target_date
    ).group_by(PointRecordArchive.record_type).all()
    
    for record_type, total in archive_points:
        if record_type in result:
            result[record_type] = Decimal(str(total or 0))
    
    # 补充未归档的数据
    pending_points = db.session.query(
        PointRecord.record_type,
        func.sum(PointRecord.points).label('total')
    ).filter(
        PointRecord.user_id == user_id,
        func.date(PointRecord.created_at) <= target_date,
        PointRecord.archive_status == 'pending'
    ).group_by(PointRecord.record_type).all()
    
    for record_type, total in pending_points:
        if record_type in result:
            result[record_type] += Decimal(str(total or 0))
    
    return result


def _calculate_point_increases(user_id: int, target_date: date) -> Dict[str, Decimal]:
    """计算积分增长数据"""
    result = {'daily': Decimal('0'), 'weekly': Decimal('0'), 'monthly': Decimal('0')}
    
    # 计算日增长（从归档数据）
    daily_archive = PointRecordArchive.query.filter(
        PointRecordArchive.user_id == user_id,
        PointRecordArchive.archive_date == target_date,
        PointRecordArchive.archive_type == ArchiveType.DAILY
    ).all()
    
    for archive in daily_archive:
        result['daily'] += archive.total_points
    
    # 如果没有归档数据，从原始数据计算
    if result['daily'] == 0:
        daily_points = db.session.query(
            func.sum(PointRecord.points)
        ).filter(
            PointRecord.user_id == user_id,
            func.date(PointRecord.created_at) == target_date
        ).scalar() or 0
        result['daily'] = Decimal(str(daily_points))
    
    # 计算周增长和月增长（简化实现）
    week_start = target_date - timedelta(days=target_date.weekday())
    month_start = target_date.replace(day=1)
    
    # 这里可以进一步优化，使用归档数据计算
    result['weekly'] = result['daily']  # 简化实现
    result['monthly'] = result['daily']  # 简化实现
    
    return result


@shared_task(bind=True)
def cleanup_old_point_records(self, days_to_keep: int = 90):
    """清理旧的积分记录
    
    Args:
        days_to_keep: 保留天数，默认90天
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        logger.info(f"开始清理 {cutoff_date} 之前的已归档积分记录")
        
        # 统计要删除的记录数
        total_to_delete = PointRecord.query.filter(
            PointRecord.created_at < cutoff_date,
            PointRecord.archive_status == 'archived'
        ).count()
        
        if total_to_delete == 0:
            logger.info("没有需要清理的记录")
            return {"status": "success", "deleted_count": 0}
        
        batch_size = 10000
        deleted_count = 0
        
        # 分批删除
        while True:
            batch_records = PointRecord.query.filter(
                PointRecord.created_at < cutoff_date,
                PointRecord.archive_status == 'archived'
            ).limit(batch_size).all()
            
            if not batch_records:
                break
            
            for record in batch_records:
                db.session.delete(record)
            
            db.session.commit()
            deleted_count += len(batch_records)
            
            logger.info(f"已删除 {deleted_count}/{total_to_delete} 条旧记录")
        
        result = {
            "status": "success",
            "total_to_delete": total_to_delete,
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date.isoformat()
        }
        
        logger.info(f"清理完成: {result}")
        return result

    except Exception as e:
        logger.error(f"清理旧记录失败: {e}")
        db.session.rollback()
        return {"status": "failed", "error": str(e)}


@shared_task(bind=True, max_retries=3)
def gradual_migrate_user_points(self, batch_size: int = 1000, max_users: int = 5000):
    """渐进式迁移用户积分到资产系统

    Args:
        batch_size: 批处理大小
        max_users: 单次任务最大处理用户数
    """
    try:
        logger.info(f"开始渐进式迁移用户积分，批次大小: {batch_size}, 最大用户数: {max_users}")

        # 确保UBI积分资产类型存在
        try:
            asset_type = UbiPointsIntegrationService.ensure_ubi_asset_type_exists()
        except Exception as e:
            logger.error(f"确保UBI积分资产类型失败: {e}")
            return {"status": "failed", "error": "asset_type_creation_failed"}

        # 查找还没有迁移的用户（没有对应UserAsset记录的用户）
        users_without_assets = db.session.query(User.id).outerjoin(
            UserAsset, and_(
                User.id == UserAsset.user_id,
                UserAsset.asset_type_id == asset_type.id
            )
        ).filter(UserAsset.id.is_(None)).limit(max_users).all()

        if not users_without_assets:
            logger.info("所有用户积分已迁移完成")
            return {"status": "completed", "migrated_users": 0}

        user_ids = [user.id for user in users_without_assets]
        logger.info(f"找到 {len(user_ids)} 个用户需要迁移")

        # 创建迁移日志
        migration_log = PointMigrationLog(
            migration_type='gradual_migrate_user_points',
            migration_date=date.today(),
            start_time=datetime.utcnow(),
            total_records=len(user_ids),
            config={'batch_size': batch_size, 'max_users': max_users}
        )
        db.session.add(migration_log)
        db.session.commit()

        migrated_users = 0
        failed_users = 0
        errors = []

        # 分批处理用户
        for i in range(0, len(user_ids), batch_size):
            batch_user_ids = user_ids[i:i + batch_size]

            for user_id in batch_user_ids:
                try:
                    # 计算用户总积分
                    total_points = db.session.query(
                        func.sum(PointRecord.points)
                    ).filter(PointRecord.user_id == user_id).scalar() or 0

                    if total_points > 0:
                        # 创建用户资产记录
                        user_asset = UserAsset(
                            user_id=user_id,
                            asset_type_id=asset_type.id,
                            available_balance=Decimal(str(total_points)),
                            frozen_balance=Decimal('0'),
                            total_balance=Decimal(str(total_points))
                        )
                        db.session.add(user_asset)
                        migrated_users += 1

                        logger.debug(f"迁移用户 {user_id}: {total_points} 积分")

                except Exception as e:
                    failed_users += 1
                    error_msg = f"用户 {user_id}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"迁移用户积分失败: {error_msg}")

            # 批量提交
            try:
                db.session.commit()
                migration_log.update_progress(
                    processed=migrated_users + failed_users,
                    success=migrated_users,
                    failed=failed_users
                )
                db.session.commit()

                logger.info(f"已处理 {migrated_users + failed_users}/{len(user_ids)} 个用户")

            except Exception as e:
                db.session.rollback()
                logger.error(f"批量提交失败: {e}")
                raise

        # 完成迁移
        migration_log.mark_completed('success' if failed_users == 0 else 'partial_success')
        db.session.commit()

        result = {
            "status": "success",
            "total_users": len(user_ids),
            "migrated_users": migrated_users,
            "failed_users": failed_users,
            "errors": errors[:10]  # 只返回前10个错误
        }

        logger.info(f"渐进式迁移完成: {result}")

        # 如果还有用户需要迁移，安排下一次任务
        remaining_users = db.session.query(User.id).outerjoin(
            UserAsset, and_(
                User.id == UserAsset.user_id,
                UserAsset.asset_type_id == asset_type.id
            )
        ).filter(UserAsset.id.is_(None)).count()

        if remaining_users > 0:
            logger.info(f"还有 {remaining_users} 个用户需要迁移，安排下一次任务")
            # 延迟5分钟执行下一批
            gradual_migrate_user_points.apply_async(
                args=[batch_size, max_users],
                countdown=300
            )
            result["next_batch_scheduled"] = True
            result["remaining_users"] = remaining_users
        else:
            result["migration_completed"] = True

        return result

    except Exception as e:
        logger.error(f"渐进式迁移用户积分失败: {e}")
        if 'migration_log' in locals():
            migration_log.mark_failed(str(e))
            db.session.commit()

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务失败，将在300秒后重试 (第{self.request.retries + 1}次)")
            raise self.retry(countdown=300, exc=e)
        else:
            return {"status": "failed", "error": str(e)}


@shared_task(bind=True)
def gradual_archive_historical_data(self, days_per_batch: int = 7, max_days: int = 90):
    """渐进式归档历史数据

    Args:
        days_per_batch: 每批处理天数
        max_days: 最大处理天数
    """
    try:
        logger.info(f"开始渐进式归档历史数据，每批 {days_per_batch} 天，最大 {max_days} 天")

        # 找到最早的未归档数据日期
        earliest_pending = db.session.query(
            func.min(func.date(PointRecord.created_at))
        ).filter(PointRecord.archive_status == 'pending').scalar()

        if not earliest_pending:
            logger.info("没有需要归档的数据")
            return {"status": "completed", "archived_days": 0}

        # 计算归档范围
        end_date = date.today() - timedelta(days=1)  # 昨天
        start_date = max(earliest_pending, end_date - timedelta(days=max_days))

        if start_date > end_date:
            logger.info("没有需要归档的数据")
            return {"status": "completed", "archived_days": 0}

        archived_days = 0
        current_date = start_date

        # 按批次归档
        while current_date <= end_date and archived_days < max_days:
            batch_end = min(current_date + timedelta(days=days_per_batch - 1), end_date)

            logger.info(f"归档日期范围: {current_date} 到 {batch_end}")

            # 归档这个批次的数据
            batch_date = current_date
            while batch_date <= batch_end:
                try:
                    # 调用日归档任务
                    result = auto_archive_daily_points.apply(
                        args=[batch_date.isoformat()]
                    ).get(timeout=300)  # 5分钟超时

                    if result.get("status") == "success":
                        archived_days += 1
                        logger.info(f"成功归档 {batch_date}")
                    else:
                        logger.warning(f"归档 {batch_date} 失败: {result}")

                except Exception as e:
                    logger.error(f"归档 {batch_date} 异常: {e}")

                batch_date += timedelta(days=1)

            current_date = batch_end + timedelta(days=1)

            # 避免任务运行时间过长，分批执行
            if archived_days >= days_per_batch:
                break

        result = {
            "status": "success",
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "archived_days": archived_days
        }

        # 检查是否还有数据需要归档
        remaining_pending = db.session.query(
            func.count(PointRecord.id)
        ).filter(
            PointRecord.archive_status == 'pending',
            func.date(PointRecord.created_at) < end_date
        ).scalar()

        if remaining_pending > 0:
            logger.info(f"还有 {remaining_pending} 条记录需要归档，安排下一次任务")
            # 延迟10分钟执行下一批
            gradual_archive_historical_data.apply_async(
                args=[days_per_batch, max_days],
                countdown=600
            )
            result["next_batch_scheduled"] = True
            result["remaining_records"] = remaining_pending
        else:
            result["archive_completed"] = True

        logger.info(f"渐进式归档完成: {result}")
        return result

    except Exception as e:
        logger.error(f"渐进式归档历史数据失败: {e}")
        return {"status": "failed", "error": str(e)}
