from datetime import datetime, timezone, timedelta


from celery import shared_task
from celery.utils.log import get_task_logger

from app.enums.biz_enums import DeviceStatus, FixtureProjectName
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsDetail
from app.services.device_service import DeviceService
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.services.lighter_points_integration_service import LighterPointsIntegrationService

logger = get_task_logger(__name__)

@shared_task
def check_device_status():
    """
    Check device and project status based on metrics updates.
    
    Device status:
    - 0: initializing
    - 1: wait to configure (default)
    - 2: running
    - 3: failure (no metrics within threshold)
    - 4: offline (no metrics for 7+ days)
    
    Project status:
    - 'created': initial state, not configured
    - 'updated': configured but not yet running or stopped
    - 'running': active with recent metrics and status_code 200
    - 'stopped': no recent metrics or status_code not 200
    
    Note: Metrics are updated every 5 minutes, so thresholds are adjusted accordingly.
    """
    # Get current time and threshold
    now = datetime.now(timezone.utc)
    # Changed from 36 minutes to 7 minutes (5 minute interval + 2 minute grace period)
    threshold = now - timedelta(minutes=7)

    # 分批处理设备
    page = 0
    per_page = 100

    while True:
        devices = Device.query.order_by(Device.id).offset(page * per_page).limit(per_page).all()
        if not devices:
            break

        logger.info(f'Processing devices batch {page + 1}, size {len(devices)}')

        for device in devices:
            # Get all projects for this device
            device_projects = DeviceProject.query.filter_by(device_id=device.id).all()

            # Get latest metrics for this device
            latest_metrics = ServiceMetricsDetail.query.filter_by(
                device_id=device.id
            ).order_by(ServiceMetricsDetail.updated_at.desc()).all()

            has_running_project = False
            has_updated_project = False
            all_projects_created = True
            all_project_non_recent_metrics = False


            for project in device_projects:
                if project.project is None:
                    continue

                # Track if all projects are in 'created' state
                if project.state != 'created':
                    all_projects_created = False

                # Skip projects in 'created' state
                if project.state == 'created':
                    continue

                # Track if any project is in 'updated' state
                if project.state == 'updated':
                    has_updated_project = True
                    threshold = now - timedelta(minutes=7)
                else:
                    threshold = now - timedelta(minutes=36)

                # Track if any project is in 'running' state
                if project.state == 'running':
                    has_running_project = True

                # Find metrics for this project's services
                project_metrics = [
                    m for m in latest_metrics
                    if m.service_name.startswith(project.project.name)
                ]

                # 如果项目没有metrics，并且更新时间在 50 分钟内，则跳过
                if len(project_metrics) == 0 and project.updated_at.replace(tzinfo=timezone.utc) > now - timedelta(minutes=50):
                    all_project_non_recent_metrics = True
                    continue

                # Check if project has recent metrics and valid status code
                has_recent_metrics = False
                has_valid_status = False
                if project_metrics:
                    has_recent_metrics = any(
                        metric.updated_at.replace(tzinfo=timezone.utc) >= threshold
                        for metric in project_metrics
                    )
                    # Check if any recent metric has status_code 200
                    has_valid_status = any(
                        metric.status_code == 200 or metric.status_code is None or metric.status_code == ''
                        for metric in project_metrics
                        if metric.updated_at.replace(tzinfo=timezone.utc) >= threshold
                    )

                # Update project state based on metrics and status code
                if has_recent_metrics and has_valid_status:
                    # Project is running
                    if project.state != 'running':
                        project.state = 'running'
                        project.continuous_running_checks = 1  # Reset counter when state changes to running
                        db.session.add(project)
                    else:
                        # Project was already running, increment the counter
                        project.continuous_running_checks += 1
                        db.session.add(project)

                        # Check if the project has been running for 1 hour
                        # Changed to 12 checks at 5-minute intervals
                        if project.continuous_running_checks >= 12 and project.continuous_running_checks % 12 == 0:
                            # Award points to the device owner if there is one
                            if device.owner_id:
                                points_earned = 0.01
                                success, error = UbiPointsIntegrationService.add_ubi_points(
                                    user_id=device.owner_id,
                                    points=points_earned,
                                    record_type='project',
                                    related_id=project.id
                                )
                                if not success:
                                    print(f"Failed to award points: {error}")

                        # Special handling for Lighter project - award points every 24 hours (288 checks at 5-minute intervals)
                        if project.project.name == FixtureProjectName.LIGHTER_PROJECT_NAME:
                            if project.continuous_running_checks >= 288 and project.continuous_running_checks % 288 == 0:
                                if device.owner_id:
                                    lighter_points_earned = 0.01
                                    success, error = LighterPointsIntegrationService.add_lighter_points(
                                        user_id=device.owner_id,
                                        points=lighter_points_earned,
                                        record_type='lighter_device',
                                        related_id=device.id
                                    )
                                    if not success:
                                        logger.error(f"Failed to award Lighter points for device {device.id}: {error}")
                                    else:
                                        logger.info(f"Awarded {lighter_points_earned} Lighter points to user {device.owner_id} for device {device.id}")

                else:
                    # No recent metrics or invalid status code - mark as stopped regardless of current state
                    if project.state in ['running', 'updated']:
                        project.state = 'stopped'
                        project.continuous_running_checks = 0  # Reset counter when state changes to stopped
                        db.session.add(project)

            device_status = None
            if all_projects_created:
                device_status = DeviceStatus.WAIT_TO_CONFIGURE.code
            elif not all_project_non_recent_metrics:
                device_status = DeviceService.check_device_offline_status(device.id)

            # Update device status if needed
            if device_status and device.status != device_status:
                device.status = device_status
                db.session.add(device)
    
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error(f'Error processing batch {page + 1}: {str(e)}')
            raise

        # 移动到下一批
        page += 1