from datetime import datetime, timezone, timedelta

from celery import shared_task
from celery.utils.log import get_task_logger

from app.enums.biz_enums import DeviceStatus, FixtureProjectName
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsDetail, ServiceMetrics
from app.services.device_service import DeviceService
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.services.asset_service import AssetService
from app.models.blockchain import AssetTypeEnum, AssetType
from decimal import Decimal
from app.models.project import Project

logger = get_task_logger(__name__)


def handle_light_project(device: Device):
    # 查询 Lighter 项目
    lighter_project_obj = Project.query.filter_by(name=FixtureProjectName.LIGHTER_PROJECT_NAME).first()

    if lighter_project_obj:
        # 查询该设备是否有关联的 Lighter device_project
        lighter_device_project = DeviceProject.query.filter_by(
            device_id=device.id,
            project_id=lighter_project_obj.id
        ).first()

        if not lighter_device_project:
            lighter_device_project = DeviceProject()
            lighter_device_project.device_id = device.id
            lighter_device_project.project_id = lighter_project_obj.id
            lighter_device_project.state = 'running'
            db.session.add(lighter_device_project)
            db.session.flush()  # 确保获得ID，但不提交事务

        if device.status == DeviceStatus.RUNNING.code:
            # 执行 Lighter 积分处理
            handle_light_device_project(lighter_device_project, device)
        elif device.status in [DeviceStatus.FAILURE.code, DeviceStatus.OFFLINE.code]:
            lighter_device_project.state = 'stopped'


def handle_light_device_project(lighter_device_project, device: Device):
    """处理 Lighter 项目"""
    try:
        if not lighter_device_project:
            logger.warning(f"Lighter 项目不存在")
            return

        # 获取 Lighter 项目的积分资产类型
        asset_type = AssetType.query.filter_by(
            project_id=lighter_device_project.project_id,
            type=AssetTypeEnum.POINTS
        ).first()

        if not asset_type:
            logger.warning(f"Lighter 项目的积分资产类型不存在")
            return

        # 计算运行时长增量（每次检查代表5分钟 = 300秒）
        running_time_increment = 300  # 5分钟 = 300秒

        # 初始化积分增量
        points_increment = 0

        # Special handling for Lighter project - award points every 24 hours (288 checks at 5-minute intervals)
        lighter_device_project.continuous_running_checks += 1

        # 检查是否达到24小时（288次检查）
        check_round = 288
        if lighter_device_project.continuous_running_checks >= check_round and lighter_device_project.continuous_running_checks % check_round == 0:
            lighter_points_earned = Decimal('0.01')
            lighter_asset_type_id = asset_type.id

            form_schema = lighter_device_project.project.form_schema
            decimals = form_schema.get('basic', {}).get('decimals', 2)
            # 设置积分增量（转换为整数，假设精度为2位小数）
            points_increment = int(lighter_points_earned * 10 ** decimals)  # 0.01 -> 1

            # 直接使用 AssetService 增加用户资产
            AssetService.add_or_create_user_asset(
                user_id=lighter_device_project.device.owner_id,
                asset_type_id=lighter_asset_type_id,
                amount=lighter_points_earned,
                remark=f"Lighter设备运行24小时奖励 - 设备ID: {lighter_device_project.device.id}"
            )
            logger.info(
                f"Awarded {lighter_points_earned} Lighter points to user {lighter_device_project.device.owner_id} for device {lighter_device_project.device.id}")

        # 更新或创建 ServiceMetrics 记录
        _update_lighter_service_metrics(
            device_id=lighter_device_project.device_id,
            project_id=lighter_device_project.project_id,
            points_increment=points_increment,
            running_time_increment=running_time_increment
        )

    except Exception as e:
        logger.error(f"处理 Lighter 项目失败: {e}")
        return


def _update_lighter_service_metrics(device_id: int, project_id: int, points_increment: int,
                                    running_time_increment: int):
    """更新或创建 Lighter 项目的 ServiceMetrics 记录

    Args:
        device_id: 设备ID
        project_id: 项目ID
        points_increment: 积分增量
        running_time_increment: 运行时长增量（秒）
    """
    try:
        # 查询现有的 ServiceMetrics 记录
        service_metrics = ServiceMetrics.query.filter_by(
            device_id=device_id,
            project_id=project_id,
            service_name=FixtureProjectName.LIGHTER_PROJECT_NAME
        ).first()

        current_time = datetime.now(timezone.utc)

        if service_metrics:
            # 如果记录存在，累加积分和运行时长
            service_metrics.points += points_increment
            service_metrics.running_time += running_time_increment
            service_metrics.updated_at = current_time

            logger.info(
                f"更新 Lighter ServiceMetrics: device_id={device_id}, "
                f"总积分={service_metrics.points}, 总运行时长={service_metrics.running_time}秒, "
                f"本次积分增量={points_increment}, 本次时长增量={running_time_increment}秒"
            )
        else:
            # 如果记录不存在，创建新记录
            service_metrics = ServiceMetrics(
                device_id=device_id,
                project_id=project_id,
                service_name=FixtureProjectName.LIGHTER_PROJECT_NAME,
                points=points_increment,
                running_time=running_time_increment,
                updated_at=current_time
            )
            db.session.add(service_metrics)

            logger.info(
                f"创建 Lighter ServiceMetrics: device_id={device_id}, "
                f"初始积分={points_increment}, 初始运行时长={running_time_increment}秒"
            )

        # 注意：这里不提交事务，由调用方统一提交

    except Exception as e:
        logger.error(f"更新 Lighter ServiceMetrics 失败: device_id={device_id}, error={e}")
        raise


@shared_task
def check_device_status():
    """
    Check device and project status based on metrics updates.
    
    Device status:
    - 0: initializing
    - 1: wait to configure (default)
    - 2: running
    - 3: failure (no metrics within threshold)
    - 4: offline (no metrics for 7+ days)
    
    Project status:
    - 'created': initial state, not configured
    - 'updated': configured but not yet running or stopped
    - 'running': active with recent metrics and status_code 200
    - 'stopped': no recent metrics or status_code not 200
    
    Note: Metrics are updated every 5 minutes, so thresholds are adjusted accordingly.
    """
    # Get current time and threshold
    now = datetime.now(timezone.utc)
    # Changed from 36 minutes to 7 minutes (5 minute interval + 2 minute grace period)
    threshold = now - timedelta(minutes=7)

    # 分批处理设备
    page = 0
    per_page = 100

    while True:
        devices = Device.query.order_by(Device.id).offset(page * per_page).limit(per_page).all()
        if not devices:
            break

        logger.info(f'Processing devices batch {page + 1}, size {len(devices)}')

        for device in devices:
            # Get all projects for this device
            device_projects = DeviceProject.query.filter_by(device_id=device.id).all()

            # Get latest metrics for this device
            latest_metrics = ServiceMetricsDetail.query.filter_by(
                device_id=device.id
            ).order_by(ServiceMetricsDetail.updated_at.desc()).all()

            has_running_project = False
            has_updated_project = False
            all_projects_created = True
            all_project_non_recent_metrics = False

            for project in device_projects:
                if project.project is None:
                    continue

                # 跳过特殊项目的处理，这些项目在后面单独处理
                if project.project.name in [FixtureProjectName.UBI_PROJECT_NAME,
                                            FixtureProjectName.LIGHTER_PROJECT_NAME]:
                    continue

                # Track if all projects are in 'created' state
                if project.state != 'created':
                    all_projects_created = False

                # Skip projects in 'created' state
                if project.state == 'created':
                    continue

                # Track if any project is in 'updated' state
                if project.state == 'updated':
                    has_updated_project = True
                    threshold = now - timedelta(minutes=7)
                else:
                    threshold = now - timedelta(minutes=36)

                # Track if any project is in 'running' state
                if project.state == 'running':
                    has_running_project = True

                # Find metrics for this project's services
                project_metrics = [
                    m for m in latest_metrics
                    if m.service_name.startswith(project.project.name)
                ]

                # 如果项目没有metrics，并且更新时间在 50 分钟内，则跳过
                if len(project_metrics) == 0 and project.updated_at.replace(tzinfo=timezone.utc) > now - timedelta(
                        minutes=50):
                    all_project_non_recent_metrics = True
                    continue

                # Check if project has recent metrics and valid status code
                has_recent_metrics = False
                has_valid_status = False
                if project_metrics:
                    has_recent_metrics = any(
                        metric.updated_at.replace(tzinfo=timezone.utc) >= threshold
                        for metric in project_metrics
                    )
                    # Check if any recent metric has status_code 200
                    has_valid_status = any(
                        metric.status_code == 200 or metric.status_code is None or metric.status_code == ''
                        for metric in project_metrics
                        if metric.updated_at.replace(tzinfo=timezone.utc) >= threshold
                    )

                # Update project state based on metrics and status code
                if has_recent_metrics and has_valid_status:
                    # Project is running
                    if project.state != 'running':
                        project.state = 'running'
                        project.continuous_running_checks = 1  # Reset counter when state changes to running
                        db.session.add(project)
                    else:
                        # Project was already running, increment the counter
                        project.continuous_running_checks += 1
                        db.session.add(project)

                        # Check if the project has been running for 1 hour
                        # Changed to 12 checks at 5-minute intervals
                        if project.continuous_running_checks >= 12 and project.continuous_running_checks % 12 == 0:
                            # Award points to the device owner if there is one
                            if device.owner_id:
                                points_earned = 0.01
                                success, error = UbiPointsIntegrationService.add_ubi_points(
                                    user_id=device.owner_id,
                                    points=points_earned,
                                    record_type='project',
                                    related_id=project.id
                                )
                                if not success:
                                    print(f"Failed to award points: {error}")

                else:
                    # No recent metrics or invalid status code - mark as stopped regardless of current state
                    if project.state in ['running', 'updated']:
                        project.state = 'stopped'
                        project.continuous_running_checks = 0  # Reset counter when state changes to stopped
                        db.session.add(project)

            device_status = None
            if all_projects_created:
                device_status = DeviceStatus.WAIT_TO_CONFIGURE.code
            elif not all_project_non_recent_metrics:
                device_status = DeviceService.check_device_offline_status(device.id)

            # Update device status if needed
            if device_status and device.status != device_status:
                device.status = device_status
                db.session.add(device)

            # 处理 Lighter 项目积分逻辑（在设备状态为 running 时）
            handle_light_project(device)

        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error(f'Error processing batch {page + 1}: {str(e)}')
            raise

        # 移动到下一批
        page += 1
