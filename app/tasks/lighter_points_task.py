"""Lighter项目积分任务"""

from datetime import datetime, timezone, timedelta
from celery import shared_task
from celery.utils.log import get_task_logger

from app.enums.biz_enums import FixtureProjectName
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.services.lighter_points_integration_service import LighterPointsIntegrationService

logger = get_task_logger(__name__)


@shared_task
def award_lighter_device_points():
    """
    为Lighter项目的设备分配积分
    
    规则：每个设备累计运行24小时（不需要连续）增加0.01积分
    
    实现逻辑：
    1. 查找所有Lighter项目的设备项目关联
    2. 检查设备的累计运行时间
    3. 根据累计运行时间计算应该获得的积分
    4. 为设备所有者分配积分
    """
    try:
        # 获取Lighter项目
        lighter_project = Project.query.filter_by(name=FixtureProjectName.LIGHTER_PROJECT_NAME).first()
        if not lighter_project:
            logger.warning(f"Lighter项目不存在，跳过积分分配")
            return {"success": False, "message": "Lighter项目不存在"}
        
        # 查找所有Lighter项目的设备项目关联
        lighter_device_projects = DeviceProject.query.filter_by(
            project_id=lighter_project.id,
            state='running'  # 只处理运行中的项目
        ).all()
        
        if not lighter_device_projects:
            logger.info("没有找到运行中的Lighter项目设备")
            return {"success": True, "message": "没有运行中的Lighter项目设备"}
        
        points_awarded_count = 0
        total_points_awarded = 0.0
        
        for device_project in lighter_device_projects:
            device = device_project.device
            
            # 检查设备是否有所有者
            if not device.owner_id:
                logger.warning(f"设备 {device.id} 没有所有者，跳过积分分配")
                continue
            
            # 计算设备应该获得的积分
            # 每288个检查周期（24小时 * 60分钟 / 5分钟间隔 = 288）获得0.01积分
            if device_project.continuous_running_checks >= 288:
                # 计算应该获得的积分次数
                points_cycles = device_project.continuous_running_checks // 288
                
                # 检查是否有新的积分周期完成
                if device_project.continuous_running_checks % 288 == 0:
                    # 分配积分
                    points_earned = 0.01
                    success, error = LighterPointsIntegrationService.add_lighter_points(
                        user_id=device.owner_id,
                        points=points_earned,
                        record_type='lighter_device',
                        related_id=device.id
                    )
                    
                    if success:
                        points_awarded_count += 1
                        total_points_awarded += points_earned
                        logger.info(f"为用户 {device.owner_id} 的设备 {device.id} 分配了 {points_earned} Lighter积分")
                    else:
                        logger.error(f"为设备 {device.id} 分配Lighter积分失败: {error}")
        
        return {
            "success": True,
            "message": f"成功为 {points_awarded_count} 个设备分配了总计 {total_points_awarded} Lighter积分"
        }
        
    except Exception as e:
        logger.error(f"Lighter积分分配任务执行失败: {str(e)}")
        return {"success": False, "message": f"任务执行失败: {str(e)}"}


@shared_task
def check_lighter_device_runtime():
    """
    检查Lighter项目设备的运行时间
    
    这个任务可以用于统计和监控Lighter项目设备的运行情况
    """
    try:
        # 获取Lighter项目
        lighter_project = Project.query.filter_by(name=FixtureProjectName.LIGHTER_PROJECT_NAME).first()
        if not lighter_project:
            logger.warning(f"Lighter项目不存在")
            return {"success": False, "message": "Lighter项目不存在"}
        
        # 查找所有Lighter项目的设备项目关联
        lighter_device_projects = DeviceProject.query.filter_by(
            project_id=lighter_project.id
        ).all()
        
        runtime_stats = {
            "total_devices": len(lighter_device_projects),
            "running_devices": 0,
            "devices_with_24h_runtime": 0,
            "total_runtime_hours": 0
        }
        
        for device_project in lighter_device_projects:
            if device_project.state == 'running':
                runtime_stats["running_devices"] += 1
            
            # 计算运行时间（以小时为单位）
            runtime_hours = (device_project.continuous_running_checks * 5) / 60  # 5分钟间隔转换为小时
            runtime_stats["total_runtime_hours"] += runtime_hours
            
            # 检查是否达到24小时运行时间
            if runtime_hours >= 24:
                runtime_stats["devices_with_24h_runtime"] += 1
        
        logger.info(f"Lighter项目运行时间统计: {runtime_stats}")
        return {"success": True, "stats": runtime_stats}
        
    except Exception as e:
        logger.error(f"Lighter设备运行时间检查失败: {str(e)}")
        return {"success": False, "message": f"检查失败: {str(e)}"}


@shared_task
def cleanup_lighter_points_records():
    """
    清理过期的Lighter积分记录（可选任务）
    
    这个任务可以用于清理过期的积分记录，保持数据库的整洁
    """
    try:
        # 这里可以添加清理逻辑，比如删除超过一定时间的积分记录
        # 目前只是一个占位符
        logger.info("Lighter积分记录清理任务执行完成")
        return {"success": True, "message": "清理任务完成"}
        
    except Exception as e:
        logger.error(f"Lighter积分记录清理失败: {str(e)}")
        return {"success": False, "message": f"清理失败: {str(e)}"}
