from enum import Enum, auto, IntEnum, StrEnum


class DeviceStatus(Enum):
    # 0: initializing
    INITIALIZING = (0, "initializing")

    # 1: wait to configure (default)
    WAIT_TO_CONFIGURE = (1, "wait to configure")

    # 2: running
    RUNNING = (2, "running")

    # 3: failure
    FAILURE = (3, "failure")

    # 4: offline
    OFFLINE = (4, "offline")

    def __init__(self, code, desc):
        self.code = code
        self.desc = desc

class ProxyScope(Enum):

    PROJECT_PROXY = "Project proxy"

    DEVICE_PROXY = "Device proxy"


class NotifyType(str, Enum):
    """通知类型枚举"""
    ANNOUNCEMENT = "announcement"  # 即时公告
    ACTIVITY = "activity"  # 活动公告
    UPDATE = "update"  # 版本公告
    AIRDROP = "airdrop"  # 空投
    TRADE = "trade"  # 交易
    SECURITY = "security"  # 安全
    SYSTEM = "system" # 系统通知


class NotifyStatus(IntEnum):
    """通知状态枚举"""
    DRAFT = 0  # 草稿
    PUBLISHED = 1  # 已发布
    RETRACT = 2  # 撤回


class NotifyPriority(IntEnum):
    """通知优先级枚举"""
    LOW = 0  # 低优先级 - 普通通知
    NORMAL = 1  # 普通优先级 - 一般通知
    HIGH = 2  # 高优先级 - 重要通知

class NotifyPopup(IntEnum):
    """通知弹窗枚举"""
    POPUP_SHOW_NO       = 0  # 不显示
    POPUP_SHOW_YES      = 1  # 显示

    # 弹窗方式
    POPUP_MODE_NONE     = 0 # 空
    POPUP_MODE_NOW      = 1 # 即时
    POPUP_MODE_APP      = 2 # 打开APP
    POPUP_MODE_REFRESH  = 4 # 刷新页面

class CustodyType(StrEnum):

    SELF_CUSTODY = "self custody",

    UBI_CUSTODY = "ubi custody",

class FixtureProjectName(str, Enum):

    UBI_PROJECT_NAME = "UBI"

    LIGHTER_PROJECT_NAME = 'light'
