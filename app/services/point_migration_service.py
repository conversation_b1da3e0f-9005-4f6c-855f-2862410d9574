"""积分迁移服务"""

import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from sqlalchemy import func, and_, or_
from sqlalchemy.exc import IntegrityError

from app.models.base import db
from app.models.points import PointRecord
from app.models.point_archive import (
    PointRecordArchive, PointStatisticsSnapshot, 
    PointMigrationLog, PointArchiveConfig, ArchiveType
)
from app.models.asset import UserAsset
from app.models.blockchain import AssetType, AssetTypeEnum
from app.models.user import User
from app.services.asset_service import AssetService
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.utils.errors import BusinessException

logger = logging.getLogger(__name__)


class PointMigrationService:
    """积分迁移服务"""

    @staticmethod
    def migrate_user_points_to_assets(batch_size: int = 5000, dry_run: bool = False) -> Dict:
        """将用户积分迁移到资产系统
        
        Args:
            batch_size: 批处理大小
            dry_run: 是否为试运行模式
            
        Returns:
            Dict: 迁移结果统计
        """
        migration_log = PointMigrationLog(
            migration_type='user_points_to_assets',
            migration_date=date.today(),
            start_time=datetime.utcnow(),
            config={'batch_size': batch_size, 'dry_run': dry_run}
        )
        
        try:
            # 确保UBI积分资产类型存在
            asset_type = UbiPointsIntegrationService.ensure_ubi_asset_type_exists()
            
            # 获取所有用户的积分统计
            user_points_query = db.session.query(
                PointRecord.user_id,
                func.sum(PointRecord.points).label('total_points'),
                func.count(PointRecord.id).label('record_count')
            ).group_by(PointRecord.user_id)
            
            total_users = user_points_query.count()
            migration_log.total_records = total_users
            
            if not dry_run:
                db.session.add(migration_log)
                db.session.commit()
            
            processed = 0
            success = 0
            failed = 0
            errors = []
            
            # 分批处理用户
            for offset in range(0, total_users, batch_size):
                batch_users = user_points_query.offset(offset).limit(batch_size).all()
                
                for user_data in batch_users:
                    try:
                        user_id = user_data.user_id
                        total_points = Decimal(str(user_data.total_points or 0))
                        
                        if not dry_run:
                            # 检查用户是否已有资产记录
                            existing_asset = UserAsset.query.filter_by(
                                user_id=user_id,
                                asset_type_id=asset_type.id
                            ).first()
                            
                            if existing_asset:
                                # 更新现有资产
                                existing_asset.available_balance = total_points
                                existing_asset.updated_at = datetime.utcnow()
                            else:
                                # 创建新资产记录
                                new_asset = UserAsset(
                                    user_id=user_id,
                                    asset_type_id=asset_type.id,
                                    available_balance=total_points,
                                    frozen_balance=Decimal('0'),
                                    total_balance=total_points
                                )
                                db.session.add(new_asset)
                        
                        success += 1
                        
                    except Exception as e:
                        failed += 1
                        error_msg = f"User {user_data.user_id}: {str(e)}"
                        errors.append(error_msg)
                        logger.error(f"迁移用户积分失败: {error_msg}")
                    
                    processed += 1
                
                # 批量提交
                if not dry_run:
                    try:
                        db.session.commit()
                        migration_log.update_progress(processed, success, failed)
                        db.session.commit()
                    except Exception as e:
                        db.session.rollback()
                        logger.error(f"批量提交失败: {str(e)}")
                        raise
            
            # 完成迁移
            result = {
                'total_users': total_users,
                'processed': processed,
                'success': success,
                'failed': failed,
                'errors': errors[:10],  # 只返回前10个错误
                'dry_run': dry_run
            }
            
            if not dry_run:
                migration_log.mark_completed('success' if failed == 0 else 'partial_success')
                db.session.commit()
            
            return result
            
        except Exception as e:
            if not dry_run:
                migration_log.mark_failed(str(e))
                db.session.commit()
            raise BusinessException(f"积分迁移失败: {str(e)}")

    @staticmethod
    def generate_point_statistics_snapshot(target_date: date = None) -> Dict:
        """生成积分统计快照
        
        Args:
            target_date: 目标日期，默认为昨天
            
        Returns:
            Dict: 生成结果
        """
        if target_date is None:
            target_date = date.today() - timedelta(days=1)
        
        try:
            # 获取UBI积分资产类型
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()
            
            # 获取所有有积分的用户
            users_with_points = db.session.query(
                UserAsset.user_id,
                UserAsset.available_balance.label('total_points')
            ).filter(
                UserAsset.asset_type_id == asset_type.id,
                UserAsset.available_balance > 0
            ).all()
            
            # 计算排名
            sorted_users = sorted(users_with_points, key=lambda x: x.total_points, reverse=True)
            
            snapshots_created = 0
            snapshots_updated = 0
            
            for rank, user_data in enumerate(sorted_users, 1):
                user_id = user_data.user_id
                total_points = user_data.total_points
                
                # 计算各类型积分
                type_points = PointMigrationService._calculate_user_points_by_type(
                    user_id, target_date
                )
                
                # 计算增长数据
                increases = PointMigrationService._calculate_point_increases(
                    user_id, target_date
                )
                
                # 获取前一天的排名
                prev_snapshot = PointStatisticsSnapshot.query.filter_by(
                    user_id=user_id,
                    snapshot_date=target_date - timedelta(days=1)
                ).first()
                
                prev_rank = prev_snapshot.rank_position if prev_snapshot else None
                rank_change = (prev_rank - rank) if prev_rank else 0
                
                # 检查是否已存在快照
                existing_snapshot = PointStatisticsSnapshot.query.filter_by(
                    user_id=user_id,
                    snapshot_date=target_date
                ).first()
                
                if existing_snapshot:
                    # 更新现有快照
                    existing_snapshot.total_points = total_points
                    existing_snapshot.task_points = type_points['task']
                    existing_snapshot.invite_points = type_points['invite']
                    existing_snapshot.project_points = type_points['project']
                    existing_snapshot.daily_increase = increases['daily']
                    existing_snapshot.weekly_increase = increases['weekly']
                    existing_snapshot.monthly_increase = increases['monthly']
                    existing_snapshot.rank_position = rank
                    existing_snapshot.rank_change = rank_change
                    existing_snapshot.updated_at = datetime.utcnow()
                    snapshots_updated += 1
                else:
                    # 创建新快照
                    new_snapshot = PointStatisticsSnapshot(
                        user_id=user_id,
                        snapshot_date=target_date,
                        total_points=total_points,
                        task_points=type_points['task'],
                        invite_points=type_points['invite'],
                        project_points=type_points['project'],
                        daily_increase=increases['daily'],
                        weekly_increase=increases['weekly'],
                        monthly_increase=increases['monthly'],
                        rank_position=rank,
                        rank_change=rank_change
                    )
                    db.session.add(new_snapshot)
                    snapshots_created += 1
            
            db.session.commit()
            
            return {
                'target_date': target_date.isoformat(),
                'total_users': len(sorted_users),
                'snapshots_created': snapshots_created,
                'snapshots_updated': snapshots_updated
            }
            
        except Exception as e:
            db.session.rollback()
            raise BusinessException(f"生成积分快照失败: {str(e)}")

    @staticmethod
    def _calculate_user_points_by_type(user_id: int, target_date: date) -> Dict[str, Decimal]:
        """计算用户各类型积分"""
        result = {'task': Decimal('0'), 'invite': Decimal('0'), 'project': Decimal('0')}
        
        # 从PointRecord计算（用于历史数据）
        type_points = db.session.query(
            PointRecord.record_type,
            func.sum(PointRecord.points).label('total')
        ).filter(
            PointRecord.user_id == user_id,
            func.date(PointRecord.created_at) <= target_date
        ).group_by(PointRecord.record_type).all()
        
        for record_type, total in type_points:
            if record_type in result:
                result[record_type] = Decimal(str(total or 0))
        
        return result

    @staticmethod
    def _calculate_point_increases(user_id: int, target_date: date) -> Dict[str, Decimal]:
        """计算积分增长数据"""
        result = {'daily': Decimal('0'), 'weekly': Decimal('0'), 'monthly': Decimal('0')}
        
        # 计算日增长
        daily_points = db.session.query(
            func.sum(PointRecord.points)
        ).filter(
            PointRecord.user_id == user_id,
            func.date(PointRecord.created_at) == target_date
        ).scalar() or 0
        result['daily'] = Decimal(str(daily_points))
        
        # 计算周增长
        week_start = target_date - timedelta(days=target_date.weekday())
        weekly_points = db.session.query(
            func.sum(PointRecord.points)
        ).filter(
            PointRecord.user_id == user_id,
            func.date(PointRecord.created_at) >= week_start,
            func.date(PointRecord.created_at) <= target_date
        ).scalar() or 0
        result['weekly'] = Decimal(str(weekly_points))
        
        # 计算月增长
        month_start = target_date.replace(day=1)
        monthly_points = db.session.query(
            func.sum(PointRecord.points)
        ).filter(
            PointRecord.user_id == user_id,
            func.date(PointRecord.created_at) >= month_start,
            func.date(PointRecord.created_at) <= target_date
        ).scalar() or 0
        result['monthly'] = Decimal(str(monthly_points))
        
        return result

    @staticmethod
    def validate_migration_data(user_id: int = None) -> Dict:
        """验证迁移数据的一致性
        
        Args:
            user_id: 指定用户ID，None表示验证所有用户
            
        Returns:
            Dict: 验证结果
        """
        try:
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()
            
            # 构建查询条件
            point_query = db.session.query(
                PointRecord.user_id,
                func.sum(PointRecord.points).label('point_total')
            ).group_by(PointRecord.user_id)
            
            asset_query = db.session.query(
                UserAsset.user_id,
                UserAsset.available_balance.label('asset_total')
            ).filter(UserAsset.asset_type_id == asset_type.id)
            
            if user_id:
                point_query = point_query.filter(PointRecord.user_id == user_id)
                asset_query = asset_query.filter(UserAsset.user_id == user_id)
            
            # 获取数据
            point_data = {row.user_id: row.point_total for row in point_query.all()}
            asset_data = {row.user_id: row.asset_total for row in asset_query.all()}
            
            # 验证一致性
            all_users = set(point_data.keys()) | set(asset_data.keys())
            consistent_users = 0
            inconsistent_users = []
            missing_in_assets = []
            missing_in_points = []
            
            for uid in all_users:
                point_total = Decimal(str(point_data.get(uid, 0)))
                asset_total = asset_data.get(uid, Decimal('0'))
                
                if uid not in asset_data:
                    missing_in_assets.append(uid)
                elif uid not in point_data:
                    missing_in_points.append(uid)
                elif abs(point_total - asset_total) > Decimal('0.00000001'):  # 允许微小精度差异
                    inconsistent_users.append({
                        'user_id': uid,
                        'point_total': float(point_total),
                        'asset_total': float(asset_total),
                        'difference': float(point_total - asset_total)
                    })
                else:
                    consistent_users += 1
            
            return {
                'total_users': len(all_users),
                'consistent_users': consistent_users,
                'inconsistent_users': len(inconsistent_users),
                'missing_in_assets': len(missing_in_assets),
                'missing_in_points': len(missing_in_points),
                'inconsistent_details': inconsistent_users[:10],  # 只返回前10个
                'missing_in_assets_list': missing_in_assets[:10],
                'missing_in_points_list': missing_in_points[:10]
            }
            
        except Exception as e:
            raise BusinessException(f"数据验证失败: {str(e)}")

    @staticmethod
    def get_migration_status() -> Dict:
        """获取迁移状态"""
        try:
            # 获取最近的迁移日志
            recent_logs = PointMigrationLog.query.order_by(
                PointMigrationLog.created_at.desc()
            ).limit(10).all()
            
            # 获取配置信息
            configs = PointArchiveConfig.query.filter_by(is_active=True).all()
            
            # 统计信息
            total_point_records = PointRecord.query.count()
            total_user_assets = UserAsset.query.join(AssetType).filter(
                AssetType.type == AssetTypeEnum.POINTS
            ).count()
            
            return {
                'recent_migrations': [log.to_dict() for log in recent_logs],
                'active_configs': [config.to_dict() for config in configs],
                'statistics': {
                    'total_point_records': total_point_records,
                    'total_user_assets': total_user_assets
                }
            }
            
        except Exception as e:
            raise BusinessException(f"获取迁移状态失败: {str(e)}")

    @staticmethod
    def cleanup_old_point_records(days_to_keep: int = 90, batch_size: int = 10000) -> Dict:
        """清理旧的积分记录

        Args:
            days_to_keep: 保留天数
            batch_size: 批处理大小

        Returns:
            Dict: 清理结果
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)

            # 统计要删除的记录数
            total_to_delete = PointRecord.query.filter(
                PointRecord.created_at < cutoff_date,
                PointRecord.archive_status == 'archived'
            ).count()

            deleted_count = 0

            # 分批删除
            while True:
                batch_records = PointRecord.query.filter(
                    PointRecord.created_at < cutoff_date,
                    PointRecord.archive_status == 'archived'
                ).limit(batch_size).all()

                if not batch_records:
                    break

                for record in batch_records:
                    db.session.delete(record)

                db.session.commit()
                deleted_count += len(batch_records)

                logger.info(f"已删除 {deleted_count}/{total_to_delete} 条旧记录")

            return {
                'total_to_delete': total_to_delete,
                'deleted_count': deleted_count,
                'cutoff_date': cutoff_date.isoformat()
            }

        except Exception as e:
            db.session.rollback()
            raise BusinessException(f"清理旧记录失败: {str(e)}")
