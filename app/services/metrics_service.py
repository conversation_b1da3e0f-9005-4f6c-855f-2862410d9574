"""指标服务"""
from datetime import datetime, timezone, time
from typing import List, Dict, Any
from sqlalchemy import func, insert, select, update, and_, case
from sqlalchemy.dialects.mysql import insert as mysql_insert
from sqlalchemy.dialects.sqlite import insert as sqlite_insert

from app.models import PointRecord
from app.models.metrics import ServiceMetrics, ServiceMetricsDetail, ServiceMetricsSnapshot
from app.models.base import db
from app.models.device_runtime import DeviceRuntime
from app.models.device import Device
from app.models.project import Project
from app.services.device_service import DeviceService
from app.utils.errors import ValidationError


class MetricsService:
    def update_service_metrics(self, device_id: int, services: List[Dict[str, Any]]) -> None:
        """更新设备的服务指标数据

        Args:
            device_id: 设备ID
            services: 服务指标数据列表，每个指标包含：
                - service_name: 服务名称
                - started_at: 服务启动时间
                - running_time: 运行时间（秒）
                - total_points: 总积分
                - daily_points: 今日积分（可选）
                - updated_at: 更新时间
        """
        try:
            for service in services:
                service_name = service.get('service_name')
                if not service_name:
                    raise ValidationError('service_name is required')

                target_project = Project.query.filter_by(name = service_name).first()
                if not target_project:
                    raise ValidationError('Invalid service_name')

                # 解析时间
                started_at = datetime.fromisoformat(
                    service.get('started_at'),
                ).replace(tzinfo=timezone.utc)
                updated_at = datetime.fromisoformat(
                    service.get('updated_at'),
                ).replace(tzinfo=timezone.utc)

                status_obj = service.get('status') or {'code': 200, 'message': ''}

                # 准备更新的数据
                data = {
                    'device_id': device_id,
                    'project_id': target_project.id,
                    'service_name': service_name,
                    'started_at': started_at,
                    'points': service.get('total_points', 0),
                    'running_time': service.get('running_time', 0),
                    'updated_at': updated_at,
                    'status_code': status_obj.get('code'),
                    'status_msg': status_obj.get('message'),
                }

                # 根据数据库类型选择不同的 insert 语句
                if db.engine.dialect.name == 'mysql':
                    stmt = mysql_insert(ServiceMetricsDetail).values(data)
                    stmt = stmt.on_duplicate_key_update(
                        points=stmt.inserted.points,
                        running_time=stmt.inserted.running_time,
                        updated_at=stmt.inserted.updated_at,
                        status_code = status_obj.get('code'),
                        status_msg = status_obj.get('message')
                    )
                else:  # sqlite
                    stmt = sqlite_insert(ServiceMetricsDetail).values(data)
                    stmt = stmt.on_conflict_do_update(
                        index_elements=['device_id', 'service_name', 'started_at'],
                        set_=dict(
                            points=stmt.excluded.points,
                            running_time=stmt.excluded.running_time,
                            updated_at=stmt.excluded.updated_at,
                            status_code = status_obj.get('code'),
                            status_msg = status_obj.get('message')
                        )
                    )

                db.session.execute(stmt)

            # 提交更改
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise e

    def update_device_runtime(self, device_id: int, device_data: Dict[str, Any]) -> None:
        """更新设备运行时数据

        Args:
            device_id: 设备ID
            device_data: 设备运行时数据，包含：
                - started_at: 设备启动时间
                - running_time: 运行时间（秒）
                - lan_ip: 局域网IP
                - public_ip: 公网IP
                - network: 网络信息，包含：
                    - download_speed: 下载带宽（bps）
                    - upload_speed: 上传带宽（bps）
                - disk: 磁盘信息，包含：
                    - total: 总空间（字节）
                    - used: 已用空间（字节）
                    - free: 可用空间（字节）
                - cpu: CPU信息，包含：
                    - usage: CPU使用率（百分比）
                    - cores: CPU核心数
                - memory: 内存信息，包含：
                    - total: 总内存（字节）
                    - used: 已用内存（字节）
                    - free: 可用内存（字节）
                - updated_at: 更新时间
        """
        try:
            # 解析时间
            started_at = datetime.fromisoformat(
                device_data.get('started_at'),
            ).replace(tzinfo=timezone.utc)
            updated_at = datetime.fromisoformat(
                device_data.get('updated_at'),
            ).replace(tzinfo=timezone.utc)

            # 获取或创建设备运行时记录
            runtime = DeviceRuntime.query.filter_by(device_id=device_id).first()
            if not runtime:
                runtime = DeviceRuntime(
                    device_id=device_id,
                    started_at=started_at,
                    running_time=device_data.get('running_time', 0)
                )
                db.session.add(runtime)
            # 更新设备运行时数据
            runtime.agent_version = device_data.get('agent_version', '')
            runtime.started_at = started_at
            runtime.running_time = device_data.get('running_time', 0)
            runtime.lan_ip = device_data.get('lan_ip')
            runtime.public_ip = device_data.get('public_ip')

            # 更新网络信息
            network = device_data.get('network')
            if network and isinstance(network, dict):
                runtime.network = {
                    'download_speed': network.get('download_speed'),
                    'upload_speed': network.get('upload_speed')
                }

            # 更新磁盘信息
            disk = device_data.get('disk')
            if disk and isinstance(disk, dict):
                runtime.disk = {
                    'total': disk.get('total'),
                    'used': disk.get('used'),
                    'free': disk.get('free')
                }

            # 更新CPU信息
            cpu = device_data.get('cpu')
            if cpu and isinstance(cpu, dict):
                runtime.cpu = {
                    'usage': cpu.get('usage'),
                    'cores': cpu.get('cores')
                }

            # 更新内存信息
            memory = device_data.get('memory')
            if memory and isinstance(memory, dict):
                runtime.memory = {
                    'total': memory.get('total'),
                    'used': memory.get('used'),
                    'free': memory.get('free')
                }

            runtime.updated_at = updated_at

            # 提交更改
            db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise e

    def update_metrics(self, device_id: int, services_data: Dict[str, Any]) -> None:
        """更新设备的服务指标数据和设备运行时数据

        Args:
            device_id: 设备ID
            services_data: 指标数据，包含：
                - services: 服务指标数据列表
                - device: 设备运行时数据
        """
        try:
            # 更新服务指标数据
            services = services_data.get('services', [])
            if services:
                self.update_service_metrics(device_id, services)

            # 更新设备运行时数据
            device_data = services_data.get('device')
            if device_data:
                self.update_device_runtime(device_id, device_data)

            DeviceService.update_device_running_status(device_id)

        except Exception as e:
            db.session.rollback()
            raise e

    def get_daily_statistics(self, user_id: int, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取用户每日统计数据

        Args:
            user_id: 用户ID
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD

        Returns:
            Dict[str, Any]: 每日统计数据，格式：
            {
                "2024-02-13": {
                    "service_name": ["service_a", "service_b"],
                    "points": [100, 200],
                    "running_time": [3600, 7200]
                }
            }
        """
        # 获取用户的所有设备
        devices = Device.query.filter_by(owner_id=user_id).all()
        device_ids = [d.id for d in devices]

        # 构建查询条件
        query = ServiceMetricsSnapshot.query.filter(
            ServiceMetricsSnapshot.device_id.in_(device_ids)
        )

        if start_date:
            query = query.filter(ServiceMetricsSnapshot.day >= start_date)
        if end_date:
            query = query.filter(ServiceMetricsSnapshot.day <= end_date)

        # 获取快照数据
        snapshots = query.all()

        # 按日期组织数据
        statistics = {}
        for snapshot in snapshots:
            day = snapshot.day
            if day not in statistics:
                statistics[day] = {
                    "service_name": [],
                    "points": [],
                    "running_time": [],
                    "points_increase": [],
                    "running_time_increase": []
                }
            
            statistics[day]["service_name"].append(snapshot.service_name)
            statistics[day]["points"].append(snapshot.points)
            statistics[day]["running_time"].append(snapshot.running_time)
            statistics[day]["points_increase"].append(snapshot.points_increase)
            statistics[day]["running_time_increase"].append(snapshot.running_time_increase)

        return statistics

    def get_device_services(self, device_id: int) -> List[str]:
        """获取设备的所有服务名称

        Args:
            device_id: 设备ID

        Returns:
            List[str]: 服务名称列表
        """
        services = ServiceMetrics.query.filter_by(device_id=device_id).all()
        return list(set(s.service_name for s in services))

    def get_service_metrics(self, device_id: int, service_name: str) -> ServiceMetrics:
        """获取服务的指标数据

        Args:
            device_id: 设备ID
            service_name: 服务名称

        Returns:
            ServiceMetrics: 服务指标数据
        """
        return ServiceMetrics.query.filter_by(
            device_id=device_id,
            service_name=service_name
        ).first()

    def get_last_metrics_detail(self, device_id, service_name):
        return ServiceMetricsDetail.query.filter_by(
            device_id=device_id,
            service_name=service_name
        ).order_by(ServiceMetricsDetail.updated_at.desc()).first()

    def get_device_total_running_time(self, device_id: int) -> int:
        """获取设备的总运行时间

        Args:
            device_id: 设备ID

        Returns:
            int: 设备的总运行时间（秒）
        """
        total_running_time = db.session.query(
            func.sum(ServiceMetrics.running_time)
        ).filter(
            ServiceMetrics.device_id == device_id
        ).scalar() or 0

        return total_running_time

    def get_device_total_points(self, device_id: int) -> float:
        """获取设备的总积分

        Args:
            device_id: 设备ID

        Returns:
            float: 设备的总积分
        """
        total_points = db.session.query(
            func.sum(ServiceMetrics.points)
        ).filter(
            ServiceMetrics.device_id == device_id
        ).scalar() or 0

        return float(total_points)

    def aggregate_metrics(self):
        """
        从 ServiceMetricsDetail 聚合数据到 ServiceMetrics
        聚合规则：
        1. 按照 device_id、service_name 分组
        2. 计算每组的 points 最大值和 running_time 总和
        3. 更新或插入到 ServiceMetrics 表中
        """
        try:
            # 从 ServiceMetricsDetail 聚合数据
            subquery = db.session.query(
                ServiceMetricsDetail.device_id,
                ServiceMetricsDetail.service_name,
                func.max(ServiceMetricsDetail.id).label('max_id')
            ).group_by(
                ServiceMetricsDetail.device_id,
                ServiceMetricsDetail.service_name
            ).subquery()

            aggregated_data = db.session.query(
                ServiceMetricsDetail.device_id,
                ServiceMetricsDetail.service_name,
                func.sum(
                    case(
                        (ServiceMetricsDetail.id == subquery.c.max_id, ServiceMetricsDetail.points),
                        else_=0.0
                    )
                ).label('total_points'),
                func.sum(ServiceMetricsDetail.running_time).label('total_running_time'),
                func.max(ServiceMetricsDetail.updated_at).label('latest_updated_at')
            ).join(
                subquery,
                (ServiceMetricsDetail.device_id == subquery.c.device_id) &
                (ServiceMetricsDetail.service_name == subquery.c.service_name)
            ).group_by(
                ServiceMetricsDetail.device_id,
                ServiceMetricsDetail.service_name
            ).all()

            today_time = datetime.combine(datetime.today(), time.min)
            target_projects = {}
            target_devices = {}
            # 更新或插入聚合数据到 ServiceMetrics
            for data in aggregated_data:
                target_project = target_projects.get(data.service_name)
                if not target_project:
                    target_project = Project.query.filter_by(name=data.service_name).first()
                target_projects[data.service_name] = target_project

                target_device = target_devices.get(data.device_id)
                if not target_device:
                    target_device = Device.query.get(data.device_id)
                target_devices[data.device_id] = target_device

                if not target_device or not target_project:
                    continue

                today_user_ubi_points = PointRecord.query.with_entities(
                    func.sum(PointRecord.points)
                ).filter(
                    PointRecord.user_id == target_device.owner_id,
                    PointRecord.record_type == 'project',
                    PointRecord.related_id == target_project.id,
                    PointRecord.created_at >= today_time
                ).scalar() or 0

                # 查找现有记录
                existing_metric = ServiceMetrics.query.filter_by(
                    device_id=data.device_id,
                    service_name=data.service_name
                ).first()

                if existing_metric:
                    # 更新现有记录
                    existing_metric.points = data.total_points
                    existing_metric.running_time = data.total_running_time
                    existing_metric.updated_at = data.latest_updated_at
                    existing_metric.ubi_points = today_user_ubi_points
                else:
                    # 创建新记录
                    new_metric = ServiceMetrics(
                        device_id=data.device_id,
                        project_id=target_project.id,
                        service_name=data.service_name,
                        points=data.total_points,
                        running_time=data.total_running_time,
                        updated_at=data.latest_updated_at,
                        ubi_points=today_user_ubi_points
                    )
                    db.session.add(new_metric)

            # 提交更改
            db.session.commit()
            return len(aggregated_data)

        except Exception as e:
            db.session.rollback()
            raise


metrics_service = MetricsService()
