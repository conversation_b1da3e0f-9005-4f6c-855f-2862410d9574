"""设备服务模块"""

from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass
from typing import Any, Dict, List, Optional, Tuple

from app.enums.biz_enums import DeviceStatus
from app.models.base import db
from app.models.device import Device
from app.models.device_runtime import DeviceRuntime
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetrics, ServiceMetricsSnapshot, ServiceMetricsDetail
from app.models.user import User
from app.utils.exceptions import PermissionError
from app.utils.redis_client import RedisClient


@dataclass
class SystemAppConfig:
    """系统应用配置"""
    name: str
    command: str
    args: List[str]
    configs: Dict[str, str]
    version: str
    download_url: str

class SystemAppConfigGenerator(ABC):
    """系统应用配置生成器抽象基类"""

    @abstractmethod
    def generate(self, device_id: int) -> SystemAppConfig:
        """生成系统应用配置

        Args:
            device_id: 设备ID

        Returns:
            SystemAppConfig: 系统应用配置
        """
        pass

class FrpcConfigGenerator(SystemAppConfigGenerator):
    """Frpc 配置生成器"""

    def generate(self, device_id: int) -> SystemAppConfig:
        """生成 Frpc 系统应用配置"""
        device_name = f"depra{device_id}"

        # 生成 frpc.toml 配置内容
        config_content = f"""serverAddr = "*************"
serverPort = 6699

[auth]
  method = "token"
  token = "w*(kiw5hfL-0"

[[proxies]]
  name = "{device_name}"
  type = "tcpmux"
  customDomains = ["{device_name}"]
  localIP = "127.0.0.1"
  localPort = 22
  multiplexer = "httpconnect"

  [proxies.transport]
    useCompression = true
    useEncryption = true"""

        return SystemAppConfig(
            name="frpc",
            command="frpc",
            args=["-c", "frpc.toml"],
            configs={"frpc.toml": config_content},
            version="01bea65e89966b5fefd4a21acc82fcb9",
            download_url="https://ubisource.oss-accelerate.aliyuncs.com/dl/frpc"
        )

class DeviceService:
    """设备服务"""

    def __init__(self):
        self.system_app_generators: Dict[str, SystemAppConfigGenerator] = {
            "frpc": FrpcConfigGenerator()
        }

    def get_device_list(self, current_user, filters: Optional[Dict] = None, page: int = 1, per_page: int = 10) -> Tuple[List[Device], int]:
        """获取设备列表
        Args:
            current_user: 当前用户
            filters: 过滤条件
            page: 页码，默认为1
            per_page: 每页数量，默认为10
        Returns:
            Tuple[List[Device], int]: 设备列表和总数
        """
        # 参数验证
        page = max(1, page)  # 确保页码至少为1
        per_page = max(1, min(100, per_page))  # 限制每页数量在1-100之间

        query = Device.query

        if filters:
            # 按名称搜索
            if filters.get("name"):
                query = query.filter(Device.name.like(f"%{filters['name']}%"))

            # 按状态搜索
            if filters.get("status"):
                query = query.filter(Device.status == filters["status"])

            # 按标签搜索
            if filters.get("tags"):
                query = query.filter(Device.tags.contains(filters["tags"]))

            # 按 MAC 地址搜索
            if filters.get("mac"):
                query = query.filter(Device.mac_address.like(f"%{filters['mac']}%"))

            # 按 IP 地址搜索
            if filters.get("ip"):
                query = query.filter(Device.ip_address.like(f"%{filters['ip']}%"))

            if filters.get("ids"):
                query = query.filter(Device.id.in_(filters['ids']))

        # 普通用户只能看到自己的设备
        if current_user.role != "admin":
            query = query.filter(Device.owner_id == current_user.id)

        # 获取总数
        total = query.count()

        # 分页并按ID倒序排序
        devices = (
            query.order_by(Device.id.desc())
            .offset((page - 1) * per_page)
            .limit(per_page)
            .all()
        )

        return devices, total

    def get_device(self, device_id: int, user) -> Device:
        """获取设备详情
        Args:
            device_id: 设备ID
            user: 当前用户
        Returns:
            Device: 设备对象
        Raises:
            PermissionError: 没有权限
        """
        device = Device.query.get(device_id)
        if not device:
            return None

        if not user.has_device_permission(device_id):
            raise PermissionError()

        return device

    def create_device(self, data: dict, user: User) -> Device:
        """创建设备
        Args:
            data: 设备数据
            user: 当前用户
        Returns:
            Device: 设备对象
        """
        if data.get("owner_id") != user.id:
            raise PermissionError("权限不足")

        device = Device(
            name=data.get("name"),
            description=data.get("description"),
            ip_address=data.get("ip_address"),
            mac_address=data.get("mac_address"),
            owner_id=data.get("owner_id"),
            hardware_info=data.get("hardware_info")
        )
        db.session.add(device)
        db.session.flush()

        # 生成并设置系统应用配置
        device.system_app_configs = self.generate_system_app_configs(device.id)

        db.session.commit()
        return device

    def auto_create_device(self, mac_address: str) -> Device:
        """自动创建设备（无所有者）
        Args:
            mac_address: MAC地址
        Returns:
            Device: 设备对象
        """
        device = Device(
            name=f"Device-{mac_address[-4:]}",  # 使用MAC地址后4位作为默认名称
            mac_address=mac_address,
            ip_address="0.0.0.0",  # 默认IP地址，后续可通过指标上报或用户绑定时更新
            # owner_id 不设置，保持为 None
        )
        db.session.add(device)
        db.session.flush()

        # 生成并设置系统应用配置
        device.system_app_configs = self.generate_system_app_configs(device.id)

        db.session.commit()
        return device

    def update_device(self, device_id: int, data: dict, user: User) -> Optional[Device]:
        """更新设备
        Args:
            device_id: 设备ID
            data: 设备数据
            user: 当前用户
        Returns:
            Optional[Device]: 设备对象
        """
        device = Device.query.get(device_id)
        if not device:
            return None

        if not user.is_admin and device.owner_id is not None and device.owner_id != user.id:
            raise PermissionError("权限不足")

        for key, value in data.items():
            if hasattr(device, key):
                setattr(device, key, value)

        db.session.commit()
        return device

    def delete_device(self, device_id: int, user: User) -> bool:
        """删除设备
        Args:
            device_id: 设备ID
            user: 当前用户
        Returns:
            bool: 是否成功
        """
        device = Device.query.get(device_id)
        if not device:
            return False

        if not user.is_admin and device.owner_id is not None and device.owner_id != user.id:
            raise PermissionError("权限不足")

        # 删除设备项目关联
        DeviceProject.query.filter_by(device_id=device_id).delete()
        DeviceRuntime.query.filter_by(device_id=device_id).delete()
        ServiceMetrics.query.filter_by(device_id=device_id).delete()
        ServiceMetricsDetail.query.filter_by(device_id=device_id).delete()
        ServiceMetricsSnapshot.query.filter_by(device_id=device_id).delete()
        db.session.delete(device)
        db.session.commit()

        return True

    @staticmethod
    def batch_authorize_by_tags(tags: List[str], user_id: int) -> Tuple[int, str]:
        """
        根据标签批量授权设备
        :param tags: 标签列表
        :param user_id: 用户ID
        :return: 授权成功的设备数量和消息
        """
        # 查找包含所有指定标签的设备
        devices = Device.query
        for tag in tags:
            devices = devices.filter(Device.tags.like(f"%{tag}%"))
        devices = devices.all()

        if not devices:
            return 0, "没有找到匹配的设备"

        # 更新设备所有者
        count = 0
        for device in devices:
            device.owner_id = user_id
            count += 1

        if count > 0:
            db.session.commit()

        return count, f"成功授权 {count} 个设备"

    def generate_service_config(self, device_project):
        """生成服务配置"""
        project = device_project.project
        project_files = project.files
        config = {"files": {}}
        for file in project_files:
            rendered_content = self._render_file_content(file.content, device_project.data)
            config["files"][file.name] = rendered_content
        return config

    def update_device_service_config(self, device_project):
        """更新设备服务配置"""
        config = self.generate_service_config(device_project)
        device_project.config = config
        device_project.state = "updated"
        db.session.commit()
        return config

    @staticmethod
    def authorize_device(device_id: int, user_id: int) -> bool:
        """
        授权设备给用户
        :param device_id: 设备ID
        :param user_id: 用户ID
        :return: 是否授权成功
        """
        device = Device.query.get(device_id)
        if not device:
            return False

        device.owner_id = user_id
        db.session.commit()
        return True

    def get_device_project_service_config(self, device_id: int, project_id: int) -> Optional[Dict]:
        """获取设备项目的服务配置"""
        device_project = DeviceProject.query.filter_by(
            device_id=device_id,
            project_id=project_id
        ).first()

        if not device_project:
            return None

        return device_project.config

    def generate_system_app_configs(self, device_id: int) -> Dict[str, Any]:
        """生成设备的系统应用配置

        Args:
            device_id: 设备ID

        Returns:
            Dict[str, Any]: 系统应用配置
        """
        system_apps = []
        for generator in self.system_app_generators.values():
            system_apps.append(generator.generate(device_id))

        return {
            "system_apps": [asdict(app) for app in system_apps]
        }

    def get_device_runtime(self, device_id: int, user: str) -> Optional[dict]:
        """获取设备运行时信息

        Args:
            device_id: 设备ID
            user: 用户ID

        Returns:
            设备运行时信息字典，如果设备不存在或没有运行时信息则返回 None
        """
        device = self.get_device(device_id, user)
        if not device:
            return None

        runtime = DeviceRuntime.query.filter_by(device_id=device_id).first()
        if not runtime:
            return None

        return runtime.to_dict()

    @staticmethod
    def update_device_running_status(device_id: int):
        device = db.session.get(Device, device_id)
        device.status = DeviceStatus.RUNNING.code
        db.session.commit()
        RedisClient.get_instance().setex(f'device_{device_id}_tag', 1800, 1)

    @staticmethod
    def check_device_offline_status(device_id: int):
        device_tag_ttl = RedisClient.get_instance().ttl(f'device_{device_id}_tag')
        if device_tag_ttl == -2:
            return DeviceStatus.OFFLINE.code
        elif device_tag_ttl <= 900:
            return DeviceStatus.FAILURE.code
        return None

    def batch_delete_devices(self, device_ids: List[int], current_user: User) -> bool:
        """批量删除设备及其关联数据
    
        Args:
            device_ids: 设备ID列表
            current_user: 当前用户
    
        Returns:
            bool: 是否成功删除
        """
        try:
            # 检查设备是否存在以及用户是否有权限删除
            devices_to_delete = Device.query.filter(Device.id.in_(device_ids)).all()
            if len(devices_to_delete) != len(set(device_ids)):
                # 如果查询到的设备数量与请求的设备ID数量不一致，说明部分设备不存在
                # 或者有重复的ID，这里简单处理为部分设备找不到，也可以更细致地返回错误信息
                # raise ValueError("部分设备未找到或ID重复") # 可以考虑抛出更具体的异常
                return False # 或者根据业务需求决定如何处理

            for device in devices_to_delete:
                if not current_user.is_admin and device.owner_id != current_user.id:
                    raise PermissionError(f"用户 {current_user.id} 无权删除设备 {device.id}")

            # 开始事务
            with db.session.begin_nested(): # 使用嵌套事务，如果外部已有事务则加入，否则开启新事务
                # 1. 删除设备关联的 DeviceProject
                DeviceProject.query.filter(DeviceProject.device_id.in_(device_ids)).delete(synchronize_session=False)

                # 2. 删除设备关联的 DeviceRuntime
                DeviceRuntime.query.filter(DeviceRuntime.device_id.in_(device_ids)).delete(synchronize_session=False)

                # 3. 删除设备关联的 ServiceMetrics, ServiceMetricsSnapshot, ServiceMetricsDetail
                # 注意：删除 ServiceMetrics 会级联删除 ServiceMetricsSnapshot 和 ServiceMetricsDetail (如果设置了级联)
                # 如果没有设置级联，需要分别删除
                ServiceMetricsDetail.query.filter(ServiceMetricsDetail.device_id.in_(device_ids)).delete(synchronize_session=False)
                ServiceMetricsSnapshot.query.filter(ServiceMetricsSnapshot.device_id.in_(device_ids)).delete(synchronize_session=False)
                ServiceMetrics.query.filter(ServiceMetrics.device_id.in_(device_ids)).delete(synchronize_session=False)

                # 4. 删除设备本身
                for device in devices_to_delete:
                    db.session.delete(device)
            
            db.session.commit() # 提交主事务
            return True
        except PermissionError: # 捕获权限错误并重新抛出，以便上层API可以处理
            db.session.rollback()
            raise
        except Exception as e: # 捕获其他所有异常，回滚事务并记录错误
            db.session.rollback()
            # 考虑记录日志 current_app.logger.error(f"Batch delete error: {str(e)}")
            # print(f"Error during batch delete: {e}") # for debugging
            return False