"""项目服务模块"""
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple

from jsonschema import ValidationError as JsonSchemaValidationError
from jsonschema import validate

from app.enums.biz_enums import ProxyScope
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.project_file import ProjectFile
from app.models.service_config import ServiceConfig
from app.models.metrics import ServiceMetrics, ServiceMetricsSnapshot
from app.models.user import User
from app.models.wallet import WalletGenerated
from app.services.service_config_generator import ServiceConfigGenerator
from app.services.wallet_service import WalletService
from app.utils.decorators import handle_service_errors
from app.utils.exceptions import NotFoundError, PermissionError, ValidationError
from app.utils.permission import PermissionManager
from sqlalchemy import func
from sqlalchemy import and_, or_, desc


class ProjectService:
    """项目服务"""

    def __init__(self):
        self.permission_manager = PermissionManager()

    @staticmethod
    @handle_service_errors
    def check_device_projects_health(user_id: int) -> Tuple[bool, str, List[dict]]:
        """
        Check if all required device project records exist for a user's devices.
        
        Args:
            user_id: The ID of the user to check
            
        Returns:
            Tuple[bool, str, List[dict]]: 
                - Success status
                - Message
                - List of missing device project combinations
        """
        try:
            # Get all devices owned by the user
            devices = Device.query.filter_by(owner_id=user_id).all()
            if not devices:
                return True, "No devices found for this user", []
            
            # Get all enabled projects
            enabled_projects = Project.query.filter_by(status=1).all()  # 1 = enabled
            if not enabled_projects:
                return True, "No enabled projects found", []
            
            # Track missing records
            missing_records = []
            
            # For each device and project combination
            for device in devices:
                for project in enabled_projects:
                    # Check if device_project record exists
                    existing = DeviceProject.query.filter_by(
                        device_id=device.id,
                        project_id=project.id
                    ).first()
                    
                    # If not, add to missing records
                    if not existing:
                        missing_records.append({
                            "device_id": device.id,
                            "device_name": device.name,
                            "project_id": project.id,
                            "project_name": project.name
                        })
            
            # Return health status
            if missing_records:
                return False, f"Missing {len(missing_records)} device project records", missing_records
            else:
                return True, "All device project records exist", []
                
        except Exception as e:
            return False, f"Error checking device projects health: {str(e)}", []

    @handle_service_errors
    def get_projects(
        self,
        user: User,
        page: int = 1,
        per_page: int = 10,
        status: Optional[int] = None,
        backend: Optional[int] = None
    ) -> Tuple[Dict, Optional[str]]:
        """获取项目列表

        Args:
            user: 当前用户
            page: 页码
            per_page: 每页数量
            status: 项目状态

        Returns:
            Tuple[Dict, Optional[str]]: 分页数据和错误信息
        """
        query = Project.not_deleted()

        # 根据状态筛选
        if status is not None:
            query = query.filter_by(status=status)
        # 非管理员用户只能看到启用状态的项目
        elif not user.is_admin:
            query = query.filter_by(status=Project.STATUS_ENABLED)

        # 获取总数和分页数据
        total = query.count()
        if backend :
            # 默认排序修改时间降序
            query = query.order_by(Project.updated_at.desc())
        projects = query.paginate(page=page, per_page=per_page).items

        return {
            "total": total,
            "items": [p.to_dict(user=user) for p in projects]
        }, None

    @handle_service_errors
    def get_project(self, project_id: int, user: User) -> Tuple[Optional[Project], Optional[str]]:
        """获取项目详情

        Args:
            project_id: 项目ID
            user: 当前用户

        Returns:
            Tuple[Optional[Project], Optional[str]]: 项目对象和错误信息
        """
        project = Project.not_deleted().filter_by(id=project_id).first()
        if not project:
            return None, "项目不存在"

        if not user.is_admin and not user.has_project_permission(project_id):
            raise PermissionError("权限不足")

        return project, None

    @handle_service_errors
    def create_project(self, data: Dict, user: User) -> Tuple[Project, Optional[str]]:
        """创建项目
        Args:
            data: 项目数据
            user: 当前用户
        Returns:
            Tuple[Project, Optional[str]]: 项目对象和错误信息
        """
        if not user.is_admin:
            raise PermissionError("权限不足")

        # 检查服务配置是否存在
        service_config = ServiceConfig.query.get(data.get("service_config_id"))
        if not service_config:
            return None, "服务配置不存在"

        # 检查项目名称是否已存在
        if Project.query.filter_by(name=data["name"]).first():
            return None, "项目名称已存在"

        if data.get("show_new_label", Project.SHOW_NO) == Project.SHOW_YES and data.get("show_day") not in Project.SHOW_DAY :
            return None, "展示时长不正确"

        try:
            project = Project(
                name=data["name"],
                description=data.get("description", ""),
                service_config_id=data.get("service_config_id"),
                service_compose=data.get("service_compose", {}),
                form_schema=data.get("form_schema", {}),
                status=data.get("status", Project.STATUS_ENABLED),
                special_login_hint=data.get("special_login_hint", ""),
                show_new_label=data.get("show_new_label", Project.SHOW_NO)
            )

            if project.show_new_label == Project.SHOW_YES :
                project.show_day = data.get("show_day", 7)
                project.show_date_end = (datetime.utcnow() + timedelta(days=project.show_day))

            # 创建项目文件
            if "files" in data:
                for file_data in data["files"]:
                    file = ProjectFile(
                        name=file_data["name"],
                        content=file_data["content"],
                        project=project
                    )
                    db.session.add(file)

            db.session.add(project)
            db.session.commit()
            return project, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @handle_service_errors
    def update_project(
        self,
        project_id: int,
        data: Dict,
        user: User
    ) -> tuple[Optional[Project], Optional[str]]:
        """更新项目

        Args:
            project_id: 项目ID
            data: 更新数据
            user: 当前用户

        Returns:
            tuple[Optional[Project], Optional[str]]: 项目对象和错误信息
        """
        if not user.is_admin:
            raise PermissionError("权限不足")

        project = Project.query.get(project_id)
        if not project:
            return None, "项目不存在"

        # 如果要禁用项目，检查是否有设备正在使用
        if data.get("status") == Project.STATUS_DISABLED:
            device_projects = DeviceProject.query.filter_by(project_id=project_id).all()
            if device_projects:
                return None, "项目正在被设备使用，无法禁用"

        if data.get("show_new_label", Project.SHOW_NO) == Project.SHOW_YES and data.get("show_day") not in Project.SHOW_DAY :
            return None, "展示时长不正确"

        try:
            # 更新基本信息
            if "name" in data:
                # 检查新名称是否已被其他项目使用
                existing = Project.query.filter(
                    Project.name == data["name"],
                    Project.id != project_id
                ).first()
                if existing:
                    return None, "项目名称已存在"
                project.name = data["name"]

            if "description" in data:
                project.description = data["description"]

            if "service_config_id" in data:
                # 检查服务配置是否存在
                service_config = ServiceConfig.query.get(data["service_config_id"])
                if not service_config:
                    return None, "服务配置不存在"
                project.service_config_id = data["service_config_id"]

            if "service_compose" in data:
                project.service_compose = data["service_compose"]

            if "form_schema" in data:
                project.form_schema = data["form_schema"]

            if "status" in data:
                project.status = data["status"]

            if "special_login_hint" in data:
                project.special_login_hint = data["special_login_hint"]

            if "show_new_label" in data:
                project.show_new_label = data["show_new_label"]
                if project.show_new_label == Project.SHOW_YES:
                    project.show_day = data.get("show_day", 7)
                    project.show_date_end = (datetime.utcnow() + timedelta(days=project.show_day))

            # 更新项目文件
            if "files" in data:
                # 删除现有文件
                ProjectFile.query.filter_by(project_id=project_id).delete()

                # 创建新文件
                for file_data in data["files"]:
                    file = ProjectFile(
                        name=file_data["name"],
                        content=file_data["content"],
                        project=project
                    )
                    db.session.add(file)

            db.session.commit()
            return project, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @handle_service_errors
    def delete_project(self, project_id: int) -> tuple[bool, Optional[str]]:
        """删除项目

        Args:
            project_id: 项目ID

        Returns:
            Tuple[bool, Optional[str]]: 是否成功和错误信息
        """
        project = Project.query.get(project_id)
        if not project:
            return False, "项目不存在"

        # 检查项目是否正在被设备使用
        device_projects = DeviceProject.query.filter_by(project_id=project_id).all()
        if device_projects:
            return False, "项目正在被设备使用，无法删除"

        # 软删除项目
        project.deleted_at = datetime.now()
        db.session.commit()

        return True, None

    @handle_service_errors
    def restore_project(self, project_id: int) -> tuple[bool, Optional[str]]:
        """恢复已删除的项目

        Args:
            project_id: 项目ID

        Returns:
            Tuple[bool, Optional[str]]: 是否成功和错误信息
        """
        project = Project.query.get(project_id)
        if not project:
            return False, "项目不存在"

        if project.deleted_at is None:
            return False, "项目未被删除"

        # 检查项目名称是否已被其他项目使用
        if Project.query.filter(
            Project.name == project.name,
            Project.id != project.id,
            Project.deleted_at.is_(None)
        ).first():
            return False, "项目名称已被使用"

        project.deleted_at = None
        db.session.commit()
        return True, None

    @handle_service_errors
    def get_project_detail(self, project_id: int, user: User) -> Project:
        """获取项目详情"""
        project = Project.query.get_or_404(project_id)

        # 检查权限
        has_permission, error = self.permission_manager.check_project_permission(user, project_id)
        if not has_permission:
            raise PermissionError(error or "权限不足")

        return project

    @handle_service_errors
    def add_project_file(self, project_id: int, name: str, content: str) -> tuple[Optional[ProjectFile], Optional[str]]:
        """添加项目文件

        Args:
            project_id: 项目ID
            name: 文件名
            content: 文件内容

        Returns:
            tuple[Optional[ProjectFile], Optional[str]]: 项目文件对象和错误信息
        """
        project = Project.query.get(project_id)
        if not project:
            return None, "项目不存在"

        # 检查文件名是否已存在
        if ProjectFile.query.filter_by(project_id=project_id, name=name).first():
            return None, "文件名已存在"

        project_file = ProjectFile(
            name=name,
            content=content,
            project_id=project_id
        )
        db.session.add(project_file)
        db.session.commit()
        return project_file, None

    @handle_service_errors
    def update_project_file(self, project_id: int, file_id: int, data: Dict) -> tuple[Optional[ProjectFile], Optional[str]]:
        """更新项目文件

        Args:
            project_id: 项目ID
            file_id: 文件ID
            data: 更新数据

        Returns:
            tuple[Optional[ProjectFile], Optional[str]]: 项目文件对象和错误信息
        """
        project_file = ProjectFile.query.filter_by(id=file_id, project_id=project_id).first()
        if not project_file:
            return None, "文件不存在"

        # 如果要更新文件名，检查新文件名是否已存在
        new_name = data.get("name")
        if new_name and new_name != project_file.name:
            if ProjectFile.query.filter_by(project_id=project_id, name=new_name).first():
                return None, "文件名已存在"
            project_file.name = new_name

        # 更新文件内容
        if "content" in data:
            project_file.content = data["content"]

        db.session.commit()
        return project_file, None

    @handle_service_errors
    def delete_project_file(self, project_id: int, file_id: int) -> tuple[bool, Optional[str]]:
        """删除项目文件

        Args:
            project_id: 项目ID
            file_id: 文件ID

        Returns:
            tuple[bool, Optional[str]]: 是否成功和错误信息
        """
        project_file = ProjectFile.query.filter_by(id=file_id, project_id=project_id).first()
        if not project_file:
            return False, "文件不存在"

        db.session.delete(project_file)
        db.session.commit()
        return True, None

    @handle_service_errors
    def get_project_files(self, project_id: int) -> List[ProjectFile]:
        """获取项目文件列表

        Args:
            project_id: 项目ID

        Returns:
            List[ProjectFile]: 项目文件列表
        """
        return ProjectFile.query.filter_by(project_id=project_id).all()

    @handle_service_errors
    def generate_device_project_config(self, device_project: DeviceProject):
        """生成设备项目配置

        Args:
            device_project: DeviceProject实例

        Returns:
            dict: 生成的服务配置
        """
        try:
            project = device_project.project
            if project.status == Project.STATUS_DISABLED:
                return None

            device = device_project.device

            # 准备基础配置
            base_config = {}
            if project.service_config:
                base_config = {
                    "name": project.service_config.name,
                    "description": project.service_config.description,
                    "docker_compose": project.service_config.docker_compose,
                    "env": project.service_config.default_env or {}
                }

            device_project_data = device_project.data or {}
            try:
                validate(instance=device_project_data, schema=device_project.project.form_schema)
            except JsonSchemaValidationError as e:
                return None

            proxy_scope = device_project_data.get('proxy_scope')
            proxy_enabled = device_project_data.get('proxy_enable', False)

            combined_proxy_val = (
                device_project_data.get('proxy') if proxy_scope == ProxyScope.PROJECT_PROXY.value
                else device.service_configs.get('proxy') if proxy_scope == ProxyScope.DEVICE_PROXY.value
                else None
            ) if proxy_enabled else None

            combined_data = {**device_project_data}
            if combined_proxy_val:
                combined_data["proxy"] = combined_proxy_val
            else:
                combined_data.pop("proxy", None)

            # 获取钱包数据
            wallet_data = WalletService.get_or_create_wallets(device.owner_id)

            # 合并渲染数据
            render_data = {
                # 添加基础配置中的环境变量
                **(base_config.get("env", {})),
                # 添加设备项目的数据
                **combined_data,
                # 添加钱包数据
                "wallet_data": json.dumps(wallet_data),
                # 添加一些内置变量
                "project_name": project.name,
                "device_name": device_project.device.name
            }

            # 生成配置
            config = ServiceConfigGenerator.generate_service_config(
                base_config=base_config,
                project_compose=project.service_compose,
                project_files=project.files,
                render_data=render_data,
                project_name=project.name
            )
            return config
        except Exception as e:
            print(f"Error in generate_device_project_config: {str(e)}")
            return None

    @handle_service_errors
    def update_device_project_config(self, project_id, device_project_id):
        """更新设备项目配置

        Args:
            project_id: 项目ID
            device_project_id: 设备项目ID

        Returns:
            bool: 是否更新成功
        """
        # 获取项目
        project = Project.query.get(project_id)
        if not project:
            raise NotFoundError("项目不存在")

        # 获取设备项目
        device_project = DeviceProject.query.get(device_project_id)
        if not device_project:
            raise NotFoundError("设备项目不存在")

        # 检查设备项目是否属于该项目
        if device_project.project_id != project.id:
            raise ValidationError("设备项目不属于该项目")

        # 生成配置
        service_config = self.generate_device_project_config(device_project)
        if service_config:
            # 更新设备的服务配置
            # Deprecated: service_configs is no longer in use
            device_project.device.service_configs = {}
            # 更新设备项目状态
            device_project.state = "updated"
            db.session.commit()
            return True
        return False

    @handle_service_errors
    def get_projects_metrics(
        self,
        service_names: List[str],
        user_id: Optional[int] = None
    ) -> Tuple[Dict[str, Dict[str, float]], Optional[str]]:
        """获取服务指标数据

        Args:
            service_names: 服务名称列表
            user_id: 用户ID，如果提供则只返回该用户拥有的设备的指标数据

        Returns:
            Tuple[Dict[str, Dict[str, Any]], Optional[str]]: 服务指标数据字典和错误信息
            {
                service_name: {
                    'device_count': 设备数量,
                    'device_status': [
                        {
                            'status': 'stopped',
                            'device_ids': [设备ID列表]
                        },
                        {
                            'status': 'created',
                            'device_ids': [设备ID列表]
                        },
                        {
                            'status': 'updated',
                            'device_ids': [设备ID列表]
                        },
                        {
                            'status': 'running',
                            'device_ids': [设备ID列表]
                        }
                    ],
                    'custody_type': 托管类型,
                    'total_running_time': 总运行时长,
                    'total_points': 总积分,
                    'yesterday_points': 昨日单日积分,
                    'yesterday_running_time': 昨日单日运行时长
                }
            }
        """
        from app.models.metrics import ServiceMetrics, ServiceMetricsSnapshot
        from app.models.device import Device
        from app.models.device_project import DeviceProject
        from app.models.project import Project
        from datetime import datetime, timedelta

        # 获取今天和昨天的日期
        today_time = datetime.now(timezone.utc)
        yesterday = (today_time - timedelta(days=1)).strftime("%Y-%m-%d")
        day_before_yesterday = (today_time - timedelta(days=2)).strftime("%Y-%m-%d")
        # 初始化结果字典
        result = {
            service_name: {
                'device_count': 0,
                'device_status': [
                    {'status': 'stopped', 'device_ids': []},
                    {'status': 'created', 'device_ids': []},
                    {'status': 'updated', 'device_ids': []},
                    {'status': 'running', 'device_ids': []}
                ],
                'custody_type': "self custody",
                'total_running_time': 0,
                'total_points': 0,
                'yesterday_points': 0,
                'yesterday_running_time': 0
            }
            for service_name in service_names
        }

        # 构建基础查询
        base_query = db.session.query(
            ServiceMetrics.service_name,
            func.count(ServiceMetrics.device_id.distinct()).label('device_count'),
            func.sum(ServiceMetrics.running_time).label('total_running_time'),
            func.sum(ServiceMetrics.points).label('total_points'),
            func.sum(ServiceMetrics.ubi_points).label('total_ubi_points')
        ).filter(
            ServiceMetrics.service_name.in_(service_names)
        )

        # 如果提供了 user_id，添加用户设备过滤
        if user_id is not None:
            user_device_subquery = db.session.query(Device.id).filter(Device.owner_id == user_id)
            base_query = base_query.filter(ServiceMetrics.device_id.in_(user_device_subquery))

        # 按服务名称分组
        base_query = base_query.group_by(ServiceMetrics.service_name)

        # 获取所有指标数据
        for metrics in base_query.all():
            result[metrics.service_name].update({
                'device_count': int(metrics.device_count or 0),
                'total_running_time': float(metrics.total_running_time or 0),
                'total_points': float(metrics.total_points or 0),
                'total_ubi_points': float(metrics.total_ubi_points or 0),
            })

        # 获取项目ID和名称的映射
        project_name_map = {}
        for project in Project.query.filter(Project.name.in_(service_names)).all():
            project_name_map[project.id] = project.name

        # 获取设备状态统计
        for project_id, project_name in project_name_map.items():
            # 获取项目关联的所有设备项目
            device_projects_query = DeviceProject.query.filter(
                DeviceProject.project_id == project_id
            )

            # 如果提供了 user_id，添加用户设备过滤
            if user_id is not None:
                user_device_subquery = db.session.query(Device.id).filter(Device.owner_id == user_id)
                device_projects_query = device_projects_query.filter(DeviceProject.device_id.in_(user_device_subquery))

            # 获取所有设备项目
            device_projects = device_projects_query.all()

            # 按状态分组设备ID
            status_map = {
                'stopped': [],
                'created': [],
                'updated': [],
                'running': []
            }

            for dp in device_projects:
                if dp.state in status_map:
                    status_map[dp.state].append(dp.device_id)

            # 更新结果字典中的设备状态
            if project_name in result:
                for i, status in enumerate(['stopped', 'created', 'updated', 'running']):
                    result[project_name]['device_status'][i]['device_ids'] = status_map[status]

        # 获取昨日指标数据 - 简化的直接查询方法
        service_yesterday_metrics = {}
        
        for service_name in service_names:
            # 初始化该服务的昨日指标
            service_yesterday_metrics[service_name] = {
                'yesterday_points': 0,
                'yesterday_ubi_points': 0,
                'yesterday_running_time': 0
            }
            
            # 查询该服务的所有设备（按用户过滤，如果需要）
            device_query = db.session.query(ServiceMetrics.device_id).filter(
                ServiceMetrics.service_name == service_name
            )
            
            if user_id is not None:
                user_device_subquery = db.session.query(Device.id).filter(Device.owner_id == user_id)
                device_query = device_query.filter(ServiceMetrics.device_id.in_(user_device_subquery))
            
            device_ids = [d[0] for d in device_query.distinct().all()]
            
            # 如果没有设备，跳过
            if not device_ids:
                continue
            
            # 对每个设备，直接查找昨天的最后一个快照，提取其 points 和 running_time
            for device_id in device_ids:
                # 首先查询昨天的最后一个快照
                yesterday_snapshot = ServiceMetricsSnapshot.query.filter(
                    ServiceMetricsSnapshot.device_id == device_id,
                    ServiceMetricsSnapshot.service_name == service_name,
                    ServiceMetricsSnapshot.day == yesterday
                ).order_by(ServiceMetricsSnapshot.created_at.desc()).first()

                day_before_yesterday_snapshot = ServiceMetricsSnapshot.query.filter(
                    ServiceMetricsSnapshot.device_id == device_id,
                    ServiceMetricsSnapshot.service_name == service_name,
                    ServiceMetricsSnapshot.day == day_before_yesterday
                ).order_by(ServiceMetricsSnapshot.created_at.desc()).first()

                db_points = day_before_yesterday_snapshot.points if day_before_yesterday_snapshot else 0
                db_ubi_points = day_before_yesterday_snapshot.ubi_points if day_before_yesterday_snapshot else 0
                db_running_time = day_before_yesterday_snapshot.running_time if day_before_yesterday_snapshot else 0

                if yesterday_snapshot:
                    service_yesterday_metrics[service_name]['yesterday_points'] = max(0, (yesterday_snapshot.points or 0) - (db_points or 0))
                    service_yesterday_metrics[service_name]['yesterday_ubi_points'] = max(0, (yesterday_snapshot.ubi_points or 0) - (db_ubi_points or 0))
                    service_yesterday_metrics[service_name]['yesterday_running_time'] = max(0, (yesterday_snapshot.running_time or 0) - (db_running_time or 0))
        
        # 为了兼容前端（目前展示用的是 Points 字段）更新结果
        for service_name, metrics in service_yesterday_metrics.items():
            if service_name in result:
                result[service_name]['yesterday_points'] = metrics['yesterday_points']
                result[service_name]['yesterday_ubi_points'] = metrics['yesterday_ubi_points']
                result[service_name]['yesterday_running_time'] = metrics['yesterday_running_time']

        return result, None

class DeviceProjectService:
    """设备项目关联服务"""

    @staticmethod
    def get_device_projects(
        device_id: int,
        user: User
    ) -> Tuple[Optional[List[DeviceProject]], Optional[str]]:
        """获取设备的项目列表

        Args:
            device_id: 设备ID
            user: 当前用户

        Returns:
            Tuple[Optional[List[DeviceProject]], Optional[str]]: 设备项目列表和错误信息
        """
        device = db.session.get(Device, device_id)
        if not device:
            return None, "设备不存在"

        if not user.is_admin and not device.has_user_permission(user.id):
            raise PermissionError("权限不足")

        device_projects = DeviceProject.query.filter_by(device_id=device_id).all()
        return device_projects, None

    @staticmethod
    def create_device_project(
        device_id: int,
        project_id: int,
        data: Dict,
        user: User
    ) -> Tuple[Optional[DeviceProject], Optional[str]]:
        """创建设备项目配置

        Args:
            device_id: 设备ID
            project_id: 项目ID
            data: 配置数据
            user: 当前用户

        Returns:
            Tuple[Optional[DeviceProject], Optional[str]]: 设备项目对象和错误信息
        """
        device = db.session.get(Device, device_id)
        if not device:
            return None, "设备不存在"

        if not user.is_admin and not device.has_user_permission(user.id, "write"):
            raise PermissionError("权限不足")

        project = db.session.get(Project, project_id)
        if not project:
            return None, "项目不存在"

        if DeviceProject.query.filter_by(device_id=device_id, project_id=project_id).first():
            return None, "项目配置已存在"

        # 使用 jsonschema 验证数据
        try:
            validate(instance=data, schema=project.form_schema)
        except JsonSchemaValidationError as e:
            return None, f"数据验证失败: {e.message}"

        device_project = DeviceProject(
            device_id=device_id,
            project_id=project_id,
            data=data
        )
        db.session.add(device_project)
        db.session.commit()

        # 更新设备的服务配置
        device_project.update_device_service_config()
        return device_project, None

    @staticmethod
    def update_device_project(
        device_id: int,
        project_id: int,
        data: Dict,
        user: User
    ) -> Tuple[Optional[DeviceProject], Optional[str]]:
        """更新设备项目配置

        Args:
            device_id: 设备ID
            project_id: 项目ID
            data: 更新数据
            user: 当前用户

        Returns:
            Tuple[Optional[DeviceProject], Optional[str]]: 设备项目对象和错误信息
        """
        device = db.session.get(Device, device_id)
        if not device:
            return None, "设备不存在"

        if not user.is_admin and not device.has_user_permission(user.id, "write"):
            raise PermissionError("权限不足")

        device_project = DeviceProject.query.filter_by(
            device_id=device_id,
            project_id=project_id
        ).first()
        if not device_project:
            return None, "项目配置不存在"

        # 写死的数据校验
        if data.get("proxy_enable"):
            # 检查项目是否允许配置代理
            not_in_validate = device_project.project.form_schema.get("not", {})
            if "proxy" in (not_in_validate.get("required") or []):
                return None, "该项目不允许配置代理"

            # 获取代理范围
            proxy_scope = data.get("proxy_scope")
            # 根据代理范围进行不同的检查
            if proxy_scope == ProxyScope.DEVICE_PROXY.value:
                # 设备代理配置检查
                device_configs = (device_project.device.service_configs or {})
                if not device_configs.get("proxy"):
                    return None, "设备代理未配置"
            else:
                # 项目代理配置检查
                if not data.get("proxy"):
                    return None, "项目代理未配置"


        # 使用 jsonschema 验证数据
        try:
            validate(instance=data, schema=device_project.project.form_schema)
        except JsonSchemaValidationError as e:
            return None, f"数据验证失败: {e.message}"

        # # if email or password is null, change project state to stopped
        # if not data.get('email') or not data.get('password'):
        #     # device_project.state = 'created'
        #     db.session.delete(device_project)
        #     db.session.commit()
        # else:
        device_project.data = data
        db.session.commit()

        # 更新设备的服务配置
        device_project.update_device_service_config()

        return device_project, None

    @staticmethod
    def delete_device_project(
        device_id: int,
        project_id: int,
        user: User
    ) -> Tuple[bool, Optional[str]]:
        """删除设备项目配置

        Args:
            device_id: 设备ID
            project_id: 项目ID
            user: 当前用户

        Returns:
            Tuple[bool, Optional[str]]: 是否成功和错误信息
        """
        device = db.session.get(Device, device_id)
        if not device:
            return False, "设备不存在"

        if not user.is_admin and not device.has_user_permission(user.id, "write"):
            raise PermissionError("权限不足")

        device_project = DeviceProject.query.filter_by(
            device_id=device_id,
            project_id=project_id
        ).first()
        if not device_project:
            return False, "项目配置不存在"

        # 从设备的服务配置中移除项目配置
        # Deprecated: service_configs is no longer in use
        device_project.device.service_configs = {}

        db.session.delete(device_project)
        db.session.commit()
        return True, None
