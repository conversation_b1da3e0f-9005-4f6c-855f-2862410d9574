"""积分归档服务"""

import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from sqlalchemy import func, and_, or_
from sqlalchemy.exc import IntegrityError

from app.models.base import db
from app.models.points import PointRecord
from app.models.point_archive import (
    PointRecordArchive, PointMigrationLog, 
    PointArchiveConfig, ArchiveType
)
from app.utils.errors import BusinessException

logger = logging.getLogger(__name__)


class PointArchiveService:
    """积分归档服务"""

    @staticmethod
    def archive_daily_records(target_date: date = None, batch_size: int = 1000) -> Dict:
        """归档日积分记录
        
        Args:
            target_date: 目标日期，默认为昨天
            batch_size: 批处理大小
            
        Returns:
            Dict: 归档结果
        """
        if target_date is None:
            target_date = date.today() - timedelta(days=1)
        
        return PointArchiveService._archive_records_by_period(
            target_date, ArchiveType.DAILY, batch_size
        )

    @staticmethod
    def archive_weekly_records(target_date: date = None, batch_size: int = 1000) -> Dict:
        """归档周积分记录
        
        Args:
            target_date: 目标日期，默认为上周最后一天
            batch_size: 批处理大小
            
        Returns:
            Dict: 归档结果
        """
        if target_date is None:
            # 计算上周的最后一天（周日）
            today = date.today()
            days_since_monday = today.weekday()
            last_sunday = today - timedelta(days=days_since_monday + 1)
            target_date = last_sunday
        
        # 计算周的开始日期（周一）
        week_start = target_date - timedelta(days=target_date.weekday())
        week_end = target_date
        
        return PointArchiveService._archive_records_by_period(
            target_date, ArchiveType.WEEKLY, batch_size, week_start, week_end
        )

    @staticmethod
    def archive_monthly_records(target_date: date = None, batch_size: int = 1000) -> Dict:
        """归档月积分记录
        
        Args:
            target_date: 目标日期，默认为上月最后一天
            batch_size: 批处理大小
            
        Returns:
            Dict: 归档结果
        """
        if target_date is None:
            # 计算上月的最后一天
            today = date.today()
            first_day_this_month = today.replace(day=1)
            target_date = first_day_this_month - timedelta(days=1)
        
        # 计算月的开始和结束日期
        month_start = target_date.replace(day=1)
        month_end = target_date
        
        return PointArchiveService._archive_records_by_period(
            target_date, ArchiveType.MONTHLY, batch_size, month_start, month_end
        )

    @staticmethod
    def _archive_records_by_period(
        archive_date: date, 
        archive_type: ArchiveType, 
        batch_size: int,
        period_start: date = None,
        period_end: date = None
    ) -> Dict:
        """按时间段归档记录
        
        Args:
            archive_date: 归档日期
            archive_type: 归档类型
            batch_size: 批处理大小
            period_start: 时间段开始日期
            period_end: 时间段结束日期
            
        Returns:
            Dict: 归档结果
        """
        migration_log = PointMigrationLog(
            migration_type=f'archive_{archive_type.value}',
            migration_date=archive_date,
            start_time=datetime.utcnow(),
            config={
                'archive_type': archive_type.value,
                'batch_size': batch_size,
                'period_start': period_start.isoformat() if period_start else None,
                'period_end': period_end.isoformat() if period_end else None
            }
        )
        
        try:
            # 如果没有指定时间段，使用单日
            if period_start is None:
                period_start = archive_date
            if period_end is None:
                period_end = archive_date
            
            # 查询需要归档的记录
            base_query = db.session.query(
                PointRecord.user_id,
                PointRecord.record_type,
                func.sum(PointRecord.points).label('total_points'),
                func.count(PointRecord.id).label('record_count')
            ).filter(
                func.date(PointRecord.created_at) >= period_start,
                func.date(PointRecord.created_at) <= period_end,
                PointRecord.archive_status == 'pending'
            ).group_by(PointRecord.user_id, PointRecord.record_type)
            
            total_groups = base_query.count()
            migration_log.total_records = total_groups
            db.session.add(migration_log)
            db.session.commit()
            
            processed = 0
            success = 0
            failed = 0
            errors = []
            
            # 分批处理
            for offset in range(0, total_groups, batch_size):
                batch_data = base_query.offset(offset).limit(batch_size).all()
                
                for group_data in batch_data:
                    try:
                        user_id = group_data.user_id
                        record_type = group_data.record_type
                        total_points = Decimal(str(group_data.total_points or 0))
                        record_count = group_data.record_count
                        
                        # 检查是否已存在归档记录
                        existing_archive = PointRecordArchive.query.filter_by(
                            user_id=user_id,
                            record_type=record_type,
                            archive_type=archive_type,
                            archive_date=archive_date
                        ).first()
                        
                        if existing_archive:
                            # 更新现有归档记录
                            existing_archive.total_points += total_points
                            existing_archive.record_count += record_count
                            existing_archive.updated_at = datetime.utcnow()
                        else:
                            # 创建新归档记录
                            new_archive = PointRecordArchive(
                                user_id=user_id,
                                record_type=record_type,
                                archive_type=archive_type,
                                archive_date=archive_date,
                                total_points=total_points,
                                record_count=record_count
                            )
                            db.session.add(new_archive)
                        
                        success += 1
                        
                    except Exception as e:
                        failed += 1
                        error_msg = f"User {group_data.user_id}, Type {group_data.record_type}: {str(e)}"
                        errors.append(error_msg)
                        logger.error(f"归档记录失败: {error_msg}")
                    
                    processed += 1
                
                # 批量提交
                try:
                    db.session.commit()
                    migration_log.update_progress(processed, success, failed)
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"批量提交失败: {str(e)}")
                    raise
            
            # 更新原始记录状态
            if success > 0:
                updated_count = db.session.query(PointRecord).filter(
                    func.date(PointRecord.created_at) >= period_start,
                    func.date(PointRecord.created_at) <= period_end,
                    PointRecord.archive_status == 'pending'
                ).update({
                    'archive_status': 'archived',
                    'archived_at': datetime.utcnow()
                }, synchronize_session=False)
                
                db.session.commit()
                logger.info(f"更新了 {updated_count} 条记录的归档状态")
            
            # 完成归档
            result = {
                'archive_type': archive_type.value,
                'archive_date': archive_date.isoformat(),
                'period_start': period_start.isoformat(),
                'period_end': period_end.isoformat(),
                'total_groups': total_groups,
                'processed': processed,
                'success': success,
                'failed': failed,
                'errors': errors[:10]  # 只返回前10个错误
            }
            
            migration_log.mark_completed('success' if failed == 0 else 'partial_success')
            db.session.commit()
            
            return result
            
        except Exception as e:
            migration_log.mark_failed(str(e))
            db.session.commit()
            raise BusinessException(f"归档失败: {str(e)}")

    @staticmethod
    def get_archived_points(
        user_id: int, 
        start_date: date, 
        end_date: date,
        archive_type: ArchiveType = ArchiveType.DAILY,
        record_type: str = None
    ) -> List[Dict]:
        """获取归档的积分数据
        
        Args:
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            archive_type: 归档类型
            record_type: 记录类型过滤
            
        Returns:
            List[Dict]: 归档数据列表
        """
        try:
            query = PointRecordArchive.query.filter(
                PointRecordArchive.user_id == user_id,
                PointRecordArchive.archive_type == archive_type,
                PointRecordArchive.archive_date >= start_date,
                PointRecordArchive.archive_date <= end_date
            )
            
            if record_type:
                query = query.filter(PointRecordArchive.record_type == record_type)
            
            archives = query.order_by(PointRecordArchive.archive_date.desc()).all()
            
            return [archive.to_dict() for archive in archives]
            
        except Exception as e:
            raise BusinessException(f"获取归档数据失败: {str(e)}")

    @staticmethod
    def get_archive_statistics(days: int = 30) -> Dict:
        """获取归档统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            Dict: 统计信息
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            # 按归档类型统计
            archive_stats = db.session.query(
                PointRecordArchive.archive_type,
                func.count(PointRecordArchive.id).label('archive_count'),
                func.sum(PointRecordArchive.total_points).label('total_points'),
                func.sum(PointRecordArchive.record_count).label('total_records')
            ).filter(
                PointRecordArchive.archive_date >= start_date,
                PointRecordArchive.archive_date <= end_date
            ).group_by(PointRecordArchive.archive_type).all()
            
            # 按日期统计
            daily_stats = db.session.query(
                PointRecordArchive.archive_date,
                func.count(PointRecordArchive.id).label('archive_count'),
                func.sum(PointRecordArchive.total_points).label('total_points')
            ).filter(
                PointRecordArchive.archive_date >= start_date,
                PointRecordArchive.archive_date <= end_date
            ).group_by(PointRecordArchive.archive_date).order_by(
                PointRecordArchive.archive_date.desc()
            ).all()
            
            # 原始记录统计
            pending_count = PointRecord.query.filter(
                PointRecord.archive_status == 'pending'
            ).count()
            
            archived_count = PointRecord.query.filter(
                PointRecord.archive_status == 'archived'
            ).count()
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                },
                'archive_by_type': [
                    {
                        'archive_type': stat.archive_type.value,
                        'archive_count': stat.archive_count,
                        'total_points': float(stat.total_points or 0),
                        'total_records': stat.total_records
                    }
                    for stat in archive_stats
                ],
                'daily_archives': [
                    {
                        'date': stat.archive_date.isoformat(),
                        'archive_count': stat.archive_count,
                        'total_points': float(stat.total_points or 0)
                    }
                    for stat in daily_stats
                ],
                'original_records': {
                    'pending_count': pending_count,
                    'archived_count': archived_count,
                    'total_count': pending_count + archived_count
                }
            }
            
        except Exception as e:
            raise BusinessException(f"获取归档统计失败: {str(e)}")

    @staticmethod
    def cleanup_old_archives(archive_type: ArchiveType, retention_days: int) -> Dict:
        """清理过期的归档数据
        
        Args:
            archive_type: 归档类型
            retention_days: 保留天数
            
        Returns:
            Dict: 清理结果
        """
        try:
            cutoff_date = date.today() - timedelta(days=retention_days)
            
            # 查询要删除的记录
            old_archives = PointRecordArchive.query.filter(
                PointRecordArchive.archive_type == archive_type,
                PointRecordArchive.archive_date < cutoff_date
            )
            
            delete_count = old_archives.count()
            
            # 执行删除
            old_archives.delete(synchronize_session=False)
            db.session.commit()
            
            return {
                'archive_type': archive_type.value,
                'cutoff_date': cutoff_date.isoformat(),
                'deleted_count': delete_count
            }
            
        except Exception as e:
            db.session.rollback()
            raise BusinessException(f"清理归档数据失败: {str(e)}")
