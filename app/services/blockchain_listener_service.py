"""
区块链监听服务
监听链上交易并同步到延迟结算系统
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from bip32 import base58
from eth_abi import decode as abi_decode
from eth_utils import to_checksum_address
from hexbytes import HexBytes

from flask import current_app

from app.middlewares.web3.blockchain_strategy_factory import BlockchainStrategyFactory
from app.models import db
from app.models.blockchain import Blockchain
from app.models.settlement import TradeFlowTypeEnum, TradeFlow
from app.models.wallet import AddressMapping
from app.services.blockchain_service import BlockchainService
from app.services.settlement_service import SettlementService

@dataclass
class ChainTransaction:
    """链上交易数据结构"""
    tx_hash: str
    from_address: str
    to_address: str
    amount: Decimal
    token_address: Optional[str]
    block_number: int
    timestamp: datetime
    gas_fee: Decimal
    status: str  # 'success' or 'failed'


class BlockchainListenerService:
    """区块链监听服务"""

    _instance = None

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = BlockchainListenerService()
        return cls._instance

    def __init__(self):
        self.factory = BlockchainStrategyFactory()
        self.is_running = False
        self.listeners = {}

    async def start_all_listeners(self):
        """启动所有链的监听器"""
        current_app.logger.info("🚀 启动区块链监听服务...")

        self.is_running = True

        # 获取所有支持的区块链
        supported_chains = BlockchainService.get_all()

        tasks = []
        for chain in [supported_chain for supported_chain in supported_chains] :
            try:
                if chain:
                    task = asyncio.create_task(self._start_chain_listener(chain))
                    tasks.append(task)
                    self.listeners[chain.id] = task
                    current_app.logger.info(f"✓ 启动 {chain.chain_name} 监听器")
            except Exception as e:
                current_app.logger.error(f"✗ 启动 {chain.chain_code} 监听器失败: {e}")

        current_app.logger.info(f"📊 总共启动了 {len(tasks)} 个区块链监听器")

        # 等待所有监听器
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e:
                current_app.logger.error(f"❌ 监听器运行异常: {e}")
            finally:
                current_app.logger.info("🛑 所有区块链监听器已停止")

    async def _start_chain_listener(self, chain: Blockchain):
        """启动单个链的监听器"""
        try:
            strategy = self.factory.get_strategy(chain)

            while self.is_running:
                try:
                    # 获取最新区块
                    latest_block = await self._get_latest_block(strategy)

                    # 获取上次处理的区块号
                    last_processed_block = self._get_last_processed_block(chain.id)

                    # 处理新区块
                    if latest_block > last_processed_block:
                        # 确保起始区块不是创世区块（block 0）
                        start_block = max(last_processed_block + 1, 1)

                        await self._process_blocks(
                            strategy,
                            chain,
                            start_block,
                            latest_block
                        )

                        # 更新最后处理的区块号
                        self._update_last_processed_block(chain.id, latest_block)

                    # 等待一段时间再检查
                    await asyncio.sleep(10)  # 10秒检查一次

                except Exception as e:
                    current_app.logger.error(f"❌ {chain.chain_name} 监听器错误: {e}")
                    await asyncio.sleep(30)  # 错误时等待30秒

        except Exception as e:
            current_app.logger.error(f"❌ {chain.chain_name} 监听器启动失败: {e}")

    async def _get_latest_block(self, strategy) -> int:
        """获取最新区块号"""
        # 这里需要根据不同的策略实现
        # EVM 链使用 web3.eth.block_number
        # Solana 使用 getSlot
        try:
            if hasattr(strategy, 'web3'):
                return strategy.web3.eth.block_number
            elif hasattr(strategy, 'client'):
                # Solana 实现
                response = strategy.client.get_slot()
                return response.value
            else:
                return 0
        except Exception as e:
            current_app.logger.error(f"获取最新区块失败: {e}")
            return 0

    async def _process_blocks(self, strategy, chain: Blockchain, start_block: int, end_block: int):
        """处理区块范围内的交易"""
        current_app.logger.info(f"处理 {chain.chain_name} 区块 {start_block} - {end_block}")

        for block_number in range(start_block, end_block + 1):
            try:
                # 跳过创世区块（block 0）
                if block_number == 0:
                    current_app.logger.info(f"⏭️ 跳过 {chain.chain_name} 创世区块 (block 0)")
                    # 即使跳过也要更新区块号，避免死循环
                    self._update_last_processed_block(chain.id, block_number)
                    continue

                # 获取区块中的交易
                transactions = await self._get_block_transactions(strategy, block_number)

                # 统计交易处理情况
                total_transactions = len(transactions)
                processed_transactions = 0

                # 处理区块中的每个交易
                for tx in transactions:
                    result = await self._process_transaction(chain, tx)
                    if result:  # 如果交易被处理（不是跳过）
                        processed_transactions += 1

                # 记录处理统计
                if total_transactions > 0:
                    current_app.logger.info(
                        f"📊 区块 {block_number}: 总交易 {total_transactions}, "
                        f"相关交易 {processed_transactions}, "
                        f"过滤 {total_transactions - processed_transactions}"
                    )

                # 成功处理完区块后，更新最后处理的区块号
                self._update_last_processed_block(chain.id, block_number)

                # 每处理10个区块打印一次进度
                if block_number % 10 == 0:
                    current_app.logger.info(f"📊 {chain.chain_name} 已处理到区块 {block_number}")

            except Exception as e:
                current_app.logger.error(f"处理区块 {block_number} 失败: {e}")
                # 即使处理失败，也要更新区块号，避免死循环
                # 但可以选择性地记录错误状态
                try:
                    from app.models.blockchain_sync import BlockchainSyncStatus
                    BlockchainSyncStatus.record_error(chain.id, f"区块 {block_number} 处理失败: {str(e)}")
                    # 仍然更新区块号，避免重复处理同一个有问题的区块
                    self._update_last_processed_block(chain.id, block_number)
                except Exception as update_error:
                    current_app.logger.error(f"更新错误状态失败: {update_error}")
                    # 如果连错误记录都失败了，至少要更新区块号
                    self._update_last_processed_block(chain.id, block_number)

    async def _get_block_transactions(self, strategy, block_number: int) -> List[ChainTransaction]:
        """获取区块中的交易"""
        transactions = []

        try:
            if hasattr(strategy, 'web3'):
                # EVM 链实现
                block = strategy.web3.eth.get_block(block_number, full_transactions=True)

                for tx in block.transactions:
                    continue_status = False

                    if tx.to:  # 过滤掉合约创建交易
                        continue_status = False
                        token_address = None  # 默认设为 None（原生代币）
                        to_address = tx.to
                        amount = Decimal(str(strategy.web3.from_wei(tx.value, 'ether')))

                        # 检查是否是合约地址
                        try:
                            code = strategy.web3.eth.get_code(tx.to)
                            # 检查是否是合约地址（HexBytes 类型）
                            # 空合约代码为 HexBytes('0x') 或长度为0
                            if len(code) > 0 and code != b'\x00':
                                # 是合约地址，尝试解析代币转账
                                token_data = await self.try_evm_parse_transfer_amount(tx['input'])
                                if token_data:
                                    # 这是代币转账交易
                                    if token_data['continue'] and not current_app.config['CHAIN_EVM_TOKEN_CHECK']:
                                        continue_status = True  # 跳过不需要的合约交易类型
                                    else:
                                        token_address = tx.to  # 记录代币合约地址
                                        to_address = token_data['to']  # 实际接收地址

                                        # 获取代币精度并计算实际金额
                                        decimals_token = await self.get_evm_token_decimals(strategy.web3, token_address)
                                        current_app.logger.info(f"获取代币合约精度: {decimals_token}")
                                        amount = Decimal(token_data['amount']) / Decimal(10 ** decimals_token)
                                else:
                                    # 是合约地址但不是我们关心的代币转账，跳过
                                    continue_status = True
                            else:
                                # 不是合约地址，这是原生代币转账
                                # 检查是否有转账金额
                                if amount <= 0:
                                    continue_status = True  # 跳过没有转账金额的交易

                        except Exception as e:
                            continue_status = True
                            current_app.logger.error(f"处理交易时发生异常: {e}")

                        if continue_status:
                            continue

                        # 创建交易记录
                        chain_tx = ChainTransaction(
                            tx_hash=tx.hash.hex(),
                            from_address=tx['from'],
                            to_address=to_address,
                            amount=amount,
                            token_address=token_address,  # None表示原生代币，非None表示代币合约地址
                            block_number=block_number,
                            timestamp=block.timestamp,
                            gas_fee=Decimal(str(strategy.web3.from_wei(tx.gas * tx.gasPrice, 'ether'))),
                            status='success'  # 简化处理
                        )
                        transactions.append(chain_tx)

            elif hasattr(strategy, 'client'):
                # Solana 实现
                from solders.signature import Signature
                from datetime import datetime
                
                # 获取区块中的确认交易
                block_info = strategy.client.get_block(block_number)
                if not block_info or not block_info.value:
                    return transactions
                
                block_data = block_info.value
                block_time = datetime.fromtimestamp(block_data.block_time)
                
                # 处理区块中的每个交易
                for tx_info in block_data.transactions:
                    if not tx_info.transaction.message.instructions:
                        continue

                    # 获取交易详情
                    tx_hash = str(tx_info.transaction.signatures[0])
                    status = 'success' if tx_info.meta.err is None else 'failed'
                    
                    # 只处理成功的交易
                    if status != 'success':
                        continue
                        
                    # 解析交易信息
                    for ix in tx_info.transaction.message.instructions:
                        # 检查是否是转账指令
                        program_id = str(tx_info.transaction.message.account_keys[ix.program_id_index])
                        
                        # 系统程序转账 (SOL)
                        if program_id == "11111111111111111111111111111111":
                            # 解析转账指令
                            if len(ix.accounts) >= 2:
                                from_idx = ix.accounts[0]
                                to_idx = ix.accounts[1]

                                from_address = str(tx_info.transaction.message.account_keys[from_idx])
                                to_address = str(tx_info.transaction.message.account_keys[to_idx])

                                # 获取转账金额 (lamports)
                                pre_balances = tx_info.meta.pre_balances
                                post_balances = tx_info.meta.post_balances

                                if from_idx < len(pre_balances) and to_idx < len(pre_balances):
                                    from_delta = pre_balances[from_idx] - post_balances[from_idx]
                                    to_delta = post_balances[to_idx] - pre_balances[to_idx]

                                    amount = max(from_delta, to_delta)
                                    if amount <= 0:
                                        continue  # 没有发生实际转账

                                    # 计算手续费
                                    fee = tx_info.meta.fee

                                    # 创建交易对象
                                    chain_tx = ChainTransaction(
                                        tx_hash=tx_hash,
                                        from_address=from_address,
                                        to_address=to_address,
                                        amount=Decimal(str(amount / 10 ** 9)),  # 转换为 SOL
                                        token_address=None,  # 原生代币
                                        block_number=block_number,
                                        timestamp=block_time,
                                        gas_fee=Decimal(str(fee / 10 ** 9)),  # 转换为 SOL
                                        status=status
                                    )
                                    transactions.append(chain_tx)
                        
                        # SPL Token 转账
                        elif program_id == "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA":
                            # 这里可以添加 SPL Token 转账的处理逻辑
                            try:
                                decoded_data = base58.b58decode(ix.data)
                            except Exception:
                                current_app.logger.error(f"Base58 解析失败: {ix.data}")
                                continue

                            tag = decoded_data[0]
                            # 只记录 Transfer(3) 与 TransferChecked(12)
                            if tag not in (3, 12):
                                current_app.logger.error(f"不是转账: {tag}")
                                continue

                            # ------------- 账户索引解析 -------------
                            if tag == 3:  # Transfer
                                src_idx, dst_idx, owner_idx = ix.accounts
                                mint_idx = None  # 需要从 token 账户里获取
                            else:  # TransferChecked
                                src_idx, mint_idx, dst_idx, owner_idx = ix.accounts

                            # 地址字符串
                            src_addr = str(tx_info.transaction.message.account_keys[src_idx])
                            dst_addr = str(tx_info.transaction.message.account_keys[dst_idx])
                            owner_addr = str(tx_info.transaction.message.account_keys[owner_idx])
                            mint_addr = (str(tx_info.transaction.message.account_keys[mint_idx])
                                         if mint_idx is not None else None)

                            # ------------- 获取 decimals 与余额差 -------------
                            def _get_token_balance(index, balances):
                                for b in (balances or []):
                                    if b.account_index == index:
                                        return int(b.ui_token_amount.amount), int(b.ui_token_amount.decimals), b.mint
                                return None, None, None

                            pre_amt_src, decimals, mint_from_bal = _get_token_balance(src_idx,
                                                                                      tx_info.meta.pre_token_balances)
                            post_amt_src, _, _ = _get_token_balance(src_idx, tx_info.meta.post_token_balances)
                            pre_amt_dst, decimals_dst, mint_from_bal_dst = _get_token_balance(dst_idx,
                                                                                              tx_info.meta.pre_token_balances)
                            post_amt_dst, _, _ = _get_token_balance(dst_idx, tx_info.meta.post_token_balances)

                            # 如果 mint_idx 为 None（Transfer 指令），从余额记录里取 mint
                            if mint_addr is None:
                                mint_addr = mint_from_bal or mint_from_bal_dst

                            # 有时接收方 pre_amt_dst 为 None（第一次创建账户）——视为 0
                            pre_amt_src = pre_amt_src or 0
                            post_amt_src = post_amt_src or 0
                            pre_amt_dst = pre_amt_dst or 0
                            post_amt_dst = post_amt_dst or 0
                            decimals = decimals or decimals_dst or 0

                            # 计算实际转账量（base units）
                            amount_raw = post_amt_dst - pre_amt_dst
                            if amount_raw <= 0:  # 安全兜底
                                amount_raw = pre_amt_src - post_amt_src
                            if amount_raw <= 0:
                                continue  # 无有效金额，跳过

                            amount_token = Decimal(str(amount_raw)) / (10 ** decimals)
                            fee = tx_info.meta.fee

                            chain_tx = ChainTransaction(
                                tx_hash=tx_hash,
                                from_address=src_addr,
                                to_address=dst_addr,
                                amount=amount_token,
                                token_address=mint_addr,  # 代币合约地址
                                block_number=block_number,
                                timestamp=block_time,
                                gas_fee=Decimal(str(fee / 10 ** 9)),
                                status=status
                            )
                            transactions.append(chain_tx)

        except Exception as e:
            current_app.logger.error(f"获取区块交易失败: {e}")

        return transactions

    async def _process_transaction(self, chain: Blockchain, tx: ChainTransaction) -> bool:
        """
        处理单个交易

        Returns:
            bool: True 如果交易被处理（相关交易），False 如果交易被跳过（无关交易）
        """
        try:
            # 检查是否是系统监听的地址
            from_user_mapping = self._get_user_mapping(tx.to_address, chain.chain_code)
            to_user_mapping = self._get_user_mapping(tx.from_address, chain.chain_code)

            # 只有涉及系统用户的交易才需要处理和记录
            if not from_user_mapping and not to_user_mapping:
                # 不是系统用户的交易，直接跳过，不入库
                current_app.logger.debug(f"跳过非系统用户交易: {tx.tx_hash}")
                return False

            # 记录交易到同步日志（只记录相关交易）
            await self._log_transaction(chain, tx)

            if from_user_mapping and to_user_mapping:
                # 是内部交易，记录但标记为内部交易
                current_app.logger.info(f"检测到内部交易: {tx.tx_hash}")
                await self._mark_transaction_internal(chain, tx)
                return True

            if to_user_mapping:
                # 这是用户的充值交易
                await self._handle_deposit_transaction(to_user_mapping, tx, chain)
                return True

            if from_user_mapping:
                # 这是用户的提现交易
                await self._handle_withdrawal_transaction(from_user_mapping, tx, chain)
                return True

            return False

        except Exception as e:
            current_app.logger.error(f"处理交易 {tx.tx_hash} 失败: {e}")
            # 只有已经记录的交易才标记为失败
            if 'from_user_mapping' in locals() or 'to_user_mapping' in locals():
                if from_user_mapping or to_user_mapping:
                    await self._mark_transaction_failed(chain, tx, str(e))
            return False

    def _get_user_mapping(self, address: str, chain_code: str) -> Optional[AddressMapping]:
        """获取地址映射"""
        return AddressMapping.query.filter_by(
            address=address.lower(),
            chain_type=chain_code.lower()
        ).first()

    async def _handle_deposit_transaction(self, user_mapping: AddressMapping,
                                          tx: ChainTransaction, chain: Blockchain):
        """处理充值交易"""
        try:
            # 获取资产类型ID
            asset_type_id = self._get_asset_type_id(chain.code, tx.token_address)

            if not asset_type_id:
                current_app.logger.error(f"⚠️  未找到资产类型: chain={chain.code}, token={tx.token_address}")
                return

            # 检查是否已经处理过
            existing_flow = db.session.query(
                TradeFlow
            ).filter_by(
                reference_id=tx.tx_hash,
                user_id=user_mapping.user_id,
                asset_type_id=asset_type_id,
                flow_type=TradeFlowTypeEnum.DEPOSIT
            ).first()

            if existing_flow:
                current_app.logger.info(f"交易 {tx.tx_hash} 已处理过，跳过")
                return

            # 检查交易同步日志
            from app.models.blockchain_sync import TransactionSyncLog
            sync_log = TransactionSyncLog.query.filter_by(
                tx_hash=tx.tx_hash,
                chain_id=chain.id,
                sync_status='PROCESSED'
            ).first()

            if sync_log:
                current_app.logger.info(f"交易 {tx.tx_hash} 已在同步日志中标记为处理过，跳过")
                return

            # 记录充值流水
            flow = SettlementService.record_trade_flow(
                user_id=user_mapping.user_id,
                asset_type_id=asset_type_id,
                flow_type=TradeFlowTypeEnum.DEPOSIT,
                amount=tx.amount,
                price=None,
                fee_amount=tx.gas_fee,
                reference_id=tx.tx_hash,
                remark=f"链上充值 - {chain.chain_name}"
            )

            # 更新用户链上资产余额
            await self._update_user_asset_after_deposit(
                user_mapping.user_id,
                asset_type_id,
                user_mapping.address,
                tx.token_address,
                chain
            )

            db.session.commit()

            current_app.logger.info(f"✓ 处理充值: 用户={user_mapping.user_id}, 金额={tx.amount}, 交易={tx.tx_hash}")

        except Exception as e:
            current_app.logger.error(f"处理充值交易失败: {e}")
            db.session.rollback()

    async def _handle_withdrawal_transaction(self, user_mapping: AddressMapping,
                                             tx: ChainTransaction, chain: Blockchain):
        """处理提现交易"""
        try:
            # 获取资产类型ID
            asset_type_id = self._get_asset_type_id(chain.code, tx.token_address)

            if not asset_type_id:
                current_app.logger.error(f"⚠️  未找到资产类型: chain={chain.code}, token={tx.token_address}")
                return

            # 查找对应的结算记录
            from app.models.settlement import UserSettlement, SettlementStatusEnum

            settlement = UserSettlement.query.filter_by(
                user_id=user_mapping.user_id,
                status=SettlementStatusEnum.PROCESSING
            ).filter(
                UserSettlement.withdraw_amount == tx.amount
            ).first()

            if settlement:
                # 完成结算
                SettlementService.complete_settlement(settlement.id)

                # 更新结算记录的交易哈希
                settlement.extend_field = settlement.extend_field or {}
                settlement.extend_field['tx_hash'] = tx.tx_hash
                settlement.extend_field['block_number'] = tx.block_number

                # 更新用户链上资产余额
                await self._update_user_asset_after_withdrawal(
                    user_mapping.user_id,
                    asset_type_id,
                    user_mapping.address,
                    tx.token_address,
                    chain
                )

                db.session.commit()

                current_app.logger.info(f"✓ 完成提现结算: 用户={user_mapping.user_id}, 金额={tx.amount}, 交易={tx.tx_hash}")
            else:
                current_app.logger.info(f"⚠️  未找到对应的提现结算记录: 用户={user_mapping.user_id}, 金额={tx.amount}")

        except Exception as e:
            current_app.logger.error(f"处理提现交易失败: {e}")
            db.session.rollback()

    def _get_asset_type_id(self, chain_code: str, token_address: Optional[str]) -> Optional[int]:
        """获取资产类型ID"""
        try:
            from app.models.blockchain import AssetType, AssetTypeEnum

            if token_address:
                # ERC20 代币
                asset_type = AssetType.query.filter_by(
                    type=AssetTypeEnum.TOKEN,
                    chain_type=chain_code,
                    # 这里需要根据 token_address 查找对应的 token_id
                ).first()
            else:
                # 原生代币
                asset_type = AssetType.query.filter_by(
                    type=AssetTypeEnum.TOKEN,
                    chain_type=chain_code,
                    # 原生代币的标识
                ).first()

            return asset_type.id if asset_type else None

        except Exception as e:
            current_app.logger.error(f"获取资产类型ID失败: {e}")
            return None

    def _get_last_processed_block(self, chain_id: int) -> int:
        """获取最后处理的区块号 - 实现回溯功能"""
        try:
            from app.models.blockchain_sync import BlockchainSyncStatus

            sync_status = BlockchainSyncStatus.query.filter_by(chain_id=chain_id).first()

            if sync_status and sync_status.last_processed_block:
                current_app.logger.info(f"📍 {chain_id} 链从区块 {sync_status.last_processed_block} 继续同步")
                return sync_status.last_processed_block
            else:
                # 首次启动，从较近的区块开始（避免同步太多历史数据）
                # 跳过创世区块（block 0），从 block 1 开始
                current_app.logger.info(f"🆕 {chain_id} 链首次启动，从 block 1 开始同步（跳过创世区块）")
                return 0  # 返回 0，这样 start_block 会被设置为 max(0 + 1, 1) = 1

        except Exception as e:
            current_app.logger.error(f"获取最后处理区块号失败: {e}")
            return 0

    def _update_last_processed_block(self, chain_id: int, block_number: int):
        """更新最后处理的区块号 - 持久化存储"""
        try:
            from app.models.blockchain_sync import BlockchainSyncStatus

            # 使用模型的类方法更新
            BlockchainSyncStatus.update_processed_block(chain_id, block_number)

            # 每处理100个区块打印一次进度
            if block_number % 100 == 0:
                current_app.logger.info(f"📊 链 {chain_id} 已处理到区块 {block_number}")

        except Exception as e:
            current_app.logger.error(f"更新最后处理区块号失败: {e}")
            # 记录错误到数据库
            try:
                from app.models.blockchain_sync import BlockchainSyncStatus
                BlockchainSyncStatus.record_error(chain_id, f"更新区块号失败: {str(e)}")
            except:
                pass

    async def _log_transaction(self, chain: Blockchain, tx: ChainTransaction):
        """记录交易到同步日志"""
        try:
            from app.models.blockchain_sync import TransactionSyncLog
            from datetime import datetime

            # 检查是否已经记录过
            existing_log = TransactionSyncLog.query.filter_by(
                tx_hash=tx.tx_hash,
                chain_id=chain.id
            ).first()

            if not existing_log:
                sync_log = TransactionSyncLog(
                    chain_id=chain.id,
                    tx_hash=tx.tx_hash,
                    block_number=tx.block_number,
                    from_address=tx.from_address,
                    to_address=tx.to_address,
                    amount=tx.amount,
                    token_address=tx.token_address,
                    gas_fee=tx.gas_fee,
                    sync_status='PENDING',
                    sync_time=datetime.utcnow()
                )
                db.session.add(sync_log)
                db.session.commit()

        except Exception as e:
            current_app.logger.error(f"记录交易日志失败: {e}")

    async def _mark_transaction_internal(self, chain: Blockchain, tx: ChainTransaction):
        """标记交易为内部交易"""
        try:
            from app.models.blockchain_sync import TransactionSyncLog

            sync_log = TransactionSyncLog.query.filter_by(
                tx_hash=tx.tx_hash,
                chain_id=chain.id
            ).first()

            if sync_log:
                sync_log.sync_status = 'INTERNAL'
                db.session.commit()

        except Exception as e:
            current_app.logger.error(f"标记内部交易失败: {e}")

    async def _mark_transaction_ignored(self, chain: Blockchain, tx: ChainTransaction):
        """标记交易为忽略"""
        try:
            from app.models.blockchain_sync import TransactionSyncLog

            sync_log = TransactionSyncLog.query.filter_by(
                tx_hash=tx.tx_hash,
                chain_id=chain.id
            ).first()

            if sync_log:
                sync_log.sync_status = 'IGNORED'
                db.session.commit()

        except Exception as e:
            current_app.logger.error(f"标记交易忽略失败: {e}")

    async def _mark_transaction_failed(self, chain: Blockchain, tx: ChainTransaction, error_msg: str):
        """标记交易处理失败"""
        try:
            from app.models.blockchain_sync import TransactionSyncLog

            sync_log = TransactionSyncLog.query.filter_by(
                tx_hash=tx.tx_hash,
                chain_id=chain.id
            ).first()

            if sync_log:
                sync_log.sync_status = 'FAILED'
                sync_log.error_message = error_msg
                db.session.commit()

        except Exception as e:
            current_app.logger.error(f"标记交易失败状态失败: {e}")

    async def _update_user_asset_after_deposit(self, user_id: int, asset_type_id: int,
                                               address: str, token_address: Optional[str],
                                               chain: Blockchain):
        """充值后更新用户链上资产余额"""
        try:
            from app.models.asset import UserAsset

            # 获取链上实际余额
            chain_balance = await self._get_chain_balance(address, token_address, chain)

            # 获取或创建用户资产记录
            user_asset = UserAsset.query.filter_by(
                user_id=user_id,
                asset_type_id=asset_type_id
            ).first()

            if not user_asset:
                # 创建新的用户资产记录
                user_asset = UserAsset(
                    user_id=user_id,
                    asset_type_id=asset_type_id,
                    available_balance=Decimal('0'),
                    frozen_balance=Decimal('0'),
                    occupied_balance=Decimal('0')
                )
                db.session.add(user_asset)

            # 更新余额：链上资产 - 占用资产
            new_balance = chain_balance - user_asset.occupied_balance
            old_balance = user_asset.available_balance

            user_asset.available_balance = new_balance

            current_app.logger.info(
                f"💰 充值更新用户资产: 用户={user_id}, 资产类型={asset_type_id}, "
                f"余额 {old_balance} → {new_balance}, 链上余额={chain_balance}, 占用={user_asset.occupied_balance}"
            )

        except Exception as e:
            current_app.logger.error(f"更新充值后用户资产失败: {e}")
            raise

    async def _update_user_asset_after_withdrawal(self, user_id: int, asset_type_id: int,
                                                  address: str, token_address: Optional[str],
                                                  chain: Blockchain):
        """提现后更新用户链上资产余额"""
        try:
            from app.models.asset import UserAsset

            # 获取链上实际余额
            chain_balance = await self._get_chain_balance(address, token_address, chain)

            # 获取用户资产记录
            user_asset = UserAsset.query.filter_by(
                user_id=user_id,
                asset_type_id=asset_type_id
            ).first()

            if not user_asset:
                current_app.logger.error(f"⚠️  用户资产记录不存在: 用户={user_id}, 资产类型={asset_type_id}")
                return

            # 更新余额：链上资产 - 占用资产
            new_balance = chain_balance - user_asset.occupied_balance
            old_balance = user_asset.available_balance

            user_asset.available_balance = new_balance

            current_app.logger.info(
                f"💸 提现更新用户资产: 用户={user_id}, 资产类型={asset_type_id}, "
                f"余额 {old_balance} → {new_balance}, 链上余额={chain_balance}, 占用={user_asset.occupied_balance}"
            )

        except Exception as e:
            current_app.logger.error(f"更新提现后用户资产失败: {e}")
            raise

    async def _get_chain_balance(self, address: str, token_address: Optional[str],
                                 chain: Blockchain) -> Decimal:
        """获取链上资产余额"""
        try:
            # 根据链类型获取对应的策略
            strategy = self.factory.get_strategy(chain)
            if not strategy:
                raise ValueError(f"未找到链 {chain.id} 的策略")

            if hasattr(strategy, 'web3'):
                # EVM 链
                if token_address:
                    # ERC20 代币余额
                    balance_wei = await self._get_erc20_balance(strategy.web3, token_address, address)
                    decimals = await self.get_evm_token_decimals(strategy.web3, token_address)
                    return Decimal(str(balance_wei)) / Decimal(10 ** decimals)
                else:
                    # 原生代币余额
                    balance_wei = strategy.web3.eth.get_balance(address)
                    return Decimal(str(strategy.web3.from_wei(balance_wei, 'ether')))

            elif hasattr(strategy, 'client'):
                # Solana 链
                if token_address:
                    # SPL Token 余额
                    # TODO: 实现 SPL Token 余额查询
                    return Decimal('0')
                else:
                    # SOL 余额
                    balance_response = strategy.client.get_balance(address)
                    if balance_response.value:
                        return Decimal(str(balance_response.value / 10 ** 9))  # lamports to SOL
                    return Decimal('0')

            return Decimal('0')

        except Exception as e:
            current_app.logger.error(f"获取链上余额失败: {e}")
            return Decimal('0')

    async def _get_erc20_balance(self, web3, token_address: str, account_address: str) -> int:
        """获取 ERC20 代币余额"""
        try:
            # 简化的 ERC20 ABI，只包含 balanceOf 方法
            erc20_abi = [
                {
                    "constant": True,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                }
            ]

            contract = web3.eth.contract(address=token_address, abi=erc20_abi)
            balance = contract.functions.balanceOf(account_address).call()
            return balance

        except Exception as e:
            current_app.logger.error(f"获取 ERC20 余额失败: {e}")
            return 0

    def stop_all_listeners(self):
        """停止所有监听器"""
        current_app.logger.error("🛑 停止区块链监听服务...")
        self.is_running = False

    async def try_evm_parse_transfer_amount(self, tx_input):
        input_data = HexBytes(tx_input)
        method_sig = '0x' + input_data[:4].hex()
        current_app.logger.error(f"method_sig: {str(method_sig)}")
        params_data = input_data[4:]
        KNOWN_METHODS = {
            "0xa9059cbb": ("ERC20.transfer", ['address', 'uint256']),
            "0x23b872dd": ("ERC20/721.transferFrom", ['address', 'address', 'uint256']),
            "0xf242432a": ("ERC1155.safeTransferFrom", ['address', 'address', 'uint256', 'uint256', 'bytes']),
            "0x40c10f19": ("ERC20.mint", ['address', 'uint256']),
        }

        if method_sig in KNOWN_METHODS:
            method_name, param_types = KNOWN_METHODS[method_sig]
            decoded = abi_decode(param_types, params_data)

            if method_name == "ERC20.transfer":
                return {
                    "method": method_name,
                    "to": decoded[0],
                    "amount": int(decoded[1]),
                    "continue": False
                }
            elif method_name == "ERC20/721.transferFrom":
                return {
                    "method": method_name,
                    "from": decoded[0],
                    "to": decoded[1],
                    "token_id": int(decoded[2]),
                    "continue": False
                }
            elif method_name == "ERC1155.safeTransferFrom":
                return {
                    "method": method_name,
                    "from": decoded[0],
                    "to": decoded[1],
                    "token_id": int(decoded[2]),
                    "amount": int(decoded[3]),
                    "continue": False
                }
            elif method_name == "ERC20.mint":
                return {
                    "method": method_name,
                    "to": decoded[0],  # 被铸币的地址
                    "amount": int(decoded[1]),
                    "continue": True  # 主动跳过记录
                }

        return {
                    "continue": True  # 主动跳过记录
                }

    async def get_evm_token_decimals(self, web3, token_address: str) -> int:
        try:
            token_address = to_checksum_address(token_address)
            decimals_selector = "0x313ce567"  # keccak("decimals()")[:4]
            data = {
                "to": token_address,
                "data": decimals_selector
            }
            raw = web3.eth.call(data)

            if raw and len(raw) >= 32:
                return int.from_bytes(raw[-32:], byteorder='big')
            else:
                current_app.logger.error(f"[ERROR] 合约 {token_address} decimals() 返回异常: {raw}")
                return 18  # 默认 fallback 精度
        except Exception as e:
            current_app.logger.error(f"[ERROR] 获取合约 {token_address} decimals() 异常: {e}")
            return 18