import base64
import json
import secrets
from collections import defaultdict
from decimal import Decimal
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, List, Any

from flask import current_app
from mnemonic import Mnemonic

from app.middlewares.web3.consts import TransactionStatus
from app.middlewares.web3.price_service import PriceService
from app.middlewares.web3.web3_facade import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.models import db
from app.models.asset import UserAsset
from app.models.blockchain import Blockchain, Token, AssetType, AssetTypeEnum, TokenType
from app.models.wallet import WalletGroup, WalletGenerated, WalletLog
from app.utils.enc_port import EncPort
from app.utils.asset import format_decimal_by_precision


class WalletService:

    @staticmethod
    def get_wallet_groups(user_id: int, device_id: int, page: int, per_page: int, chain_code: Optional[str] = None,
                          exist_balance: bool = False, token_code: Optional[str] = None) -> Dict[str, Any]:
        """Get wallet groups with balances

        Args:
            user_id: User ID
            device_id: Device ID
            page: Page number
            per_page: Items per page
            chain_code: Optional chain code filter
            exist_balance: Only return wallets with balance
            token_code: Optional token code filter

        Returns:
            Dict containing paginated wallet groups with balances
        """
        # 查询钱包组
        query = (db.session.query(WalletGroup, WalletGenerated)
                 .join(WalletGenerated, WalletGroup.id == WalletGenerated.group_id)
                 .filter(WalletGroup.user_id == user_id))

        if chain_code:
            query = query.filter(WalletGenerated.chain_code == chain_code)
        if device_id:
            query = query.filter(WalletGroup.device_id == device_id)

        # 执行分页查询
        from sqlalchemy import func
        total = query.count()
        items = query.offset((page - 1) * per_page).limit(per_page).all()

        # 创建一个简单的分页对象
        class SimplePagination:
            def __init__(self, items, total, page, per_page):
                self.items = items
                self.total = total
                self.page = page
                self.per_page = per_page
                self.pages = (total + per_page - 1) // per_page

        pagination = SimplePagination(items, total, page, per_page)
        total_balance_usd = Decimal(0)
        # 获取指定代币（如果有）
        target_token = None
        if token_code:
            target_token = Token.query.filter_by(token_code=token_code).first()

        # 构建返回数据结构
        groups_dict = defaultdict(lambda: {'group_id': None, 'wallets': []})
        for group, wallet in pagination.items:
            groups_dict[group.id]['group_id'] = group.id

            try:
                chain = Blockchain.query.filter_by(chain_code=wallet.chain_code).first()
                if not chain:
                    continue

                # 如果指定了代币，只获取该代币的余额
                if target_token and target_token.chain_id != chain.id:
                    continue

                # 优化1: 先获取资产类型列表，查询其中是token类型的
                asset_types_query = db.session.query(AssetType).filter(
                    AssetType.type == AssetTypeEnum.TOKEN
                )
                
                # 如果指定了代币，只获取该代币对应的资产类型
                if target_token:
                    asset_types_query = asset_types_query.filter(AssetType.token_id == target_token.id)
                else:
                    # 获取该链上的所有代币
                    chain_tokens = Token.query.filter_by(chain_id=chain.id).all()
                    token_ids = [token.id for token in chain_tokens]
                    asset_types_query = asset_types_query.filter(AssetType.token_id.in_(token_ids))
                
                asset_types = asset_types_query.all()
                
                for asset_type in asset_types:
                    # 获取对应的token
                    token = Token.query.get(asset_type.token_id)
                    if not token:
                        continue
                    
                    # 优化2: 查询用户在该资产类型上的内部余额
                    user_asset = db.session.query(UserAsset).filter(
                        UserAsset.user_id == user_id,
                        UserAsset.asset_type_id == asset_type.id
                    ).first()

                    # 如果有用户资产记录，直接使用记录中的余额
                    if user_asset:
                        balance = user_asset.available_balance
                    else:
                        balance = Decimal(0)

                    if exist_balance and not balance:
                        continue
                    token_price = PriceService.get_token_price(token)
                    token_balance = Decimal(balance) / Decimal(10 ** token.decimals)
                    balance_usd = token_price * token_balance
                    total_balance_usd += balance_usd
                    wallet_info = {
                        'chain_code': wallet.chain_code,
                        'chain_name': chain.chain_name,
                        'wallet_id': wallet.id,
                        'wallet_address': wallet.address,
                        'balance': format_decimal_by_precision(token_balance, token.decimals),
                        'balance_usd': format_decimal_by_precision(balance_usd, 2),  # USD 通常保留2位小数
                        'token_price': format_decimal_by_precision(token_price, 8),  # 价格通常保留8位小数
                        'token_name': token.token_name,
                        'token_code': token.token_symbol,
                        'token_id': token.id,
                        'is_native': token.token_type == "NATIVE",
                        'asset_type_id': asset_type.id
                    }
                    groups_dict[group.id]['wallets'].append(wallet_info)
            except Exception as e:
                current_app.logger.error(f'Failed to get balance for {wallet.address}: {str(e)}', stack_info=True)

        return {
            'items': list(groups_dict.values()),
            'total_balance_usd': format_decimal_by_precision(total_balance_usd, 2),
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page
        }

    @staticmethod
    def create_wallet_group(user_id: int, device_id: int) -> Tuple[Optional[dict], Optional[str]]:
        """创建钱包组并返回组信息"""
        try:
            exist_count = WalletGroup.query.filter_by(user_id=user_id, device_id=device_id).count()
            if exist_count > 0:
                return None, "请勿重复创建钱包"

            # 生成助记词（BIP39）
            mnemonic = mnemo.generate(128)
            seed = mnemo.to_seed(mnemonic)
            nonce_bytes = secrets.token_bytes(32)
            nonce = str(int.from_bytes(nonce_bytes, byteorder='big'))
            new_group = WalletGroup(
                user_id=user_id,
                device_id=device_id,
                name=f'wallet{exist_count + 1}',
                nonce=nonce
            )

            # 获取所有区块链
            blockchains = Blockchain.query.all()
            wallet_generated_list = []
            for blockchain in blockchains:
                web3_facade = Web3Facade.get_instance()
                private_key, wallet_address = web3_facade.generate_wallet(blockchain, seed, nonce_bytes)
                wallet_generated = WalletGenerated(
                    user_id=user_id,
                    address=wallet_address,
                    encrypt_key=base64.b64encode(enc_port.encrypt(private_key.encode('UTF-8'))).decode('UTF-8'),
                    chain_code=blockchain.chain_code,
                )
                wallet_generated_list.append(wallet_generated)

            # 创建钱包组记录&钱包记录
            with db.session.begin_nested():
                db.session.add(new_group)
                db.session.flush()
                for wallet_generated in wallet_generated_list:
                    wallet_generated.group_id = new_group.id
                db.session.add_all(wallet_generated_list)
            db.session.commit()

            response_data = {
                'group_id': new_group.id,
                'group_name': new_group.name,
                'device_id': new_group.device_id,
                'wallets': [
                    {
                        "id": wallet.id,
                        "wallet_address": wallet.address,
                        "chain_code": wallet.chain_code
                    }
                    for wallet in wallet_generated_list
                ]
            }
            return response_data, None

        except Exception as e:
            current_app.logger.error(f'Create wallet group failed: {str(e)}')
            raise

    @staticmethod
    def is_valid_address(address: str, chain: Blockchain) -> bool:
        web3_facade = Web3Facade.get_instance()
        return web3_facade.is_valid_address(address, chain)

    @staticmethod
    def estimate_transfer(
            chain: Blockchain,
            sender_address: str,
            receiver_address: str,
            value: Decimal,
            token_code: Optional[str] = None
    ) -> dict:
        """
        Estimate gas fees and transfer time for a blockchain transaction.

        Args:
            chain: Blockchain enum representing the chain
            sender_address: Sender's wallet address
            receiver_address: Receiver's wallet address
            value: Amount to transfer
            token_code: Optional token ID for token transfers (if None, native token is used)

        Returns:
            dict: Dictionary containing gas estimation details
        """
        # Get token if specified
        token = None
        if token_code:
            token = Token.query.filter_by(token_code=token_code).first()
            if not token:
                raise ValueError(f"Token not found: {token_code}")
            if token.chain_id != chain.id:
                raise ValueError(f"Token {token_code} is not on chain {chain.chain_code}")
        else:
            # Use native token if none specified
            token = Token.query.filter_by(chain_id=chain.id, token_type="NATIVE").first()
            if not token:
                raise ValueError(f"No native token defined for chain {chain.chain_code}")

        amount = int(Decimal(value) * (10 ** token.decimals))
        # Get gas estimation from Web3Facade
        web3_facade = Web3Facade.get_instance()
        gas_estimation = web3_facade.get_gas_estimate(
            token=token,
            sender_address=sender_address,
            receiver_address=receiver_address,
            amount=amount
        )

        native_token = Token.query.filter_by(chain_id=chain.id, token_type="NATIVE").first()
        if not native_token:
            raise ValueError(f"No native token found for chain {chain.chain_code}")
        total_gas_wei = gas_estimation.gas_price * gas_estimation.gas_limit
        total_gas = Decimal(total_gas_wei) / Decimal(10 ** native_token.decimals)
        token_price = PriceService.get_token_price(native_token)
        gas_price_usd = Decimal(total_gas) * token_price
        return {
            "token_code": token.token_symbol,
            "gas_price": str(gas_estimation.gas_price),
            "gas_limit": str(gas_estimation.gas_limit),
            "total_gas_wei": str(total_gas_wei),
            "total_gas": format_decimal_by_precision(total_gas, native_token.decimals),
            "total_gas_usd": format_decimal_by_precision(gas_price_usd, 2),
            "estimate_transfer_seconds": str(gas_estimation.transfer_seconds)
        }

    @staticmethod
    def create_transfer(
            user_id: int,
            chain: Blockchain,
            wallet_id: int,
            sender_address: str,
            receiver_address: str,
            value: Decimal,
            token_code: Optional[str] = None
    ) -> dict:
        """
        Create a blockchain transaction and record it in wallet log.

        Args:
            user_id: ID of the user
            chain: Blockchain enum representing the chain
            wallet_id: ID of the wallet
            sender_address: Sender's wallet address
            receiver_address: Receiver's wallet address
            value: Amount to transfer
            token_code: Optional token ID for token transfers (if None, native token is used)

        Returns:
            dict: Dictionary containing transaction hash
        """
        # Get wallet's private key
        wallet = WalletGenerated.query.filter_by(
            id=wallet_id,
            user_id=user_id,
            chain_code=chain.chain_code,
            address=sender_address
        ).first()

        if not wallet:
            raise ValueError("Wallet not found")
        encrypt_key_bytes = base64.b64decode(wallet.encrypt_key)
        private_key = enc_port.decrypt(encrypt_key_bytes).decode('UTF-8')

        # Get token if specified
        token = None
        if token_code:
            token = Token.query.filter_by(token_code=token_code).first()
            if not token:
                raise ValueError(f"Token not found: {token_code}")
            if token.chain_id != chain.id:
                raise ValueError(f"Token {token_code} is not on chain {chain.chain_code}")
        else:
            # Use native token if none specified
            token = Token.query.filter_by(chain_id=chain.id, token_type="NATIVE").first()
            if not token:
                raise ValueError(f"No native token defined for chain {chain.chain_code}")

        amount = int(Decimal(value) * (10 ** token.decimals))
        # Create transaction
        web3_facade = Web3Facade.get_instance()
        tx_result = web3_facade.create_transfer(
            token=token,
            sender_address=sender_address,
            receiver_address=receiver_address,
            amount=amount,
            private_key=private_key
        )

        data = json.dumps(
            {"token_id": token.id, "from_address": sender_address, "to_address": receiver_address,
             "token_amount": amount})
        # Record in wallet log
        wallet_log = WalletLog(
            wallet_id=wallet_id,
            wallet_address=sender_address,
            chain_code=chain.chain_code,
            tx_hash=tx_result,
            operation='transfer',
            data=data
        )

        try:
            db.session.add(wallet_log)
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"Failed to record wallet log: {str(e)}")
            db.session.rollback()
            raise

        return {
            "tx_hash": tx_result
        }

    @staticmethod
    def get_transaction_status(tx_hash: str) -> Optional[Dict[str, Any]]:
        """
        Get transaction status and details from wallet log and blockchain.

        Args:
            tx_hash: Transaction hash

        Returns:
            Optional[Dict]: Transaction details if found, None otherwise
        """
        # Get transaction from wallet log
        wallet_log = WalletLog.query.filter_by(tx_hash=tx_hash).first()
        if not wallet_log:
            return None

        # Get chain from wallet log
        chain = Blockchain.query.filter_by(chain_code=wallet_log.chain_code).first()
        if not chain:
            return None

        # Get token info if available
        data_json = json.loads(wallet_log.data)
        token_id = data_json['token_id']
        token = Token.query.get(token_id)
        if not token:
            return None
        transfer_value = Decimal(data_json["token_amount"]) / Decimal(10 ** token.decimals)

        # Get transaction details from token
        web3_facade = Web3Facade.get_instance()
        tx_info = web3_facade.get_transaction(token, tx_hash)
        if not tx_info:
            return {
                "tx_hash": tx_hash,
                "status": TransactionStatus.PENDING,
                "from_address": wallet_log.wallet_address,
                "to_address": data_json['to_address'],
                "chain_code": chain.chain_code,
                "token_code": token.token_symbol,
                "token_name": token.token_name,
                "value": format_decimal_by_precision(transfer_value, token.decimals)
            }

        native_token = Token.query.filter_by(chain_id=chain.id, token_type="NATIVE").first()
        if not native_token:
            return None
        total_gas_wei = tx_info.gas_price * tx_info.gas_limit
        total_gas = Decimal(total_gas_wei) / Decimal(10 ** native_token.decimals)
        token_price = PriceService.get_token_price(native_token)
        gas_price_usd = token_price * total_gas
        result = {
            "tx_hash": tx_hash,
            "status": tx_info.status,
            "from_address": wallet_log.wallet_address,
            "to_address": tx_info.to_address,
            "gas_price": str(tx_info.gas_price),
            "gas_limit": str(tx_info.gas_limit),
            "total_gas": format_decimal_by_precision(total_gas, native_token.decimals),
            "total_gas_wei": str(total_gas_wei),
            "total_gas_usd": format_decimal_by_precision(gas_price_usd, 2),
            "timestamp": tx_info.timestamp,
            "chain_code": chain.chain_code,
            "token_code": token.token_symbol,
            "token_name": token.token_name,
            "value": format_decimal_by_precision(transfer_value, token.decimals)
        }

        return result

    @staticmethod
    def get_supported_tokens(chain_code: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of supported tokens, optionally filtered by chain.

        Args:
            chain_code: Optional chain code to filter tokens

        Returns:
            List of token information dictionaries
        """
        tokens = []

        if chain_code:
            chain = Blockchain.query.filter_by(chain_code=chain_code).first()
            if not chain:
                return []

            chain_tokens = Token.query.filter_by(chain_id=chain.id).all()
            for token in chain_tokens:
                tokens.append({
                    "id": token.id,
                    "symbol": token.token_symbol,
                    "name": token.token_name,
                    "chain_code": chain.chain_code,
                    "decimals": token.decimals,
                    "is_native": token.token_type == "NATIVE",
                    "contract_address": token.contract_address if token.token_type != "NATIVE" else None
                })
        else:
            # Get all tokens
            all_tokens = Token.query.all()
            for token in all_tokens:
                chain = Blockchain.query.get(token.chain_id)
                tokens.append({
                    "id": token.id,
                    "symbol": token.token_symbol,
                    "name": token.token_name,
                    "chain_code": chain.chain_code if chain else None,
                    "decimals": token.decimals,
                    "is_native": token.token_type == "NATIVE",
                    "contract_address": token.contract_address if token.token_type != "NATIVE" else None
                })

        return tokens

    @staticmethod
    def sign_message(user_id: int, message: str, wallet_address: str, chain_code: str):
        chain = Blockchain.query.filter_by(chain_code=chain_code).first()
        if not chain:
            return None, 'Invalid chain code'
        wallet = WalletGenerated.query.filter_by(
            user_id=user_id,
            chain_code=chain_code,
            address=wallet_address
        ).first()
        if not wallet:
            return None, 'Wallet not found'
        encrypt_key_bytes = base64.b64decode(wallet.encrypt_key)
        private_key = enc_port.decrypt(encrypt_key_bytes).decode('UTF-8')
        web3_facade = Web3Facade.get_instance()
        return web3_facade.sign_message(message, private_key, chain), None

    @staticmethod
    def get_or_create_wallets(user_id: int, device_id: int = 0):
        # 查询用户是否有钱包组
        wallet_group = WalletGroup.query.filter_by(
            user_id=user_id,
            device_id=device_id  # 使用固定的device_id 0
        ).first()

        # 如果没有钱包组，创建新的
        if not wallet_group:
            wallet_data, error = WalletService.create_wallet_group(
                user_id=user_id,
                device_id=device_id  # 使用固定的device_id 0
            )
            if error:
                raise ValueError(error)
            else:
                wallet_group_id = wallet_data['group_id'] if wallet_data else None
                if not wallet_group_id:
                    raise ValueError("Failed to create wallet group")

        else:
            wallet_group_id = wallet_group.id
        # 查询钱包信息
        wallet_generated_list = (db.session.query(WalletGenerated)
                                 .filter(WalletGenerated.group_id == wallet_group_id)
                                 .all())
        wallet_items = []
        for wallet in wallet_generated_list:
            chain = Blockchain.query.filter_by(chain_code=wallet.chain_code).first()
            if not chain:
                continue
            chain_tokens = Token.query.filter_by(chain_id=chain.id).all()
            for token in chain_tokens:
                wallet_info = {
                    'chain_code': wallet.chain_code,
                    'chain_name': chain.chain_name,
                    'wallet_id': wallet.id,
                    'wallet_address': wallet.address,
                    'token_name': token.token_name,
                    'token_code': token.token_symbol,
                    'token_id': token.id,
                    'is_native': token.token_type == "NATIVE"
                }
                wallet_items.append(wallet_info)

        return {'items': wallet_items, 'total': len(wallet_items)}


mnemo = Mnemonic("english")
enc_port = EncPort.get_instance()
