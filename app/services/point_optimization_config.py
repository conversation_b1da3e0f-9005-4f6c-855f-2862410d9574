"""积分优化配置管理器

这个配置管理器控制积分系统的优化策略：
1. 双写策略控制
2. 查询数据源选择
3. 灰度发布控制
4. 性能监控开关
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from functools import wraps
import time

from app.models.base import db
from app.utils.redis_client import redis_client

logger = logging.getLogger(__name__)


class PointOptimizationConfig:
    """积分优化配置管理器"""
    
    # 配置键名
    CONFIG_KEY_PREFIX = "point_optimization"
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 双写策略
        "dual_write_enabled": False,  # 是否启用双写
        "dual_write_ratio": 0.0,      # 双写比例 (0.0-1.0)
        
        # 查询策略
        "read_from_new_system": False,  # 是否从新系统读取
        "read_ratio": 0.0,              # 新系统读取比例 (0.0-1.0)
        "fallback_enabled": True,       # 是否启用降级
        
        # 归档策略
        "auto_archive_enabled": False,  # 是否启用自动归档
        "archive_batch_size": 1000,     # 归档批次大小
        "archive_days_threshold": 30,   # 归档天数阈值
        
        # 性能监控
        "performance_monitoring": True,  # 是否启用性能监控
        "slow_query_threshold": 1.0,    # 慢查询阈值（秒）
        
        # 灰度发布
        "gradual_rollout": False,       # 是否启用灰度发布
        "rollout_percentage": 0,        # 灰度发布百分比
        
        # 安全开关
        "emergency_fallback": False,    # 紧急降级开关
        "max_error_rate": 0.05,        # 最大错误率 (5%)
    }
    
    @classmethod
    def get_config(cls, key: str = None) -> Any:
        """获取配置值"""
        try:
            # 先从 Redis 获取
            config_key = f"{cls.CONFIG_KEY_PREFIX}:config"
            config_data = redis_client.hgetall(config_key)
            
            if config_data:
                # 转换数据类型
                config = {}
                for k, v in config_data.items():
                    if isinstance(v, bytes):
                        v = v.decode('utf-8')
                    
                    # 尝试转换为合适的类型
                    if v.lower() in ('true', 'false'):
                        config[k] = v.lower() == 'true'
                    elif v.replace('.', '').isdigit():
                        config[k] = float(v) if '.' in v else int(v)
                    else:
                        config[k] = v
            else:
                config = cls.DEFAULT_CONFIG.copy()
                # 保存默认配置到 Redis
                cls._save_config_to_redis(config)
            
            if key:
                return config.get(key, cls.DEFAULT_CONFIG.get(key))
            return config
            
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            if key:
                return cls.DEFAULT_CONFIG.get(key)
            return cls.DEFAULT_CONFIG.copy()
    
    @classmethod
    def set_config(cls, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            config_key = f"{cls.CONFIG_KEY_PREFIX}:config"
            redis_client.hset(config_key, key, str(value))
            
            # 设置过期时间（24小时）
            redis_client.expire(config_key, 86400)
            
            logger.info(f"配置更新: {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
            return False
    
    @classmethod
    def update_config(cls, config_dict: Dict[str, Any]) -> bool:
        """批量更新配置"""
        try:
            config_key = f"{cls.CONFIG_KEY_PREFIX}:config"
            
            # 转换为字符串格式
            string_config = {k: str(v) for k, v in config_dict.items()}
            redis_client.hmset(config_key, string_config)
            redis_client.expire(config_key, 86400)
            
            logger.info(f"批量配置更新: {list(config_dict.keys())}")
            return True
            
        except Exception as e:
            logger.error(f"批量设置配置失败: {e}")
            return False
    
    @classmethod
    def _save_config_to_redis(cls, config: Dict[str, Any]):
        """保存配置到 Redis"""
        try:
            config_key = f"{cls.CONFIG_KEY_PREFIX}:config"
            string_config = {k: str(v) for k, v in config.items()}
            redis_client.hmset(config_key, string_config)
            redis_client.expire(config_key, 86400)
        except Exception as e:
            logger.error(f"保存配置到 Redis 失败: {e}")
    
    @classmethod
    def should_use_dual_write(cls) -> bool:
        """判断是否应该使用双写"""
        if not cls.get_config("dual_write_enabled"):
            return False
        
        ratio = cls.get_config("dual_write_ratio")
        if ratio >= 1.0:
            return True
        
        # 基于随机数判断
        import random
        return random.random() < ratio
    
    @classmethod
    def should_read_from_new_system(cls) -> bool:
        """判断是否应该从新系统读取"""
        # 紧急降级检查
        if cls.get_config("emergency_fallback"):
            return False
        
        if not cls.get_config("read_from_new_system"):
            return False
        
        # 灰度发布检查
        if cls.get_config("gradual_rollout"):
            rollout_percentage = cls.get_config("rollout_percentage")
            import random
            if random.randint(1, 100) > rollout_percentage:
                return False
        
        ratio = cls.get_config("read_ratio")
        if ratio >= 1.0:
            return True
        
        # 基于随机数判断
        import random
        return random.random() < ratio
    
    @classmethod
    def should_auto_archive(cls) -> bool:
        """判断是否应该自动归档"""
        return cls.get_config("auto_archive_enabled")
    
    @classmethod
    def get_archive_config(cls) -> Dict[str, Any]:
        """获取归档配置"""
        return {
            "batch_size": cls.get_config("archive_batch_size"),
            "days_threshold": cls.get_config("archive_days_threshold")
        }
    
    @classmethod
    def record_performance(cls, operation: str, duration: float, success: bool = True):
        """记录性能指标"""
        if not cls.get_config("performance_monitoring"):
            return
        
        try:
            # 记录到 Redis
            perf_key = f"{cls.CONFIG_KEY_PREFIX}:performance:{operation}"
            timestamp = int(time.time())
            
            # 记录性能数据
            performance_data = {
                "timestamp": timestamp,
                "duration": duration,
                "success": success
            }
            
            redis_client.lpush(perf_key, str(performance_data))
            redis_client.ltrim(perf_key, 0, 999)  # 只保留最近1000条记录
            redis_client.expire(perf_key, 86400)  # 24小时过期
            
            # 检查是否为慢查询
            slow_threshold = cls.get_config("slow_query_threshold")
            if duration > slow_threshold:
                logger.warning(f"慢查询检测: {operation} 耗时 {duration:.3f}秒")
            
        except Exception as e:
            logger.error(f"记录性能指标失败: {e}")
    
    @classmethod
    def get_performance_stats(cls, operation: str, hours: int = 1) -> Dict[str, Any]:
        """获取性能统计"""
        try:
            perf_key = f"{cls.CONFIG_KEY_PREFIX}:performance:{operation}"
            records = redis_client.lrange(perf_key, 0, -1)
            
            if not records:
                return {"total": 0, "avg_duration": 0, "success_rate": 0}
            
            # 解析记录
            import ast
            cutoff_time = time.time() - (hours * 3600)
            valid_records = []
            
            for record in records:
                try:
                    data = ast.literal_eval(record.decode('utf-8') if isinstance(record, bytes) else record)
                    if data["timestamp"] >= cutoff_time:
                        valid_records.append(data)
                except:
                    continue
            
            if not valid_records:
                return {"total": 0, "avg_duration": 0, "success_rate": 0}
            
            # 计算统计信息
            total = len(valid_records)
            avg_duration = sum(r["duration"] for r in valid_records) / total
            success_count = sum(1 for r in valid_records if r["success"])
            success_rate = success_count / total
            
            return {
                "total": total,
                "avg_duration": avg_duration,
                "success_rate": success_rate,
                "period_hours": hours
            }
            
        except Exception as e:
            logger.error(f"获取性能统计失败: {e}")
            return {"total": 0, "avg_duration": 0, "success_rate": 0}
    
    @classmethod
    def check_error_rate(cls, operation: str) -> bool:
        """检查错误率是否超过阈值"""
        try:
            stats = cls.get_performance_stats(operation, hours=1)
            max_error_rate = cls.get_config("max_error_rate")
            
            if stats["total"] < 10:  # 样本太少，不判断
                return False
            
            error_rate = 1 - stats["success_rate"]
            return error_rate > max_error_rate
            
        except Exception as e:
            logger.error(f"检查错误率失败: {e}")
            return False


def performance_monitor(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                PointOptimizationConfig.record_performance(
                    operation_name, duration, success
                )
        
        return wrapper
    return decorator


def smart_fallback(fallback_func):
    """智能降级装饰器"""
    def decorator(main_func):
        @wraps(main_func)
        def wrapper(*args, **kwargs):
            # 检查是否应该直接降级
            if PointOptimizationConfig.get_config("emergency_fallback"):
                logger.info(f"紧急降级: {main_func.__name__}")
                return fallback_func(*args, **kwargs)
            
            # 检查错误率
            if PointOptimizationConfig.check_error_rate(main_func.__name__):
                logger.warning(f"错误率过高，自动降级: {main_func.__name__}")
                return fallback_func(*args, **kwargs)
            
            try:
                return main_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"主函数执行失败，降级处理: {e}")
                if PointOptimizationConfig.get_config("fallback_enabled"):
                    return fallback_func(*args, **kwargs)
                else:
                    raise
        
        return wrapper
    return decorator
