"""
订单成交处理服务
集成K线更新、延迟结算、资产转移等功能
"""

import time
from decimal import Decimal
from typing import Dict, Optional

from flask import current_app

from app.enums.biz_enums import FixtureProjectName
from app.models import db
from app.models.blockchain import AssetType, AssetTypeEnum
from app.models.order import Order, OrderMatch, OrderStatusEnum
from app.models.wallet import WalletGenerated
from app.services.asset_service import AssetService
from app.services.kline_service import KlineService, TradeData
from app.services.settlement_service import SettlementService
from app.models.settlement import TradeFlowTypeEnum
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.services.wallet_service import WalletService
from app.utils.asset import MAKER_FEE_RATE, TAKER_FEE_RATE, calculate_order_fee, calculate_order_freeze
from app.utils.errors import BusinessException

class OrderMatchService:
    """订单成交处理服务"""

    @classmethod
    def process_order_match(cls, maker_order: Order, taker_order: Order,
                            match_price: Decimal, match_amount: Decimal) -> OrderMatch:
        """处理订单成交"""
        try:
            with db.session.begin_nested():
                # 1. 创建成交记录
                order_match = cls._create_order_match(
                    maker_order, taker_order, match_price, match_amount
                )

                maker_fee = calculate_order_fee(match_amount, match_price, MAKER_FEE_RATE, maker_order.side)
                taker_fee = calculate_order_fee(match_amount, match_price, TAKER_FEE_RATE, taker_order.side)

                # 2. 更新订单状态
                cls._update_order_status(maker_order, match_amount, match_price, maker_fee)
                cls._update_order_status(taker_order, match_amount, match_price, taker_fee)

                # 3. 处理资产转移
                cls._process_asset_transfer(
                    maker_order, taker_order, match_price, match_amount
                )

                # 4. 记录延迟结算流水
                cls._record_settlement_flows(
                    maker_order, taker_order, match_price, match_amount
                )

                # 5. 更新K线数据
                cls._update_kline_data(
                    taker_order.pair_id, match_price, match_amount,
                    maker_order.id, taker_order.id
                )

                # 6. 发送通知（异步）
                cls._send_match_notifications(order_match)

                return order_match

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"处理订单成交失败: {e}")
            raise BusinessException(f"订单成交处理失败: {str(e)}", 500)

    @classmethod
    def _create_order_match(cls, maker_order: Order, taker_order: Order,
                            match_price: Decimal, match_amount: Decimal) -> OrderMatch:
        """创建成交记录"""
        order_match = OrderMatch(
            order_id=taker_order.id,  # 主要关联吃单方
            maker_order_id=maker_order.id,
            taker_order_id=taker_order.id,
            price=match_price,
            amount=match_amount
        )

        db.session.add(order_match)
        db.session.flush()

        return order_match

    @classmethod
    def _update_order_status(cls, order: Order, match_amount: Decimal, match_price: Decimal, fee: Decimal):
        """更新订单状态"""
        # 更新已成交数量
        order.executed_amount = (order.executed_amount or Decimal('0')) + match_amount
        order.executed_value = (order.executed_value or Decimal('0')) + (match_amount * match_price)

        order.fee = (order.fee if order.fee else Decimal(0)) + fee
        # 检查是否完全成交
        if order.executed_amount >= order.original_amount:
            order.status = OrderStatusEnum.FILLED.value
        elif order.executed_amount > 0:
            order.status = OrderStatusEnum.PARTIAL.value

    @classmethod
    def _process_asset_transfer(cls, maker_order: Order, taker_order: Order,
                                match_price: Decimal, match_amount: Decimal):
        """处理资产转移"""
        try:
            # 获取交易对信息
            from app.models.blockchain import TradingPair
            trading_pair = TradingPair.query.get(maker_order.pair_id)
            if not trading_pair:
                raise BusinessException("交易对不存在", 404)

            base_asset_id = trading_pair.base_asset_id
            quote_asset_id = trading_pair.quote_asset_id
            quote_amount = match_price * match_amount

            # 计算手续费
            maker_fee = calculate_order_fee(match_amount, match_price, MAKER_FEE_RATE, maker_order.side)
            taker_fee = calculate_order_fee(match_amount, match_price, TAKER_FEE_RATE, taker_order.side)

            taker_unfreeze_amount = calculate_order_freeze(match_amount, match_price, TAKER_FEE_RATE, taker_order.side)
            maker_unfreeze_amount = calculate_order_freeze(match_amount, match_price, TAKER_FEE_RATE, maker_order.side)

            if maker_order.side.value == 'BUY':
                # Maker买入，Taker卖出
                # Maker: 支付计价货币，获得基础货币
                cls._transfer_assets(
                    from_user=maker_order.user_id,
                    to_user=taker_order.user_id,
                    asset_type_id=quote_asset_id,
                    amount=quote_amount,
                    unfreeze_amount=maker_unfreeze_amount,
                    fee=maker_fee,
                    order_id=maker_order.id,
                    order_side='BUY'
                )

                # Taker: 支付基础货币，获得计价货币
                cls._transfer_assets(
                    from_user=taker_order.user_id,
                    to_user=maker_order.user_id,
                    asset_type_id=base_asset_id,
                    amount=match_amount,
                    unfreeze_amount=taker_unfreeze_amount,
                    fee=taker_fee,
                    order_id=taker_order.id,
                    order_side='SELL'
                )
            else:
                # Maker卖出，Taker买入
                # Maker: 支付基础货币，获得计价货币
                cls._transfer_assets(
                    from_user=maker_order.user_id,
                    to_user=taker_order.user_id,
                    asset_type_id=base_asset_id,
                    amount=match_amount,
                    unfreeze_amount=maker_unfreeze_amount,
                    fee=maker_fee,
                    order_id=maker_order.id,
                    order_side='SELL'
                )

                # Taker: 支付计价货币，获得基础货币
                cls._transfer_assets(
                    from_user=taker_order.user_id,
                    to_user=maker_order.user_id,
                    asset_type_id=quote_asset_id,
                    amount=quote_amount,
                    unfreeze_amount=taker_unfreeze_amount,
                    fee=taker_fee,
                    order_id=taker_order.id,
                    order_side='BUY'
                )

        except Exception as e:
            current_app.logger.error(f"资产转移失败: {str(e)}", stack_info=True)
            raise

    @classmethod
    def _transfer_assets(cls, from_user: int, to_user: int, asset_type_id: int,
                         amount: Decimal, unfreeze_amount: Decimal, fee: Decimal, order_id: int, order_side: str):
        """执行资产转移"""

        # 执行资产解冻
        AssetService.unfreeze_asset(
            user_id=from_user,
            asset_type_id=asset_type_id,
            unfreeze_amount=unfreeze_amount,
            related_order_id=str(order_id),
            remark=f"ORDER_MATCH_{order_side}_{order_id}"
        )
        asset_type = db.session.get(AssetType, asset_type_id)

        if asset_type.type == AssetTypeEnum.POINTS and asset_type.project.name == FixtureProjectName.UBI_PROJECT_NAME:
            UbiPointsIntegrationService.add_ubi_points(
                user_id=from_user,
                points=-float(amount),
                record_type='order_match_out',
                fee=fee,
                related_id=order_id
            )

            UbiPointsIntegrationService.add_ubi_points(
                user_id=to_user,
                points=float(amount),
                record_type='order_match_in',
                related_id=order_id
            )
        elif asset_type.type == AssetTypeEnum.TOKEN:
            # 获取链、钱包、地址等信息（需根据实际业务补充获取方式）
            chain = asset_type.token.blockchain
            # 获取 from_user 的钱包信息
            from_wallet = db.session.query(WalletGenerated.address).filter(WalletGenerated.user_id == from_user)
            to_wallet = db.session.query(WalletGenerated.address).filter(WalletGenerated.user_id == to_user)

            WalletService.create_transfer(
                user_id=from_user,
                chain=chain,
                wallet_id=from_wallet.id,
                sender_address=from_wallet.address,
                receiver_address=to_wallet.address,
                value=amount,
                token_code=asset_type.token.token_code
            )

        # 扣减发送方资产
        AssetService.add_or_create_user_asset(
            user_id=from_user,
            asset_type_id=asset_type_id,
            amount=-amount,
            fee=fee,
            reference_id=str(order_id),
            remark=f"ORDER_MATCH_SEND_{order_side}_{order_id}",
        )
        # 增加接收方资产
        AssetService.add_or_create_user_asset(
            user_id=to_user,
            asset_type_id=asset_type_id,
            amount=amount,
            reference_id=str(order_id),
            remark=f"ORDER_MATCH_RECEIVE_{order_side}_{order_id}",
        )

    @classmethod
    def _record_settlement_flows(cls, maker_order: Order, taker_order: Order,
                                 match_price: Decimal, match_amount: Decimal):
        """记录延迟结算流水"""
        try:
            # 获取交易对信息
            from app.models.blockchain import TradingPair
            trading_pair = TradingPair.query.get(maker_order.pair_id)
            if not trading_pair:
                return

            maker_fee = calculate_order_fee(match_amount, match_price, MAKER_FEE_RATE, maker_order.side)
            taker_fee = calculate_order_fee(match_amount, match_price, TAKER_FEE_RATE, taker_order.side)

            # 记录Maker的交易流水
            maker_flow_type = TradeFlowTypeEnum.BUY if maker_order.side == 'BUY' else TradeFlowTypeEnum.SELL
            SettlementService.record_trade_flow(
                user_id=maker_order.user_id,
                asset_type_id=trading_pair.base_asset_id,
                flow_type=maker_flow_type,
                amount=match_amount,
                price=match_price,
                order_id=maker_order.id,
                trading_pair_id=maker_order.pair_id,
                fee_amount=maker_fee,
                remark=f"订单成交 - Maker {maker_flow_type.value}"
            )

            # 记录Taker的交易流水
            taker_flow_type = TradeFlowTypeEnum.BUY if taker_order.side == 'BUY' else TradeFlowTypeEnum.SELL
            SettlementService.record_trade_flow(
                user_id=taker_order.user_id,
                asset_type_id=trading_pair.base_asset_id,
                flow_type=taker_flow_type,
                amount=match_amount,
                price=match_price,
                order_id=taker_order.id,
                trading_pair_id=taker_order.pair_id,
                fee_amount=taker_fee,
                remark=f"订单成交 - Taker {taker_flow_type.value}"
            )

        except Exception as e:
            current_app.logger.error(f"记录结算流水失败: {e}")
            # 不抛出异常，避免影响主流程

    @classmethod
    def _update_kline_data(cls, pair_id: int, price: Decimal, amount: Decimal,
                           maker_order_id: int, taker_order_id: int):
        """更新K线数据"""
        try:
            trade_data = TradeData(
                pair_id=pair_id,
                price=price,
                amount=amount,
                timestamp=int(time.time() * 1000),
                maker_order_id=maker_order_id,
                taker_order_id=taker_order_id
            )

            # 异步更新K线数据，避免阻塞主流程
            success = KlineService.process_trade(trade_data)
            if not success:
                current_app.logger.error(f"K线数据更新失败: pair_id={pair_id}, price={price}, amount={amount}")

        except Exception as e:
            current_app.logger.error(f"更新K线数据失败: {e}")
            # 不抛出异常，避免影响主流程

    @classmethod
    def _send_match_notifications(cls, order_match: OrderMatch):
        """发送成交通知（异步）"""
        try:
            # 这里可以集成消息队列、WebSocket推送等
            # 发送给相关用户
            pass
        except Exception as e:
            current_app.logger.error(f"发送成交通知失败: {e}")

    @classmethod
    def get_match_history(cls, user_id: Optional[int] = None,
                          pair_id: Optional[int] = None,
                          page: int = 1, size: int = 20) -> Dict:
        """获取成交历史"""
        try:
            query = OrderMatch.query

            if user_id:
                # 查询用户相关的成交记录
                query = query.join(Order).filter(Order.user_id == user_id)

            if pair_id:
                # 查询特定交易对的成交记录
                query = query.join(Order).filter(Order.pair_id == pair_id)

            total = query.count()
            offset = (page - 1) * size
            matches = query.order_by(OrderMatch.created_at.desc()).offset(offset).limit(size).all()

            return {
                "records": [
                    {
                        "id": match.id,
                        "maker_order_id": match.maker_order_id,
                        "taker_order_id": match.taker_order_id,
                        "price": str(match.price),
                        "amount": str(match.amount),
                        "created_at": match.created_at.isoformat() if match.created_at else None
                    }
                    for match in matches
                ],
                "total": total,
                "page": page,
                "size": size
            }

        except Exception as e:
            current_app.logger.error(f"获取成交历史失败: {e}")
            return {"records": [], "total": 0, "page": page, "size": size}
