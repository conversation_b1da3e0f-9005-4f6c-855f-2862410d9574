import secrets
import logging
from decimal import Decimal

from sqlalchemy import func

from app.models.asset import UserAsset
from app.models.base import db
from app.models.points import TaskType, PointRecord
from app.models.user import User
from app.services.asset_service import AssetService
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.utils.errors import BusinessException
from app.utils.response import Response
from app.utils.str_helper import mask_email

# 导入优化配置管理器
from app.services.point_optimization_config import (
    PointOptimizationConfig,
    performance_monitor,
    smart_fallback
)

logger = logging.getLogger(__name__)


class PointsService:
    @staticmethod
    def generate_invite_code(user_id):
        user = User.query.get(user_id)
        if not user.invite_code:
            code = secrets.token_urlsafe(6)[:6].upper()
            while User.query.filter_by(invite_code=code).first():
                code = secrets.token_urlsafe(6)[:6].upper()
            user.invite_code = code
            db.session.commit()
        return Response.success(data={"invite_code": user.invite_code})

    @staticmethod
    def get_task_status(user_id):
        task_records = PointRecord.query.filter_by(user_id=user_id, record_type='task').all()
        completed_tasks = [tr.task_type.name for tr in task_records]
        return Response.success(data={"completed_tasks": completed_tasks})

    @staticmethod
    def add_task_points(user_id, task_type_name):
        task_type = TaskType.query.filter_by(name=task_type_name).first()
        if not task_type:
            return Response.error("任务类型不存在", code=404)

        exists = PointRecord.query.filter_by(
            user_id=user_id,
            task_type_id=task_type.id
        ).first()
        if exists:
            return Response.error("该任务已完成", code=400)

        # 使用集成服务添加积分
        success, error = UbiPointsIntegrationService.add_ubi_points(
            user_id=user_id,
            points=task_type.points,
            record_type='task',
            related_id=task_type.id
        )

        if not success:
            return Response.error(f"添加积分失败: {error}", code=500)

        return Response.success(data={"points": task_type.points})

    @staticmethod
    def add_init_invite_record(inviter_id, invitee_id):
        """
        Add a pending invitation record with 0 point.
        """
        exists = PointRecord.query.filter_by(
            invitee_id=invitee_id,
            record_type='invite'
        ).first()
        if exists:
            return Response.error("该用户已被邀请", code=400)

        # 使用集成服务添加积分记录
        success1, error1 = UbiPointsIntegrationService.add_ubi_points(
            user_id=inviter_id,
            points=0,
            record_type='invite',
            related_id=invitee_id
        )

        success2, error2 = UbiPointsIntegrationService.add_ubi_points(
            user_id=invitee_id,
            points=0,
            record_type='invite'
        )

        if not success1 or not success2:
            return Response.error(f"添加邀请记录失败: {error1 or error2}", code=500)

        return Response.success(data={"points": 0})

    @staticmethod
    def update_invite_points(inviter_id, invitee_id):
        # 查找用户邀请记录（邀请者发起邀请）
        inviter_record = PointRecord.query.filter_by(
            user_id=inviter_id,
            invitee_id=invitee_id,
            record_type='invite'
        ).first()

        # 查找用户被邀请记录（被邀请者的记录）
        invitee_record = PointRecord.query.filter_by(
            user_id=invitee_id,
            record_type='invite'
        ).first()

        if not inviter_record or not invitee_record:
            return Response.error("邀请记录不存在", code=400)

        # 如果邀请记录中points为0，则更新积分
        if inviter_record.points == 0:
            # 更新邀请者积分
            inviter_record.points = 2
            invitee_record.points = 1

            # 添加到用户资产
            asset_type = UbiPointsIntegrationService.ensure_ubi_asset_type_exists()
            AssetService.add_or_create_user_asset(
                user_id=inviter_id,
                asset_type_id=asset_type.id,
                amount=Decimal(2),
                reference_id=invitee_record.id,
                remark=f"UBI积分增加: inviter" + (f", 关联ID: {invitee_record.id}" if invitee_record else "")
            )

            AssetService.add_or_create_user_asset(
                user_id=invitee_id,
                asset_type_id=asset_type.id,
                amount=Decimal(1),
                reference_id=invitee_record.id,
                remark=f"UBI积分增加: invitee" + (f", 关联ID: {invitee_record.id}" if invitee_record else "")
            )
            db.session.commit()
            return Response.success(data={"points": 2})
        else:
            return Response.error("邀请积分已更新", code=400)

    @staticmethod
    def add_invite_points(inviter_id, invitee_id):
        exists = PointRecord.query.filter_by(
            invitee_id=invitee_id,
            record_type='invite'
        ).first()
        if exists:
            return Response.error("该用户已被邀请", code=400)

        # 使用集成服务添加积分
        success1, error1 = UbiPointsIntegrationService.add_ubi_points(
            user_id=inviter_id,
            points=100,
            record_type='invite',
            related_id=invitee_id
        )

        success2, error2 = UbiPointsIntegrationService.add_ubi_points(
            user_id=invitee_id,
            points=50,
            record_type='invite'
        )

        if not success1 or not success2:
            return Response.error(f"添加邀请积分失败: {error1 or error2}", code=500)

        return Response.success(data={"points": 100})

    @staticmethod
    def add_project_running_points(user_id, project_id):
        """
        Add points for a project that has been running continuously for 1 hour.

        Args:
            user_id: The user ID to award points to
            project_id: The id of the project that has been running

        Returns:
            Response: Success or error response
        """
        # 使用集成服务添加项目运行积分
        success, error = UbiPointsIntegrationService.add_ubi_points(
            user_id=user_id,
            points=0.01,  # 0.01 points for 1 hour of continuous running
            record_type='project',
            related_id=project_id
        )

        return success, error

    @staticmethod
    @performance_monitor("get_rank_list")
    def get_rank_list(page=1, per_page=30) -> list:
        """获取积分排行榜

        智能选择数据源：
        1. 优先使用快照数据（新系统）
        2. 降级到实时查询（旧系统）
        3. 支持配置化切换
        """

        # 智能选择数据源
        if PointOptimizationConfig.should_read_from_new_system():
            try:
                return PointsService._get_rank_list_from_snapshots(page, per_page)
            except Exception as e:
                logger.warning(f"从快照获取排名失败，降级到实时查询: {e}")
                if PointOptimizationConfig.get_config("fallback_enabled"):
                    return PointsService._get_rank_list_from_assets(page, per_page)
                else:
                    raise
        else:
            return PointsService._get_rank_list_from_assets(page, per_page)

    @staticmethod
    def _get_rank_list_from_snapshots(page=1, per_page=30) -> list:
        """从快照数据获取排名（新系统）"""
        try:
            from app.models.point_archive import PointStatisticsSnapshot

            # 获取最新的快照数据
            latest_snapshot_date = db.session.query(
                func.max(PointStatisticsSnapshot.snapshot_date)
            ).scalar()

            if not latest_snapshot_date:
                raise BusinessException("快照数据不存在")

            # 使用快照数据获取排名
            ranking_query = (db.session.query(
                User.email,
                PointStatisticsSnapshot.total_points,
                PointStatisticsSnapshot.rank_position
            ).join(
                PointStatisticsSnapshot, User.id == PointStatisticsSnapshot.user_id
            ).filter(
                PointStatisticsSnapshot.snapshot_date == latest_snapshot_date
            ).order_by(PointStatisticsSnapshot.rank_position.asc())
                             .limit(per_page)
                             .offset((page - 1) * per_page)
                             .all())

            return [{
                "email": mask_email(email),
                "points": float(points) if points else 0,
                "rank": rank,
                "data_source": "snapshot"
            } for email, points, rank in ranking_query]

        except Exception as e:
            logger.error(f"从快照获取排名失败: {e}")
            raise

    @staticmethod
    def _get_rank_list_from_assets(page=1, per_page=30) -> list:
        """从资产数据获取排名（旧系统兼容）"""
        try:
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()

            ranking_query = (db.session.query(
                User.email,
                UserAsset.available_balance
            ).join(
                UserAsset, User.id == UserAsset.user_id
            ).filter(
                UserAsset.asset_type_id == asset_type.id
            ).order_by(UserAsset.available_balance.desc())
                             .limit(per_page)
                             .offset((page - 1) * per_page)
                             .all())

            return [{
                "email": mask_email(email),
                "points": float(points) if points else 0,
                "rank": (page - 1) * per_page + idx + 1,
                "data_source": "asset"
            } for idx, (email, points) in enumerate(ranking_query)]

        except Exception as e:
            logger.error(f"从资产获取排名失败: {e}")
            return []

    @staticmethod
    @performance_monitor("get_user_rank")
    def get_user_rank(user_id: int):
        """获取用户排名信息

        智能选择数据源：
        1. 优先使用快照数据（新系统）
        2. 降级到实时查询（旧系统）
        """
        user = User.query.get(user_id)
        if not user:
            return {"error": "用户不存在"}

        # 智能选择数据源
        if PointOptimizationConfig.should_read_from_new_system():
            try:
                return PointsService._get_user_rank_from_snapshot(user_id, user)
            except Exception as e:
                logger.warning(f"从快照获取用户排名失败，降级到实时查询: {e}")
                if PointOptimizationConfig.get_config("fallback_enabled"):
                    return PointsService._get_user_rank_from_asset(user_id, user)
                else:
                    raise
        else:
            return PointsService._get_user_rank_from_asset(user_id, user)

    @staticmethod
    def _get_user_rank_from_snapshot(user_id: int, user: User):
        """从快照数据获取用户排名（新系统）"""
        try:
            from app.models.point_archive import PointStatisticsSnapshot

            # 获取最新的快照数据
            latest_snapshot = PointStatisticsSnapshot.query.filter_by(
                user_id=user_id
            ).order_by(PointStatisticsSnapshot.snapshot_date.desc()).first()

            if not latest_snapshot:
                raise BusinessException("用户快照数据不存在")

            return {
                "email": mask_email(user.email),
                "points": float(latest_snapshot.total_points),
                "rank": latest_snapshot.rank_position,
                "daily_increase": float(latest_snapshot.daily_increase),
                "last_update": latest_snapshot.snapshot_date.isoformat(),
                "data_source": "snapshot"
            }

        except Exception as e:
            logger.error(f"从快照获取用户排名失败: {e}")
            raise

    @staticmethod
    def _get_user_rank_from_asset(user_id: int, user: User):
        """从资产数据获取用户排名（旧系统兼容）"""
        try:
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()

            # 获取用户当前积分
            user_asset = UserAsset.query.filter_by(
                user_id=user.id,
                asset_type_id=asset_type.id
            ).first()

            current_user_points = Decimal('0')
            if user_asset:
                current_user_points = user_asset.available_balance

            # 计算用户排名（计算有多少用户的积分比当前用户多）
            user_rank = db.session.query(
                func.count(UserAsset.user_id)
            ).filter(
                UserAsset.asset_type_id == asset_type.id,
                (UserAsset.available_balance) > current_user_points
            ).scalar() or 0

            return {
                "email": mask_email(user.email),
                "points": float(current_user_points),
                "rank": user_rank + 1,
                "daily_increase": 0,
                "last_update": None,
                "data_source": "asset"
            }

        except Exception as e:
            logger.error(f"从资产获取用户排名失败: {e}")
            # 返回默认值
            return {
                "email": mask_email(user.email),
                "points": 0,
                "rank": 1,
                "daily_increase": 0,
                "last_update": None,
                "data_source": "default"
            }

    @staticmethod
    def get_user_point_history(user_id: int, days: int = 30, archive_type: str = 'daily'):
        """获取用户积分历史数据

        Args:
            user_id: 用户ID
            days: 查询天数
            archive_type: 归档类型 (daily, weekly, monthly)

        Returns:
            Dict: 历史数据
        """
        try:
            from app.models.point_archive import PointRecordArchive, ArchiveType
            from datetime import date, timedelta

            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            # 转换归档类型
            archive_type_enum = ArchiveType(archive_type)

            # 查询归档数据
            archives = db.session.query(
                PointRecordArchive.archive_date,
                PointRecordArchive.record_type,
                PointRecordArchive.total_points
            ).filter(
                PointRecordArchive.user_id == user_id,
                PointRecordArchive.archive_type == archive_type_enum,
                PointRecordArchive.archive_date >= start_date,
                PointRecordArchive.archive_date <= end_date
            ).order_by(PointRecordArchive.archive_date.desc()).all()

            # 按日期分组
            history_data = {}
            for archive in archives:
                date_str = archive.archive_date.isoformat()
                if date_str not in history_data:
                    history_data[date_str] = {
                        'date': date_str,
                        'total_points': 0,
                        'task_points': 0,
                        'invite_points': 0,
                        'project_points': 0
                    }

                points = float(archive.total_points)
                history_data[date_str]['total_points'] += points
                history_data[date_str][f'{archive.record_type}_points'] = points

            # 如果归档数据不足，补充实时数据
            if len(history_data) < days:
                recent_records = db.session.query(
                    func.date(PointRecord.created_at).label('record_date'),
                    PointRecord.record_type,
                    func.sum(PointRecord.points).label('total_points')
                ).filter(
                    PointRecord.user_id == user_id,
                    func.date(PointRecord.created_at) >= start_date,
                    PointRecord.archive_status == 'pending'
                ).group_by(
                    func.date(PointRecord.created_at),
                    PointRecord.record_type
                ).all()

                for record in recent_records:
                    date_str = record.record_date.isoformat()
                    if date_str not in history_data:
                        history_data[date_str] = {
                            'date': date_str,
                            'total_points': 0,
                            'task_points': 0,
                            'invite_points': 0,
                            'project_points': 0
                        }

                    points = float(record.total_points)
                    history_data[date_str]['total_points'] += points
                    history_data[date_str][f'{record.record_type}_points'] = points

            # 转换为列表并排序
            result = list(history_data.values())
            result.sort(key=lambda x: x['date'], reverse=True)

            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days,
                    'archive_type': archive_type
                },
                'history': result
            }

        except Exception as e:
            print(f"获取用户积分历史失败: {str(e)}")
            return {
                'period': {
                    'start_date': None,
                    'end_date': None,
                    'days': days,
                    'archive_type': archive_type
                },
                'history': []
            }
