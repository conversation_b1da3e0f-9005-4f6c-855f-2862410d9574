import secrets
from decimal import Decimal

from sqlalchemy import func

from app.models.asset import UserAsset
from app.models.base import db
from app.models.points import TaskType, PointRecord
from app.models.user import User
from app.services.asset_service import AssetService
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.utils.errors import BusinessException
from app.utils.response import Response
from app.utils.str_helper import mask_email


class PointsService:
    @staticmethod
    def generate_invite_code(user_id):
        user = User.query.get(user_id)
        if not user.invite_code:
            code = secrets.token_urlsafe(6)[:6].upper()
            while User.query.filter_by(invite_code=code).first():
                code = secrets.token_urlsafe(6)[:6].upper()
            user.invite_code = code
            db.session.commit()
        return Response.success(data={"invite_code": user.invite_code})

    @staticmethod
    def get_task_status(user_id):
        task_records = PointRecord.query.filter_by(user_id=user_id, record_type='task').all()
        completed_tasks = [tr.task_type.name for tr in task_records]
        return Response.success(data={"completed_tasks": completed_tasks})

    @staticmethod
    def add_task_points(user_id, task_type_name):
        task_type = TaskType.query.filter_by(name=task_type_name).first()
        if not task_type:
            return Response.error("任务类型不存在", code=404)

        exists = PointRecord.query.filter_by(
            user_id=user_id,
            task_type_id=task_type.id
        ).first()
        if exists:
            return Response.error("该任务已完成", code=400)

        # 使用集成服务添加积分
        success, error = UbiPointsIntegrationService.add_ubi_points(
            user_id=user_id,
            points=task_type.points,
            record_type='task',
            related_id=task_type.id
        )

        if not success:
            return Response.error(f"添加积分失败: {error}", code=500)

        return Response.success(data={"points": task_type.points})

    @staticmethod
    def add_init_invite_record(inviter_id, invitee_id):
        """
        Add a pending invitation record with 0 point.
        """
        exists = PointRecord.query.filter_by(
            invitee_id=invitee_id,
            record_type='invite'
        ).first()
        if exists:
            return Response.error("该用户已被邀请", code=400)

        # 使用集成服务添加积分记录
        success1, error1 = UbiPointsIntegrationService.add_ubi_points(
            user_id=inviter_id,
            points=0,
            record_type='invite',
            related_id=invitee_id
        )

        success2, error2 = UbiPointsIntegrationService.add_ubi_points(
            user_id=invitee_id,
            points=0,
            record_type='invite'
        )

        if not success1 or not success2:
            return Response.error(f"添加邀请记录失败: {error1 or error2}", code=500)

        return Response.success(data={"points": 0})

    @staticmethod
    def update_invite_points(inviter_id, invitee_id):
        # 查找用户邀请记录（邀请者发起邀请）
        inviter_record = PointRecord.query.filter_by(
            user_id=inviter_id,
            invitee_id=invitee_id,
            record_type='invite'
        ).first()

        # 查找用户被邀请记录（被邀请者的记录）
        invitee_record = PointRecord.query.filter_by(
            user_id=invitee_id,
            record_type='invite'
        ).first()

        if not inviter_record or not invitee_record:
            return Response.error("邀请记录不存在", code=400)

        # 如果邀请记录中points为0，则更新积分
        if inviter_record.points == 0:
            # 更新邀请者积分
            inviter_record.points = 2
            invitee_record.points = 1

            # 添加到用户资产
            asset_type = UbiPointsIntegrationService.ensure_ubi_asset_type_exists()
            AssetService.add_or_create_user_asset(
                user_id=inviter_id,
                asset_type_id=asset_type.id,
                amount=Decimal(2),
                reference_id=invitee_record.id,
                remark=f"UBI积分增加: inviter" + (f", 关联ID: {invitee_record.id}" if invitee_record else "")
            )

            AssetService.add_or_create_user_asset(
                user_id=invitee_id,
                asset_type_id=asset_type.id,
                amount=Decimal(1),
                reference_id=invitee_record.id,
                remark=f"UBI积分增加: invitee" + (f", 关联ID: {invitee_record.id}" if invitee_record else "")
            )
            db.session.commit()
            return Response.success(data={"points": 2})
        else:
            return Response.error("邀请积分已更新", code=400)

    @staticmethod
    def add_invite_points(inviter_id, invitee_id):
        exists = PointRecord.query.filter_by(
            invitee_id=invitee_id,
            record_type='invite'
        ).first()
        if exists:
            return Response.error("该用户已被邀请", code=400)

        # 使用集成服务添加积分
        success1, error1 = UbiPointsIntegrationService.add_ubi_points(
            user_id=inviter_id,
            points=100,
            record_type='invite',
            related_id=invitee_id
        )

        success2, error2 = UbiPointsIntegrationService.add_ubi_points(
            user_id=invitee_id,
            points=50,
            record_type='invite'
        )

        if not success1 or not success2:
            return Response.error(f"添加邀请积分失败: {error1 or error2}", code=500)

        return Response.success(data={"points": 100})

    @staticmethod
    def add_project_running_points(user_id, project_id):
        """
        Add points for a project that has been running continuously for 1 hour.

        Args:
            user_id: The user ID to award points to
            project_id: The id of the project that has been running

        Returns:
            Response: Success or error response
        """
        # 使用集成服务添加项目运行积分
        success, error = UbiPointsIntegrationService.add_ubi_points(
            user_id=user_id,
            points=0.01,  # 0.01 points for 1 hour of continuous running
            record_type='project',
            related_id=project_id
        )

        return success, error

    @staticmethod
    def get_rank_list(page=1, per_page=30) -> list:
        try:
            # 获取UBI积分资产类型
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()

            # 直接查询user_asset表获取排名
            ranking_query = (db.session.query(
                User.email,
                UserAsset.available_balance
            ).join(
                UserAsset, User.id == UserAsset.user_id
            ).filter(
                UserAsset.asset_type_id == asset_type.id
            ).order_by(UserAsset.available_balance.desc())
                             .limit(per_page)
                             .offset((page - 1) * per_page)
                             .all())

            return [{
                "email": mask_email(email),
                "points": float(points) if points else 0
            } for email, points in ranking_query]
        except BusinessException as e:
            # 如果UBI积分资产类型不存在，返回空列表
            print(f"获取积分排名失败: {str(e)}")
            return []

    @staticmethod
    def get_user_rank(user_id: int):
        user = User.query.get(user_id)
        try:
            # 获取UBI积分资产类型
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()

            # 获取用户当前积分
            user_asset = UserAsset.query.filter_by(
                user_id=user.id,
                asset_type_id=asset_type.id
            ).first()

            current_user_points = Decimal('0')
            if user_asset:
                current_user_points = user_asset.available_balance

            # 计算用户排名（计算有多少用户的积分比当前用户多）
            user_rank = db.session.query(
                func.count(UserAsset.user_id)
            ).filter(
                UserAsset.asset_type_id == asset_type.id,
                (UserAsset.available_balance) > current_user_points
            ).scalar() or 0

            return {
                "email": mask_email(user.email),
                "points": float(current_user_points),
                "rank": user_rank + 1
            }
        except BusinessException:
            # 如果UBI积分资产类型不存在，返回默认值
            return {
                "email": mask_email(user.email),
                "points": 0,
                "rank": 1
            }
