from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.enums.biz_enums import NotifyType
from app.services.notify_service import NotifyService
from app.utils.i18n_helper import get_locale
from app.utils.response import Response

user_notify_bp = Blueprint('user_notify', __name__)


@user_notify_bp.route('', methods=['GET'])
@jwt_required()
def get_user_notifications():
    """获取当前用户的通知列表"""
    user_id = get_jwt_identity()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    unread_only = request.args.get('unread_only', False, type=bool)
    b_popup = request.args.get('b_popup', False, type=bool)
    notify_type_list = request.args.getlist('notify_type[]', type=str)
    notify_priority_list = request.args.getlist('notify_priority[]', type=int)

    notifications, total = NotifyService.get_user_notifications(
        get_locale(),
        user_id=user_id,
        page=page,
        per_page=per_page,
        notify_type_list=notify_type_list,
        notify_priority_list=notify_priority_list,
        unread_only=unread_only,
        b_popup=b_popup
    )

    unread_count = NotifyService.get_unread_count(user_id=user_id,
                                                  notify_type_list=notify_type_list,
                                                  notify_priority_list=notify_priority_list)

    response_data = {
        'unread_count': unread_count,
        'items': notifications,
        'total': total,
        'page': page,
        'per_page': per_page
    }
    return Response.success(response_data)


@user_notify_bp.route('/<int:id>', methods=['DELETE'])
@jwt_required()
def delete_user_notifications(id):
    """删除通知"""

    NotifyService.delete_user_notify(
        id=id,
        user_id=get_jwt_identity()
    )
    return Response.success()


@user_notify_bp.route('/<int:id>/read', methods=['PUT'])
@jwt_required()
def get_and_read(id):
    """标记通知为已读"""

    notification = NotifyService.get_and_read_user_notification(
        get_locale(),
        id=id,
        user_id=get_jwt_identity()
    )
    return Response.success(notification)

@user_notify_bp.route('/read', methods=['PUT'])
@jwt_required()
def batch_read():
    """批量标为已读通知"""
    user_id = get_jwt_identity()
    id_list = request.args.getlist('id[]', type=int)
    notify_type_list = request.args.getlist('notify_type[]', type=str)
    notify_priority_list = request.args.getlist('notify_priority[]', type=int)

    NotifyService.batch_read(
        user_id=user_id,
        id_list=id_list,
        notify_type_list=notify_type_list,
        notify_priority_list=notify_priority_list
    )

    return Response.success()
