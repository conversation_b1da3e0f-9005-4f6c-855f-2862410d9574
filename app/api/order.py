from decimal import Decimal

from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.middlewares.auth import admin_required
from app.services.order_service import OrderService
from app.utils.auth import get_current_user
from app.utils.decorators import handle_api_errors
from app.utils.response import Response
from app.utils.asset import format_decimal_by_precision

order_bp = Blueprint('order', __name__)


@order_bp.route('', methods=['POST'])
@jwt_required()
@handle_api_errors
def create_order():
    """创建订单"""

    data = request.get_json()
    if not data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    current_user = get_current_user()
    user_id = data.get('user_id', get_jwt_identity()) if current_user.is_admin else get_jwt_identity()

    # 参数验证
    required_fields = ['order_type', 'side', 'pair_id', 'amount', 'price']
    for field in required_fields:
        if field not in data:
            return Response.error(f"缺少必要参数: {field}", 400)

    create_order_params = {
        "user_id": user_id,
        "order_type": data['order_type'],
        "side": data['side'],
        "pair_id": data['pair_id'],
        "price": data.get('price'),
        "amount": data['amount']
    }

    order = OrderService.create_order(create_order_params)

    # 获取交易对信息来确定小数位数
    from app.models.blockchain import TradingPair
    trading_pair = db.session.get(TradingPair, order.pair_id)
    price_decimals = 8  # 默认价格小数位数
    amount_decimals = 8  # 默认数量小数位数

    if trading_pair:
        # 这里可以根据交易对的配置来设置小数位数
        # 暂时使用默认值
        pass

    return Response.success({
        'order_id': order.id,
        'status': order.status.value,
        'order_type': order.order_type.value,
        'side': order.side.value,
        'pair_id': order.pair_id,
        'price': format_decimal_by_precision(order.price, price_decimals) if order.price is not None else None,
        'amount': format_decimal_by_precision(order.original_amount, amount_decimals),
        'maker_order_ids': [order.id for order in order.maker_order_matches],
        'taker_order_ids': [order.id for order in order.taker_order_matches],
    })


@jwt_required()
@order_bp.route('', methods=['GET'])
@handle_api_errors
def get_user_orders():
    """获取用户订单列表"""

    # 权限校验
    current_user_id = get_jwt_identity()

    # 参数获取和验证
    page = request.args.get('page', 1, type=int)
    size = request.args.get('size', 20, type=int)
    status = request.args.get('status', '')
    start_time = request.args.get('start_time', '')
    end_time = request.args.get('end_time', '')
    asset_type = request.args.get('asset_type', '')
    pair_id = request.args.get('pair_id', '')
    order_source = request.args.get('order_source', '')

    # 参数验证
    if page < 1:
        return Response.success(data=None, message="页码必须大于0", code=400)
    if size < 1 or size > 100:
        return Response.success(data=None, message="每页数量必须在1-100之间", code=400)

    # 构建查询参数
    query_params = {
        'user_id': current_user_id,
        'status': status,
        'start_time': start_time,
        'end_time': end_time,
        'asset_type': asset_type,
        'order_source': order_source,
        'pair_id': pair_id,
        'page': page,
        'size': size
    }

    result = OrderService.get_user_history_orders(query_params)

    # 格式化用户订单数据
    orders_data = [OrderService.build_enhanced_order_info(order) for order in result['records']]

    return Response.success({
        'records': orders_data,
        'total': result['total'],
        'page': result['page'],
        'size': result['size']
    })


@order_bp.route('/trading-hall', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_trading_hall_orders():
    """获取交易大厅订单（所有用户的PARTIAL/PENDING状态订单）"""
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    size = request.args.get('size', 20, type=int)
    asset_type = request.args.get('asset_type', '')
    pair_id = request.args.get('pair_id', '')

    # 参数校验
    if page < 1:
        return Response.success(data=None, message="页码必须大于0", code=400)
    if size < 1 or size > 100:
        return Response.success(data=None, message="每页数量必须在1-100之间", code=400)

    # 构建查询参数
    query_params = {
        'page': page,
        'size': size,
        'status_list': ['PARTIAL', 'PENDING'],  # 只查询部分成交和待成交的订单
        'asset_type': asset_type,
        'pair_id': pair_id
    }

    # 调用服务层
    result = OrderService.get_trading_hall_orders(query_params)

    # 序列化订单数据
    orders_data = [OrderService.build_enhanced_order_info(order) for order in result['records']]

    return Response.success({
        'records': orders_data,
        'total': result['total'],
        'page': result['page'],
        'size': result['size']
    })


@jwt_required()
@order_bp.route('/<int:order_id>', methods=['DELETE'])
@handle_api_errors
def cancel_order(order_id):
    """取消订单"""

    current_user_id = get_jwt_identity()

    # 参数验证
    if order_id <= 0:
        return Response.success(data=None, message="订单ID必须大于0", code=400)

    updated_order = OrderService.cancel_order(order_id, current_user_id)

    return Response.success({
        'order_id': updated_order.id,
        'status': updated_order.status.value,
        'message': '订单取消成功'
    })


# ========================================
# 管理员订单管理接口
# ========================================


@order_bp.route('/admin', methods=['GET'])
@admin_required
@handle_api_errors
def admin_get_orders():
    """管理员获取所有订单列表（包含详细信息）"""

    # 参数获取和验证
    page = request.args.get('page', 1, type=int)
    size = request.args.get('size', 20, type=int)
    user_id = request.args.get('user_id', type=int)
    status = request.args.get('status')
    order_type = request.args.get('order_type')
    side = request.args.get('side')
    pair_id = request.args.get('pair_id', type=int)
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')

    # 参数验证
    if page < 1:
        return Response.success(data=None, message="页码必须大于0", code=400)
    if size < 1 or size > 100:
        return Response.success(data=None, message="每页数量必须在1-100之间", code=400)

    # 构建查询参数
    query_params = {
        'user_id': user_id,  # 管理员可以查询指定用户的订单
        'status': status,
        'order_type': order_type,
        'side': side,
        'pair_id': pair_id,
        'start_time': start_time,
        'end_time': end_time,
        'page': page,
        'size': size
    }

    result = OrderService.get_admin_orders(query_params)

    # 格式化管理员订单数据，包含详细信息
    orders_data = []
    for order in result['records']:
        # 获取交易对信息
        trading_pair = order.trading_pair

        # 设置默认小数位数
        price_decimals = 8
        amount_decimals = 8

        remaining_amount = Decimal(str(order.original_amount)) - Decimal(str(order.executed_amount))
        fill_rate_value = (Decimal(str(order.executed_amount)) / Decimal(str(order.original_amount)) * 100) if float(order.original_amount) > 0 else Decimal('0')

        order_data = {
            'id': order.id,
            'user_id': order.user_id,
            'pair_id': order.pair_id,
            'pair_name': trading_pair['pair_name'] if trading_pair else f"交易对{order.pair_id}",
            'order_type': order.order_type.value if order.order_type else None,
            'side': order.side.value if order.side else None,
            'price': format_decimal_by_precision(order.price, price_decimals) if order.price is not None else None,
            'original_amount': format_decimal_by_precision(order.original_amount, amount_decimals),
            'executed_amount': format_decimal_by_precision(order.executed_amount, amount_decimals),
            'executed_value': format_decimal_by_precision(order.executed_value, price_decimals),
            'fee': format_decimal_by_precision(order.fee, amount_decimals),
            'status': order.status.value if order.status else None,
            'status_text': OrderService.get_status_text(order.status),
            'created_at': order.created_at.isoformat() if order.created_at else None,
            'updated_at': order.updated_at.isoformat() if order.updated_at else None,
            # 计算字段
            'remaining_amount': format_decimal_by_precision(remaining_amount, amount_decimals),
            'fill_rate': f"{float(fill_rate_value):.2f}%"
        }
        orders_data.append(order_data)

    return Response.success({
        'records': orders_data,
        'total': result['total'],
        'page': result['page'],
        'size': result['size']
    })


@admin_required
@order_bp.route('/admin/<int:order_id>', methods=['GET'])
def admin_get_order_detail(order_id):
    """管理员获取订单详情"""

    if order_id <= 0:
        return Response.success(data=None, message="订单ID必须大于0", code=400)

    order_detail = OrderService.get_order_detail(order_id)
    return Response.success(order_detail)


@admin_required
@order_bp.route('/admin/<int:order_id>/cancel', methods=['POST'])
def admin_cancel_order(order_id):
    """管理员强制取消订单"""
    if order_id <= 0:
        return Response.success(data=None, message="订单ID必须大于0", code=400)

    data = request.get_json() or {}
    reason = data.get('reason', '管理员操作')

    updated_order = OrderService.admin_cancel_order(order_id, reason)
    return Response.success({
        'order_id': updated_order.id,
        'status': updated_order.status.value,
        'message': '订单已被管理员取消'
    })


# ========================================
# 订单簿和成交记录接口
# ========================================

@order_bp.route('/orderbook/<int:pair_id>', methods=['GET'])
@jwt_required()
def get_order_book(pair_id):
    """获取订单簿"""
    depth = request.args.get('depth', 10, type=int)

    if pair_id <= 0:
        return Response.success(data=None, message="无效的交易对ID", code=400)

    if depth <= 0 or depth > 50:
        return Response.success(data=None, message="深度必须在1-50之间", code=400)

    from app.services.matching_engine import MatchingEngine
    order_book = MatchingEngine.get_order_book(pair_id, depth)
    return Response.success(order_book)


@order_bp.route('/trades/<int:pair_id>', methods=['GET'])
@jwt_required()
def get_recent_trades(pair_id):
    """获取最近成交记录"""

    limit = request.args.get('limit', 50, type=int)

    if pair_id <= 0:
        return Response.success(data=None, message="无效的交易对ID", code=400)

    if limit <= 0 or limit > 100:
        return Response.success(data=None, message="限制数量必须在1-100之间", code=400)

    from app.services.matching_engine import MatchingEngine
    trades = MatchingEngine.get_recent_trades(pair_id, limit)

    return Response.success({
        'pair_id': pair_id,
        'trades': trades,
        'count': len(trades)
    })
