"""积分优化配置管理 API

提供积分优化相关的配置管理和监控接口：
1. 配置管理（灰度发布控制）
2. 性能监控
3. 迁移状态查询
4. 手动触发任务
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.utils.response import Response
from app.services.point_optimization_config import PointOptimizationConfig
from app.tasks.point_optimization_tasks import (
    auto_archive_daily_points,
    generate_daily_point_snapshots,
    gradual_migrate_user_points,
    gradual_archive_historical_data,
    cleanup_old_point_records
)
from app.models.point_archive import PointMigrationLog, PointStatisticsSnapshot
from app.models.base import db
from sqlalchemy import func
from datetime import date, timedelta

bp = Blueprint('point_optimization', __name__, url_prefix='/api/point-optimization')


@bp.route('/config', methods=['GET'])
@jwt_required()
def get_config():
    """获取积分优化配置"""
    try:
        config = PointOptimizationConfig.get_config()
        return Response.success(config)
    except Exception as e:
        return Response.error(f"获取配置失败: {str(e)}", 500)


@bp.route('/config', methods=['POST'])
@jwt_required()
def update_config():
    """更新积分优化配置"""
    try:
        data = request.get_json()
        if not data:
            return Response.validation_error("请提供配置数据")
        
        # 验证配置项
        valid_keys = {
            'dual_write_enabled', 'dual_write_ratio',
            'read_from_new_system', 'read_ratio', 'fallback_enabled',
            'auto_archive_enabled', 'archive_batch_size', 'archive_days_threshold',
            'performance_monitoring', 'slow_query_threshold',
            'gradual_rollout', 'rollout_percentage',
            'emergency_fallback', 'max_error_rate'
        }
        
        invalid_keys = set(data.keys()) - valid_keys
        if invalid_keys:
            return Response.validation_error(f"无效的配置项: {', '.join(invalid_keys)}")
        
        # 验证数值范围
        if 'dual_write_ratio' in data:
            ratio = data['dual_write_ratio']
            if not isinstance(ratio, (int, float)) or not 0 <= ratio <= 1:
                return Response.validation_error("dual_write_ratio 必须在 0-1 之间")
        
        if 'read_ratio' in data:
            ratio = data['read_ratio']
            if not isinstance(ratio, (int, float)) or not 0 <= ratio <= 1:
                return Response.validation_error("read_ratio 必须在 0-1 之间")
        
        if 'rollout_percentage' in data:
            percentage = data['rollout_percentage']
            if not isinstance(percentage, int) or not 0 <= percentage <= 100:
                return Response.validation_error("rollout_percentage 必须在 0-100 之间")
        
        # 更新配置
        success = PointOptimizationConfig.update_config(data)
        if success:
            return Response.success({"message": "配置更新成功", "updated_keys": list(data.keys())})
        else:
            return Response.error("配置更新失败", 500)
            
    except Exception as e:
        return Response.error(f"更新配置失败: {str(e)}", 500)


@bp.route('/config/preset/<preset_name>', methods=['POST'])
@jwt_required()
def apply_config_preset(preset_name):
    """应用预设配置"""
    try:
        presets = {
            'development': {
                'dual_write_enabled': True,
                'dual_write_ratio': 0.1,
                'read_from_new_system': True,
                'read_ratio': 0.1,
                'fallback_enabled': True,
                'auto_archive_enabled': True,
                'gradual_rollout': True,
                'rollout_percentage': 10,
                'emergency_fallback': False
            },
            'staging': {
                'dual_write_enabled': True,
                'dual_write_ratio': 0.5,
                'read_from_new_system': True,
                'read_ratio': 0.5,
                'fallback_enabled': True,
                'auto_archive_enabled': True,
                'gradual_rollout': True,
                'rollout_percentage': 50,
                'emergency_fallback': False
            },
            'production': {
                'dual_write_enabled': True,
                'dual_write_ratio': 1.0,
                'read_from_new_system': True,
                'read_ratio': 1.0,
                'fallback_enabled': True,
                'auto_archive_enabled': True,
                'gradual_rollout': False,
                'rollout_percentage': 100,
                'emergency_fallback': False
            },
            'emergency': {
                'dual_write_enabled': False,
                'dual_write_ratio': 0.0,
                'read_from_new_system': False,
                'read_ratio': 0.0,
                'fallback_enabled': True,
                'auto_archive_enabled': False,
                'gradual_rollout': False,
                'rollout_percentage': 0,
                'emergency_fallback': True
            }
        }
        
        if preset_name not in presets:
            return Response.validation_error(f"未知的预设配置: {preset_name}")
        
        preset_config = presets[preset_name]
        success = PointOptimizationConfig.update_config(preset_config)
        
        if success:
            return Response.success({
                "message": f"成功应用 {preset_name} 预设配置",
                "config": preset_config
            })
        else:
            return Response.error("应用预设配置失败", 500)
            
    except Exception as e:
        return Response.error(f"应用预设配置失败: {str(e)}", 500)


@bp.route('/performance', methods=['GET'])
@jwt_required()
def get_performance_stats():
    """获取性能统计"""
    try:
        operation = request.args.get('operation', 'get_rank_list')
        hours = request.args.get('hours', 1, type=int)
        
        if hours <= 0 or hours > 24:
            return Response.validation_error("hours 必须在 1-24 之间")
        
        stats = PointOptimizationConfig.get_performance_stats(operation, hours)
        return Response.success(stats)
        
    except Exception as e:
        return Response.error(f"获取性能统计失败: {str(e)}", 500)


@bp.route('/performance/operations', methods=['GET'])
@jwt_required()
def get_all_performance_stats():
    """获取所有操作的性能统计"""
    try:
        hours = request.args.get('hours', 1, type=int)
        operations = ['get_rank_list', 'get_user_rank', 'get_user_point_history']
        
        all_stats = {}
        for operation in operations:
            all_stats[operation] = PointOptimizationConfig.get_performance_stats(operation, hours)
        
        return Response.success(all_stats)
        
    except Exception as e:
        return Response.error(f"获取性能统计失败: {str(e)}", 500)


@bp.route('/migration/status', methods=['GET'])
@jwt_required()
def get_migration_status():
    """获取迁移状态"""
    try:
        # 获取最近的迁移日志
        recent_logs = PointMigrationLog.query.order_by(
            PointMigrationLog.created_at.desc()
        ).limit(10).all()
        
        # 获取快照统计
        latest_snapshot_date = db.session.query(
            func.max(PointStatisticsSnapshot.snapshot_date)
        ).scalar()
        
        snapshot_count = 0
        if latest_snapshot_date:
            snapshot_count = PointStatisticsSnapshot.query.filter_by(
                snapshot_date=latest_snapshot_date
            ).count()
        
        # 获取归档统计
        from app.models.point_archive import PointRecordArchive
        archive_count = PointRecordArchive.query.count()
        
        # 获取待归档记录数
        from app.models.points import PointRecord
        pending_count = PointRecord.query.filter_by(archive_status='pending').count()
        archived_count = PointRecord.query.filter_by(archive_status='archived').count()
        
        return Response.success({
            'recent_migrations': [log.to_dict() for log in recent_logs],
            'snapshot_stats': {
                'latest_date': latest_snapshot_date.isoformat() if latest_snapshot_date else None,
                'user_count': snapshot_count
            },
            'archive_stats': {
                'total_archives': archive_count,
                'pending_records': pending_count,
                'archived_records': archived_count
            },
            'current_config': PointOptimizationConfig.get_config()
        })
        
    except Exception as e:
        return Response.error(f"获取迁移状态失败: {str(e)}", 500)


@bp.route('/tasks/archive', methods=['POST'])
@jwt_required()
def trigger_archive_task():
    """手动触发归档任务"""
    try:
        data = request.get_json() or {}
        target_date = data.get('target_date')
        
        if target_date:
            # 验证日期格式
            try:
                from datetime import datetime
                datetime.strptime(target_date, '%Y-%m-%d')
            except ValueError:
                return Response.validation_error("日期格式错误，应为 YYYY-MM-DD")
        
        # 触发归档任务
        task = auto_archive_daily_points.delay(target_date)
        
        return Response.success({
            "message": "归档任务已启动",
            "task_id": task.id,
            "target_date": target_date or "昨天"
        })
        
    except Exception as e:
        return Response.error(f"触发归档任务失败: {str(e)}", 500)


@bp.route('/tasks/snapshot', methods=['POST'])
@jwt_required()
def trigger_snapshot_task():
    """手动触发快照生成任务"""
    try:
        data = request.get_json() or {}
        target_date = data.get('target_date')
        
        if target_date:
            # 验证日期格式
            try:
                from datetime import datetime
                datetime.strptime(target_date, '%Y-%m-%d')
            except ValueError:
                return Response.validation_error("日期格式错误，应为 YYYY-MM-DD")
        
        # 触发快照生成任务
        task = generate_daily_point_snapshots.delay(target_date)
        
        return Response.success({
            "message": "快照生成任务已启动",
            "task_id": task.id,
            "target_date": target_date or "昨天"
        })
        
    except Exception as e:
        return Response.error(f"触发快照生成任务失败: {str(e)}", 500)


@bp.route('/tasks/migrate', methods=['POST'])
@jwt_required()
def trigger_migration_task():
    """手动触发渐进式迁移任务"""
    try:
        data = request.get_json() or {}
        batch_size = data.get('batch_size', 1000)
        max_users = data.get('max_users', 5000)
        
        if batch_size <= 0 or batch_size > 10000:
            return Response.validation_error("batch_size 必须在 1-10000 之间")
        
        if max_users <= 0 or max_users > 50000:
            return Response.validation_error("max_users 必须在 1-50000 之间")
        
        # 触发迁移任务
        task = gradual_migrate_user_points.delay(batch_size, max_users)
        
        return Response.success({
            "message": "渐进式迁移任务已启动",
            "task_id": task.id,
            "batch_size": batch_size,
            "max_users": max_users
        })
        
    except Exception as e:
        return Response.error(f"触发迁移任务失败: {str(e)}", 500)


@bp.route('/tasks/cleanup', methods=['POST'])
@jwt_required()
def trigger_cleanup_task():
    """手动触发清理任务"""
    try:
        data = request.get_json() or {}
        days_to_keep = data.get('days_to_keep', 90)
        
        if days_to_keep <= 0 or days_to_keep > 365:
            return Response.validation_error("days_to_keep 必须在 1-365 之间")
        
        # 触发清理任务
        task = cleanup_old_point_records.delay(days_to_keep)
        
        return Response.success({
            "message": "清理任务已启动",
            "task_id": task.id,
            "days_to_keep": days_to_keep
        })
        
    except Exception as e:
        return Response.error(f"触发清理任务失败: {str(e)}", 500)
