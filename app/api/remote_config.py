"""远程配置 API"""
import json
from functools import wraps

import yaml
from flask import Blueprint, current_app, jsonify, request
from sqlalchemy import func

from app.enums.biz_enums import FixtureProjectName
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.services.project_service import ProjectService
from app.utils.crypto import Crypto, decrypt_data
from app.services.metrics_service import metrics_service
from app.utils.errors import ValidationError
from app.services.jsonrpc_service import JsonRPCService

bp = Blueprint("remote_config", __name__)


def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get("Authorization")
        if not token:
            return jsonify({"status": "error", "error": "缺少认证令牌"}), 401

        device = db.session.query(Device).filter_by(token=token).first()
        if not device:
            return jsonify({"status": "error", "error": "无效的认证令牌"}), 401

        return f(device, *args, **kwargs)
    return decorated


@bp.route("/configs/system", methods=["GET"])
@token_required
def get_system_config(device):
    """获取系统配置"""
    if not device.system_app_configs:
        return jsonify({"status": "error", "error": "设备未配置系统应用"}), 404

    # 从配置中获取加密密钥
    crypto = Crypto(current_app.config["ENCRYPTION_KEY"])
    data = json.dumps(device.system_app_configs).encode("utf-8")
    encrypted, nonce = crypto.encrypt(data)

    return jsonify({
        "encrypted": encrypted,
        "nonce": nonce
    })


@bp.route("/configs/service", methods=["GET"])
@token_required
def get_services_config(device):
    """获取服务配置"""
    try:
        # 获取设备的所有项目配置
        device_projects = DeviceProject.query.filter_by(device_id=device.id).filter(DeviceProject.state!='created').all()
        if not device_projects:
            return jsonify({"status": "error", "error": "设备未分配服务配置"}), 404

        # 生成所有项目的服务配置
        project_service = ProjectService()

        # 初始化新格式的配置
        formatted_config = {
            "docker_compose": {
                "version": "3",
                "services": {}
            },
            "projects": [],
            "configs": {}
        }

        # 合并所有项目的配置
        for device_project in device_projects:
            try:
                if device_project.project.name in [FixtureProjectName.UBI_PROJECT_NAME, FixtureProjectName.LIGHTER_PROJECT_NAME]:
                    continue

                config = project_service.generate_device_project_config(device_project)
                if not config:
                    continue

                project_name = device_project.project.name

                # 合并 docker-compose 配置（每个项目的服务名称应该是唯一的）
                if config.get("docker_compose"):
                    docker_compose = config["docker_compose"]
                    if "services" in docker_compose:
                        for service_name, service_config in docker_compose["services"].items():
                            if service_name in formatted_config["docker_compose"]["services"]:
                                current_app.logger.warning(
                                    f"Service {service_name} already exists in config, "
                                    f"will be overwritten by project {project_name}"
                                )
                            formatted_config["docker_compose"]["services"][service_name] = service_config

                # 添加项目信息
                project_files = []
                if config.get("files"):
                    for file_path in config["files"].keys():
                        project_files.append(file_path)
                        # 添加文件内容到 configs，使用项目名作为前缀
                        full_path = f"{project_name}/{file_path}"
                        formatted_config["configs"][full_path] = config["files"][file_path]

                formatted_config["projects"].append({
                    "name": project_name,
                    "files": project_files
                })

            except Exception as e:
                current_app.logger.error(f"Generate service config error: {str(e)}")
                continue

        if not formatted_config["projects"]:
            return jsonify({"status": "error", "error": "生成服务配置失败"}), 500

        # 将 docker-compose 配置转换为 YAML 格式
        try:
            docker_compose_yaml = yaml.dump(
                formatted_config["docker_compose"],
                default_flow_style=False,
                sort_keys=False,
                allow_unicode=True
            )
            formatted_config["docker_compose"] = docker_compose_yaml
        except Exception as e:
            current_app.logger.error(f"Convert docker-compose to YAML error: {str(e)}")
            return jsonify({"status": "error", "error": "生成 docker-compose 配置失败"}), 500

        # 加密配置数据
        crypto = Crypto(current_app.config["ENCRYPTION_KEY"])
        data = json.dumps(formatted_config).encode("utf-8")
        encrypted, nonce = crypto.encrypt(data)

        return jsonify({
            "encrypted": encrypted,
            "nonce": nonce
        })

    except Exception as e:
        current_app.logger.error(f"Get services config error: {str(e)}")
        return jsonify({"status": "error", "error": "获取服务配置失败"}), 500


@bp.route("/devices/token", methods=["POST"])
def get_device_token():
    """通过加密的 MAC 地址获取设备 token

    请求体格式:
    {
        "encrypted": "base64编码的加密MAC地址",
        "nonce": "base64编码的nonce"
    }
    """
    try:
        data = request.get_json()
        if not data or "encrypted" not in data or "nonce" not in data:
            return jsonify({"status": "error", "error": "无效的请求数据"}), 400

        # 解密 MAC 地址
        crypto = Crypto(current_app.config["ENCRYPTION_KEY"])
        try:
            mac_address = crypto.decrypt(data["encrypted"], data["nonce"]).decode("utf-8")
        except Exception:
            return jsonify({"status": "error", "error": "解密失败"}), 400

        # 验证 MAC 地址格式
        if not mac_address or len(mac_address) != 17:
            return jsonify({"status": "error", "error": "无效的 MAC 地址"}), 400

        # 将 MAC 地址转换为大写进行查询
        mac_address = mac_address.upper()

        # 查找设备（不区分大小写）
        device = Device.query.filter(func.upper(Device.mac_address) == mac_address).first()
        if not device:
            return jsonify({"status": "error", "error": "设备未注册"}), 404

        # 加密设备 token
        token_data = device.token.encode("utf-8")
        encrypted, nonce = crypto.encrypt(token_data)

        return jsonify({
            "encrypted": encrypted,
            "nonce": nonce
        })

    except Exception as e:
        current_app.logger.error(f"Get device token error: {str(e)}")
        return jsonify({"status": "error", "error": "获取设备令牌失败"}), 500


@bp.route("/metrics", methods=["POST"])
@token_required
def update_metrics(device):
    """更新设备服务指标数据"""
    try:
        # 获取并验证请求数据
        data = request.get_json()
        if not data or 'encrypted' not in data or 'nonce' not in data:
            return jsonify({"status": "error", "error": "Invalid request data"}), 400

        # 解密数据
        try:
            decrypted_data = decrypt_data(data['encrypted'], data['nonce'])
            services_data = json.loads(decrypted_data)
        except Exception:
            return jsonify({"status": "error", "error": "Invalid encrypted data"}), 400

        # 验证解密后的数据格式
        if not isinstance(services_data, dict):
            return jsonify({"status": "error", "error": "Invalid services format"}), 400

        # 验证服务指标数据
        services = services_data.get('services', [])
        if not isinstance(services, list):
            return jsonify({"status": "error", "error": "Invalid services data format"}), 400

        # 验证设备运行时数据
        device_data = services_data.get('device')
        if device_data and not isinstance(device_data, dict):
            return jsonify({"status": "error", "error": "Invalid device data format"}), 400

        # 更新指标数据
        metrics_service.update_metrics(device.id, services_data)

        # 加密响应数据
        crypto = Crypto(current_app.config["ENCRYPTION_KEY"])
        response_data = json.dumps({"status": "success", "message": "ok"}).encode("utf-8")
        encrypted, nonce = crypto.encrypt(response_data)

        return jsonify({
            "encrypted": encrypted,
            "nonce": nonce
        })

    except ValidationError as e:
        return jsonify({"status": "error", "error": str(e)}), 400
    except Exception as e:
        current_app.logger.error(f"Update metrics error: {str(e)}")
        return jsonify({"status": "error", "error": "Internal server error"}), 500


@bp.route("/jsonrpc", methods=["POST"])
@token_required
def jsonrpc(device):
    """远程 JSON-RPC 2.0 接口"""
    try:
        data = request.get_json(force=True)
        if not data or "encrypted" not in data or "nonce" not in data:
            return jsonify({"status": "error", "error": "无效的请求体"}), 400

        # 解密 JSON-RPC 请求体
        try:
            decrypted = decrypt_data(data["encrypted"], data["nonce"])
            req = json.loads(decrypted)
        except Exception:
            return jsonify({"status": "error", "error": "解密失败"}), 400

        # 校验 JSON-RPC 2.0 基本字段
        if not req or req.get("jsonrpc") != "2.0" or "method" not in req or "id" not in req:
            return jsonify({"status": "error", "error": "无效的 JSON-RPC 请求体"}), 400

        result = JsonRPCService.handle_request(device, req)
        # 加密响应
        crypto = Crypto(current_app.config["ENCRYPTION_KEY"])
        data = json.dumps(result).encode("utf-8")
        encrypted, nonce = crypto.encrypt(data)
        return jsonify({
            "encrypted": encrypted,
            "nonce": nonce
        })
    except Exception as e:
        current_app.logger.error(f"JSON-RPC error: {str(e)}")
        return jsonify({"status": "error", "error": "内部错误"}), 500