"""项目相关的 API"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.models.project import Project
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.services.project_service import DeviceProjectService, ProjectService
from app.services.metrics_service import MetricsService
from app.utils.auth import get_current_user
from app.utils.decorators import handle_api_errors
from app.utils.response import Response
from app.tasks.device_project_validation import validate_device_projects

bp = Blueprint("project", __name__, url_prefix="/api/projects")

# New API endpoints for device project validation

@bp.route("/health", methods=["GET"])
@jwt_required()
@handle_api_errors
def check_device_projects():
    """
    Check if all required device project records exist for the current user's devices.
    
    Returns:
        JSON response with health status
    """
    current_user = get_current_user()
    if not current_user:
        return Response.unauthorized()
    
    # Use ProjectService to check health
    project_service = ProjectService()
    is_healthy, message, missing_records = project_service.check_device_projects_health(current_user.id)
    
    return Response.success({
        'healthy': is_healthy,
        'message': message,
        'missing_records': missing_records
    })


@bp.route("/missing", methods=["POST"])
@jwt_required()
@handle_api_errors
def create_missing_device_projects():
    """
    Create missing device project records for the current user's devices.
    
    Returns:
        JSON response with creation status
    """
    current_user = get_current_user()
    if not current_user:
        return Response.unauthorized()
    
    # Run the task synchronously for immediate feedback
    result = validate_device_projects(current_user.id)
    
    return Response.success({
        'success': result.get('success', False),
        'message': result.get('message', 'Unknown error')
    })


@bp.route("", methods=["GET"])
@jwt_required()
@handle_api_errors
def get_projects():
    """获取项目列表"""
    current_user = get_current_user()
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)
    status = request.args.get("status", type=int)
    backend = request.args.get("backend", type=int)

    service = ProjectService()
    data, error = service.get_projects(
        user=current_user,
        page=page,
        per_page=per_page,
        status=status,
        backend=backend,
    )
    if error:
        return Response.error(error)

    # 普通用户侧调用接口时需要查看 metrics
    if not current_user.is_admin:
        # 批量获取项目指标
        project_ids = [project["id"] for project in data["items"]]
        if project_ids:
            # 获取所有项目名称作为服务名称
            service_names = [project["name"] for project in data["items"]]

            # 获取服务指标
            metrics_data, metrics_error = service.get_projects_metrics(
                service_names,
                current_user.id if not current_user.is_admin else None
            )
            if metrics_error:
                return Response.error(metrics_error)

            # 为每个项目设置指标
            for project in data["items"]:
                project_metrics = metrics_data.get(project["name"], {})
                project_metrics['total_points'] = project_metrics.get('total_ubi_points', 0)
                project_metrics['yesterday_points'] = project_metrics.get('yesterday_ubi_points', 0)
                project["metrics"] = project_metrics

    return Response.success(data)


@bp.route("", methods=["POST"])
@jwt_required()
@handle_api_errors
def create_project():
    """
    创建项目
    :return: 项目详情
    """
    current_user = get_current_user()
    if not current_user:
        return Response.unauthorized()

    if not current_user.is_admin:
        return Response.error("权限不足", 403)

    data = request.get_json()
    if not data or not all(k in data for k in ("name", "service_config_id")):
        return Response.validation_error("缺少必要字段")

    # 验证 service_compose 格式
    if "service_compose" in data:
        if not isinstance(data["service_compose"], dict):
            return Response.validation_error("service_compose 必须是一个对象")
        if "services" not in data["service_compose"]:
            return Response.validation_error("service_compose 必须包含 services 字段")

    service = ProjectService()
    project, error = service.create_project(data, current_user)
    if error:
        return Response.error(error)

    return Response.success(project.to_dict(user=current_user), "项目创建成功")


@bp.route("/<int:project_id>", methods=["GET"])
@jwt_required()
@handle_api_errors
def get_project(project_id):
    """
    获取项目详情
    :param project_id: 项目ID
    :return: 项目详情
    """
    current_user = get_current_user()
    if not current_user:
        return Response.unauthorized()

    project = Project.query.get(project_id)
    if not project:
        return Response.not_found("项目不存在")

    if not current_user.is_admin and not current_user.has_project_permission(project_id):
        return Response.error("没有权限访问该项目", 403)

    project_data = project.to_dict(user=current_user)
    # 获取项目指标
    service = ProjectService()
    # 获取项目名称作为服务名称
    service_name = project_data["name"]
    
    # 获取服务指标
    metrics_data, metrics_error = service.get_projects_metrics(
        [service_name],
        current_user.id if not current_user.is_admin else None
    )
    if metrics_error:
        return Response.error(metrics_error)
    
    # 设置项目指标
    project_data["metrics"] = metrics_data.get(service_name, {})

    return Response.success(project_data)


@bp.route("/<int:project_id>", methods=["PUT"])
@jwt_required()
@handle_api_errors
def update_project(project_id):
    """
    更新项目
    :param project_id: 项目ID
    :return: 项目详情
    """
    current_user = get_current_user()
    if not current_user:
        return Response.unauthorized()

    if not current_user.is_admin:
        return Response.error("权限不足", 403)

    data = request.get_json()
    if not data:
        return Response.validation_error("缺少更新数据")

    # 验证 service_compose 格式
    if "service_compose" in data:
        if not isinstance(data["service_compose"], dict):
            return Response.validation_error("service_compose 必须是一个对象")
        if "services" not in data["service_compose"]:
            return Response.validation_error("service_compose 必须包含 services 字段")

    service = ProjectService()
    project, error = service.update_project(project_id, data, current_user)
    if error:
        return Response.error(error)

    return Response.success(project.to_dict(user=current_user), "项目更新成功")


@bp.route("/<int:project_id>", methods=["DELETE"])
@jwt_required()
@handle_api_errors
def delete_project(project_id):
    """删除项目"""
    current_user = get_current_user()
    service = ProjectService()
    if not current_user.is_admin:
        return Response.error("权限不足", 403)

    success, error = service.delete_project(project_id)
    if error:
        return Response.error(error)
    return Response.success(None, "项目删除成功")


# 设备项目关联相关的API
@bp.route("/devices/<int:device_id>", methods=["GET"])
@jwt_required()
@handle_api_errors
def list_device_projects(device_id):
    """获取设备的所有项目"""
    current_user = get_current_user()
    device_projects, error = DeviceProjectService.get_device_projects(
        device_id, current_user
    )
    if error:
        return Response.error(error, 400)

    metrics_service = MetricsService()
    result = []

    for dp in device_projects:
        metrics = metrics_service.get_service_metrics(
            device_id=device_id,
            service_name=dp.project.name
        )

        # Get decimals from project form_schema if available
        decimals = None
        if dp.project.form_schema and isinstance(dp.project.form_schema, dict):
            basic_info = dp.project.form_schema.get('basic', {})
            if basic_info and 'decimals' in basic_info:
                decimals = basic_info['decimals']

        # Set decimals on metrics object if available
        if metrics and decimals is not None:
            metrics.decimals = decimals

        dp_result = {
            "id": dp.id,
            "project": dp.project.to_dict(user=current_user),
            "state": dp.state,
            "data": dp.data,
            "created_at": dp.created_at.isoformat(),
            "updated_at": dp.updated_at.isoformat(),
            "metrics": metrics.to_metrics_dict() if metrics else {"running_time": 0, "points": 0},

        }

        last_status = {}
        last_metrics_detail = metrics_service.get_last_metrics_detail(device_id, service_name=dp.project.name)
        if dp.state == 'stopped' and last_metrics_detail:
            last_status['code'] = last_metrics_detail.status_code
            last_status['message'] = last_metrics_detail.status_msg
            last_status['updated_at'] = last_metrics_detail.updated_at.isoformat()
            # Add status_message based on code
            code_message_map = {
                400: '配置错误',
                401: '密码错误',
                402: 'token错误',
                452: '代理不可用',
                500: '服务内部错误',
                503: '服务暂时不可用',
            }
            from flask_babel import gettext as _
            status_message = code_message_map.get(last_metrics_detail.status_code, '')
            if status_message:
                last_status['status_message'] = _(status_message)
                dp_result.update({"status": last_status})

        result.append(dp_result)
    return Response.success(result)


@bp.route("/devices/<int:device_id>/<int:project_id>", methods=["POST"])
@jwt_required()
@handle_api_errors
def create_device_project(device_id, project_id):
    """为设备创建项目配置"""
    current_user = get_current_user()
    data = request.get_json()
    device_project, error = DeviceProjectService.create_device_project(
        device_id, project_id, data, current_user
    )
    if error:
        return Response.error(error, 400)
    return Response.success(device_project.to_dict())


@bp.route("/devices/<int:device_id>/<int:project_id>", methods=["PUT"])
@jwt_required()
@handle_api_errors
def update_device_project(device_id, project_id):
    """更新设备的项目配置"""
    current_user = get_current_user()
    data = request.get_json()
    device_project, error = DeviceProjectService.update_device_project(
        device_id, project_id, data, current_user
    )
    if error:
        return Response.error(error, 400)
    return Response.success(device_project.to_dict())


@bp.route("/devices/<int:device_id>/<int:project_id>", methods=["DELETE"])
@jwt_required()
@handle_api_errors
def delete_device_project(device_id, project_id):
    """删除设备的项目配置"""
    current_user = get_current_user()
    success, error = DeviceProjectService.delete_device_project(
        device_id, project_id, current_user
    )
    if error:
        return Response.error(error, 400)
    return Response.success(message="项目配置删除成功")
