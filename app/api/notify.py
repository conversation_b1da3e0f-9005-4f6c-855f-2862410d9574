from datetime import datetime, timezone
from zoneinfo import ZoneInfo

from flask import Blueprint, request
from flask_jwt_extended import get_jwt_identity

from app.enums.biz_enums import NotifyType
from app.middlewares.auth import admin_required
from app.services.notify_service import NotifyService
from app.utils.response import Response

notify_bp = Blueprint('notify', __name__)


@notify_bp.route('', methods=['GET'])
@admin_required
def get_notifications():
    """获取通知列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    title = request.args.get('title', None, type=str)
    content = request.args.get('content', None, type=str)
    notify_type_list = request.args.getlist('notify_type[]', type=NotifyType)
    status_list = request.args.getlist('notify_status[]', type=int)
    priority_list = request.args.getlist('notify_priority[]', type=int)
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    start_date = None
    if start_date_str:
        try:
            # 尝试将日期字符串转换为 datetime 对象
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d %H:%M:%S')
            start_date = start_date.astimezone(ZoneInfo('UTC'))
        except ValueError:
            start_date = None
    end_date = None
    if end_date_str:
        try:
            # 尝试将日期字符串转换为 datetime 对象
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d %H:%M:%S')
            end_date = end_date.astimezone(ZoneInfo('UTC'))
        except ValueError:
            end_date = None
    notifications, total = NotifyService.get_notifications(
        page=page,
        per_page=per_page,
        notify_type_list=notify_type_list,
        priority_list=priority_list,
        status_list=status_list,
        title=title,
        content=content,
        start_date=start_date,
        end_date=end_date
    )

    response_data = {
        'items': notifications,
        'total': total,
        'page': page,
        'per_page': per_page
    }
    return Response.success(response_data)


@notify_bp.route('', methods=['POST'])
@admin_required
def create_notification():
    """创建新通知"""

    data = request.get_json() or {}

    # 检查必填字段
    required_fields = ['notify_type', 'title', 'content']
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        raise ValueError(f"缺少必要字段：{', '.join(missing_fields)}")

    try:
        notification = NotifyService.create_notification(
            creator=get_jwt_identity(),
            notify_type=data['notify_type'],
            title=data['title'],
            content=data['content'],
            notify_priority=data.get('notify_priority'),
            user_ids=data.get('user_ids'),
            popup_show=data.get('popup_show'),
            popup_mode=data.get('popup_mode'),
            popup_date_end=data.get('popup_date_end'),
        )
    except ValueError as e:
        return Response.error(str(e))
    except Exception as e:
        return Response.error('操作失败')

    return Response.success(notification)


@notify_bp.route('/<int:notify_id>', methods=['PUT'])
@admin_required
def update_notification(notify_id):
    """更新通知"""

    data = request.get_json()
    if not data:
        return Response.validation_error("没有提供更新数据")
    user_id = get_jwt_identity()
    op_code = data.get('op')

    if op_code == 'update':
        try:
            NotifyService.update_notification(
                notify_id=notify_id,
                user_id=user_id,
                data=data
            )
        except ValueError as e:
            return Response.error(str(e))
        except Exception as e:
            return Response.error('操作失败')
    elif op_code == 'publish':
        NotifyService.publish_notify(notify_id, user_id)
    elif op_code == 'retract':
        NotifyService.retract_notify(notify_id, user_id)
    else:
        return Response.error("不支持的操作")
    return Response.success()


@notify_bp.route('/<int:notify_id>', methods=['DELETE'])
@admin_required
def delete_notification(notify_id):
    """删除通知"""

    NotifyService.delete_notification(
        notify_id=notify_id,
        user_id=get_jwt_identity()
    )

    return Response.success()