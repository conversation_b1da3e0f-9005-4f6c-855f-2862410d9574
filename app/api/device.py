"""设备 API 模块"""
from flask import Blueprint, current_app, request, jsonify
from flask_jwt_extended import get_jwt_identity, jwt_required
from collections import Counter

from app.enums.biz_enums import NotifyType, NotifyPriority
from app.middlewares.auth import admin_required
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.user import User
from app.services.device_service import DeviceService
from app.services.notify_service import NotifyService
from app.services.status_analysis_service import StatusAnalysisService
from app.services.project_service import ProjectService
from app.services.metrics_service import metrics_service
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.utils.exceptions import PermissionError
from app.utils.response import Response
from datetime import datetime
from sqlalchemy import func, text
from app.models.points import PointRecord

device_bp = Blueprint("device", __name__, url_prefix="/devices")
device_service = DeviceService()


@device_bp.route("", methods=["GET"])
@jwt_required()
def get_devices():
    """获取设备列表"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        # 获取分页参数
        page = request.args.get("page", 1, type=int)
        per_page = min(request.args.get("per_page", 10, type=int), 100)  # 限制最大为100

        # 获取过滤条件
        filters = {}
        if request.args.get("name"):
            filters["name"] = request.args.get("name")
        if request.args.get("status"):
            filters["status"] = request.args.get("status")
        if request.args.getlist("tags[]"):
            filters["tags"] = request.args.getlist("tags[]")
        if request.args.get("mac_address"):
            filters["mac"] = request.args.get("mac_address")
        if request.args.get("ip_address"):
            filters["ip"] = request.args.get("ip_address")

        # 获取 risk_type 请求参数
        requested_risk_types = request.args.getlist("risk_type[]")
        admin_can_see_risk = current_user.is_admin
        risk_device_ids_to_filter = set()
        # 风险设备识别逻辑 - 用于后续标记，以及在请求特定风险类型时进行预过滤
        all_risk_no_metrics_ids = set()
        all_risk_no_projects_ids = set()
        all_risk_prefix_not_match_ids = set()

        if admin_can_see_risk:
            # 1. 无上报指标的设备ID
            sql_no_metrics = text("""
            SELECT DISTINCT devices.id 
            FROM devices 
            WHERE devices.id NOT IN (SELECT DISTINCT(device_id) FROM service_metric_details)
            """)
            result_no_metrics = db.session.execute(sql_no_metrics).fetchall()
            all_risk_no_metrics_ids = {row.id for row in result_no_metrics}

            # 2. 未创建项目的设备ID
            sql_no_projects = text("""
            SELECT DISTINCT devices.id 
            FROM devices 
            WHERE devices.id NOT IN (SELECT DISTINCT(device_id) FROM device_projects)
            """)
            result_no_projects = db.session.execute(sql_no_projects).fetchall()
            all_risk_no_projects_ids = {row.id for row in result_no_projects}

            # 3. MAC地址前缀不匹配的设备ID
            sql_prefix_not_match = text("""
            SELECT DISTINCT devices.id
            FROM devices
            WHERE devices.mac_address NOT LIKE 'E0:76%'
            """)
            result_prefix_not_match = db.session.execute(sql_prefix_not_match).fetchall()
            all_risk_prefix_not_match_ids = {row.id for row in result_prefix_not_match}

            if requested_risk_types: # 如果管理员请求了特定的风险类型
                # 初始化一个包含所有设备ID的集合，用于后续求交集
                # 或者，如果没有任何风险类型被请求，则不进行ID过滤
                intersect_risk_ids = None

                if 'no_metrics_reported' in requested_risk_types:
                    if intersect_risk_ids is None:
                        intersect_risk_ids = all_risk_no_metrics_ids.copy()
                    else:
                        intersect_risk_ids.intersection_update(all_risk_no_metrics_ids)
                
                if 'no_projects_created' in requested_risk_types:
                    if intersect_risk_ids is None:
                        intersect_risk_ids = all_risk_no_projects_ids.copy()
                    else:
                        intersect_risk_ids.intersection_update(all_risk_no_projects_ids)
                
                if 'prefix_not_match' in requested_risk_types:
                    if intersect_risk_ids is None:
                        intersect_risk_ids = all_risk_prefix_not_match_ids.copy()
                    else:
                        intersect_risk_ids.intersection_update(all_risk_prefix_not_match_ids)
                
                # 如果用户请求了风险类型，但没有一个设备同时满足所有条件，
                # 或者用户传入了无效的风险类型组合导致交集为空
                if intersect_risk_ids is not None and not intersect_risk_ids:
                    filters["ids"] = [-1] # 传入一个不可能匹配的ID，确保结果为空
                elif intersect_risk_ids is not None:
                    filters["ids"] = list(intersect_risk_ids)
                # 如果 requested_risk_types 不为空，但 intersect_risk_ids 仍为 None
                # (例如，传入了未知的 risk_type)，也应返回空
                elif requested_risk_types and intersect_risk_ids is None:
                    filters["ids"] = [-1]

        devices, total = device_service.get_device_list(
            current_user,
            filters=filters, # 将包含风险设备ID的过滤器传入
            page=page,
            per_page=per_page
        )

        devices_and_projects_info = []
        for d in devices:
            device_info = d.to_dict(include_sensitive=False)
            actual_risks_on_device = []

            if admin_can_see_risk:
                if d.id in all_risk_no_metrics_ids:
                    actual_risks_on_device.append('no_metrics_reported')
                if d.id in all_risk_no_projects_ids:
                    actual_risks_on_device.append('no_projects_created')
                if d.id in all_risk_prefix_not_match_ids:
                    actual_risks_on_device.append('prefix_not_match')
            
            # 标记风险类型，即使是预过滤过的，也需要标记出来
            if admin_can_see_risk:
                device_info['risk_type'] = actual_risks_on_device
            else:
                device_info['risk_type'] = []

            # 管理员视角不需要 dp 信息
            if current_user.is_admin:
                devices_and_projects_info.append(device_info)
                continue
            # Enhanced project information
            enhanced_projects = []
            for dp in d.device_projects:
                # Get the basic device project info
                dp_info = dp.to_dict()
                
                # Get the associated project details
                if dp.project:
                    if dp.project.status != Project.STATUS_ENABLED or dp.project.deleted_at is not None:
                        continue

                    # Include project details from the Project model
                    project_info = dp.project.to_dict(user=current_user)
                    
                    # Add project details to the device project info
                    dp_info['project_info'] = project_info
                    
                    # Extract basic info from form_schema if available
                    if project_info.get('form_schema') and isinstance(project_info['form_schema'], dict):
                        basic_info = project_info['form_schema'].get('basic', {})
                        if basic_info:
                            dp_info['icon'] = basic_info.get('icon')
                            dp_info['coin'] = basic_info.get('coin')
                
                enhanced_projects.append(dp_info)
            
            device_info['projects'] = enhanced_projects

            # Count project states
            states = [item['state'] for item in enhanced_projects]
            states = dict(Counter(states))
            device_info['project_states'] = states
            
            devices_and_projects_info.append(device_info)

        return Response.success({
            "items": devices_and_projects_info,
            "total": total,
            "page": page,
            "per_page": per_page
        })
    except Exception as e:
        current_app.logger.error(f"Get devices error: {str(e)}")
        return Response.error("获取设备列表失败", 500)


@device_bp.route("", methods=["POST"])
@jwt_required()
def create_device():
    """创建设备"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        data = request.get_json()
        if not data or not all(
            k in data for k in ("name", "ip_address", "mac_address")
        ):
            return Response.validation_error("缺少必要字段")

        device = Device.query.filter_by(mac_address=data["mac_address"]).first()
        if device:
            if device.owner_id is None:
                # 设备无所有者（自动创建的设备），更新设备信息并绑定给当前用户
                device_service.update_device(device.id, {
                    "name": data.get("name", device.name),
                    "ip_address": data.get("ip_address", device.ip_address),
                    "description": data.get("description", device.description),
                    "hardware_info": data.get("hardware_info", device.hardware_info),
                    "owner_id": current_user.id
                }, current_user)
                return Response.success(device.to_dict(include_sensitive=False), "设备绑定成功")
            elif device.owner_id == current_user.id:
                # 设备已经被当前用户绑定
                return Response.success(device.to_dict(include_sensitive=False),
                                        "This device has been bound to you.", code=201)
            else:
                # 设备已被其他用户绑定，发送安全通知并返回错误
                owner = device.owner
                device_id_suffix = device.mac_address[-4:] if device.id is not None else ""
                attempt_time = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
                attempt_user_info = current_user.username[:4]
                try:
                    notify = NotifyService.create_notification(
                        -1,
                        NotifyType.SECURITY,
                        title={"en_US": "Device Binding Alert"},
                        content={
                            "en_US": f"Your device {device_id_suffix} was attempted to be bound at {attempt_time} by user {attempt_user_info}. If you did not authorize this action, please secure your account immediately."},
                        notify_priority=NotifyPriority.HIGH,
                        user_ids=[owner.id]
                    )
                    NotifyService.publish_notify(notify['id'], -1)
                except Exception as e:
                    current_app.logger.warning(f"Failed to send device bind alert: {str(e)}")

                return Response.error("设备 mac 地址已存在", 400)

        # 设置设备所有者为创建者
        data["owner_id"] = current_user.id
        device = device_service.create_device(data, current_user)
        return Response.success(device.to_dict(include_sensitive=False), "设备创建成功")

    except Exception as e:
        current_app.logger.error(f"Create device error: {str(e)}")
        return Response.error("创建设备失败", 500)


@device_bp.route("/<int:device_id>", methods=["GET"])
@jwt_required()
def get_device(device_id):
    """获取设备详情"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        device = device_service.get_device(device_id, current_user)
        if not device:
            return Response.not_found("设备不存在")

        device_info = device.to_dict(include_sensitive=False)
        device_info["runtime"] = {
            "running_time": metrics_service.get_device_total_running_time(device_id)
        }

        if device.service_configs:
            device_info["proxy"] = device.service_configs["proxy"]
            
        # Get total points for the current user
        total_points = UbiPointsIntegrationService.get_user_ubi_points(user_id=current_user.id)
            
        device_info["total_points"] = total_points

        return Response.success(device_info)
    except PermissionError:
        return Response.forbidden()
    except Exception as e:
        current_app.logger.error(f"Get device error: {str(e)}")
        return Response.error("获取设备详情失败", 500)


@device_bp.route("/<int:device_id>", methods=["PUT"])
@jwt_required()
def update_device(device_id):
    """更新设备"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        data = request.get_json()
        if not data:
            return Response.validation_error("没有提供更新数据")

        device = device_service.update_device(device_id, data, current_user)
        if not device:
            return Response.not_found("设备不存在")

        return Response.success(device.to_dict(include_sensitive=False), "设备更新成功")
    except PermissionError:
        return Response.forbidden()
    except Exception as e:
        current_app.logger.error(f"Update device error: {str(e)}")
        return Response.error("更新设备失败", 500)


@device_bp.route("/<int:device_id>", methods=["DELETE"])
@jwt_required()
def delete_device(device_id):
    """删除设备"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        success = device_service.delete_device(device_id, current_user)
        if not success:
            return Response.not_found("设备不存在")

        return Response.success(None, "设备删除成功")
    except PermissionError:
        return Response.forbidden()
    except Exception as e:
        current_app.logger.error(f"Delete device error: {str(e)}")
        return Response.error("删除设备失败", 500)

@device_bp.route('/<int:device_id>/status_analysis', methods=['GET'])
@jwt_required()
def get_status_analysis(device_id):
    current_user = db.session.get(User, get_jwt_identity())
    if not current_user or current_user.role != "admin":
        return Response.forbidden()

    try:
        result = StatusAnalysisService.analyze_status(device_id=device_id)
        return Response.success(result)
    except Exception as e:
        current_app.logger.error(f'状态分析失败: {str(e)}')
        return Response.error('服务器内部错误', 500)


@device_bp.route("/<int:device_id>/authorize", methods=["POST"])
@jwt_required()
def authorize_device(device_id):
    """
    授权设备给用户
    :param device_id:
    :return:
    """
    current_user = db.session.get(User, get_jwt_identity())
    if not current_user or current_user.role != "admin":
        return Response.forbidden()

    data = request.get_json()
    if not data or "user_id" not in data:
        return Response.validation_error("缺少用户ID")

    association = DeviceService.authorize_device(
        device_id=device_id,
        user_id=data["user_id"],
    )

    if not association:
        return Response.error("该用户已被授权访问此设备", 400)

    return Response.success(None, "设备授权成功")


@device_bp.route("/batch_authorize", methods=["POST"])
@jwt_required()
def batch_authorize_by_tags():
    """
    根据标签批量授权设备
    :return:
    """
    current_user = db.session.get(User, get_jwt_identity())
    if not current_user or current_user.role != "admin":
        return Response.forbidden()

    data = request.get_json()
    if not data or not all(k in data for k in ("tags", "user_id")):
        return Response.validation_error("缺少必要字段")

    if not data["tags"]:
        return Response.validation_error("没有提供标签")

    associations = DeviceService.batch_authorize_by_tags(
        tags=data["tags"],
        user_id=data["user_id"],
    )

    return Response.success(
        {"count": len(associations)}, f"成功授权 {len(associations)} 个设备"
    )


@device_bp.route("/<int:device_id>/projects/<int:project_id>/service_config", methods=["GET"])
@jwt_required()
def get_device_project_service_config(device_id, project_id):
    """获取设备项目的服务配置"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        device = Device.query.get(device_id)
        if not device:
            return Response.not_found("设备不存在")

        if not current_user.is_admin and not device.has_user_permission(current_user.id):
            return Response.forbidden()

        device_project = DeviceProject.query.filter_by(
            device_id=device_id,
            project_id=project_id
        ).first()
        if not device_project:
            return Response.not_found("设备项目配置不存在")

        # 生成服务配置
        project_service = ProjectService()
        service_config = project_service.generate_device_project_config(device_project)
        if not service_config:
            return Response.error("生成服务配置失败")

        return Response.success({"config": service_config})
    except Exception as e:
        current_app.logger.error(f"Get device project service config error: {str(e)}")
        return Response.error("获取设备项目服务配置失败", 500)


@device_bp.route("/<int:device_id>/system_app_configs/regenerate", methods=["POST"])
@jwt_required()
def regenerate_system_app_configs(device_id):
    """重新生成设备的系统应用配置"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user or current_user.role != "admin":
            return Response.forbidden("需要管理员权限")

        device = Device.query.get(device_id)
        if not device:
            return Response.not_found("设备不存在")

        # 使用 DeviceService 重新生成系统应用配置
        device.system_app_configs = device_service.generate_system_app_configs(device.id)
        db.session.commit()

        return Response.success(device.to_dict(include_sensitive=True), "系统应用配置重新生成成功")
    except Exception as e:
        current_app.logger.error(f"Regenerate system app configs error: {str(e)}")
        return Response.error("重新生成系统应用配置失败", 500)


@device_bp.route("/<int:device_id>/runtime", methods=["GET"])
@jwt_required()
def get_device_runtime(device_id: int):
    """获取设备运行时信息"""
    device_service = DeviceService()
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    runtime = device_service.get_device_runtime(device_id, user)
    if runtime is None:
        if not device_service.get_device(device_id, user):
            return jsonify({
                "code": 404,
                "message": "设备不存在",
                "data": None
            }), 404

    return jsonify({
        "code": 200,
        "message": "success",
        "data": runtime
    })


import random
import string

# 维护在代码中的确认码列表
VALID_CONFIRMATION_CODES = ["aB3xZ7qP", "K9sLpW2R"] # 更新为新的随机码


@device_bp.route("/batch_delete", methods=["POST"])
@admin_required
def batch_delete_devices():
    """批量删除设备"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user:
            return Response.forbidden()

        data = request.get_json()
        if not data:
            return Response.validation_error("请求体不能为空")

        device_ids = data.get("device_ids")
        confirmation_code = data.get("confirmation_code")

        if not device_ids or not isinstance(device_ids, list):
            return Response.validation_error("device_ids 不能为空且必须是列表")

        if not confirmation_code:
            return Response.validation_error("确认码不能为空")

        if confirmation_code not in VALID_CONFIRMATION_CODES:
            return Response.validation_error("确认码错误")

        delete_success = device_service.batch_delete_devices(device_ids, current_user)

        if not delete_success:
            return Response.error(f"设备删除失败", 400)

        return Response.success(message=f"成功删除")

    except Exception as e:
        current_app.logger.error(f"Batch delete devices error: {str(e)}")
        return Response.error("批量删除设备失败", 500)
