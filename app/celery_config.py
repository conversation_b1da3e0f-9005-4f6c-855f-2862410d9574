"""Celery 配置文件

包含积分优化相关的定时任务配置
"""

from celery.schedules import crontab

# Celery 定时任务配置
CELERYBEAT_SCHEDULE = {
    # 每天凌晨2点归档前一天的积分数据
    'auto-archive-daily-points': {
        'task': 'app.tasks.point_optimization_tasks.auto_archive_daily_points',
        'schedule': crontab(hour=2, minute=0),
        'options': {
            'expires': 3600,  # 1小时后过期
            'retry': True,
            'retry_policy': {
                'max_retries': 3,
                'interval_start': 60,
                'interval_step': 60,
                'interval_max': 300,
            }
        }
    },
    
    # 每天凌晨1点生成前一天的积分统计快照
    'generate-daily-point-snapshots': {
        'task': 'app.tasks.point_optimization_tasks.generate_daily_point_snapshots',
        'schedule': crontab(hour=1, minute=0),
        'options': {
            'expires': 3600,
            'retry': True,
            'retry_policy': {
                'max_retries': 2,
                'interval_start': 120,
                'interval_step': 120,
                'interval_max': 600,
            }
        }
    },
    
    # 每小时执行一次渐进式用户积分迁移
    'gradual-migrate-user-points': {
        'task': 'app.tasks.point_optimization_tasks.gradual_migrate_user_points',
        'schedule': crontab(minute=0),  # 每小时的0分
        'kwargs': {
            'batch_size': 500,
            'max_users': 2000
        },
        'options': {
            'expires': 3000,  # 50分钟后过期
            'retry': True,
            'retry_policy': {
                'max_retries': 3,
                'interval_start': 300,
                'interval_step': 300,
                'interval_max': 900,
            }
        }
    },
    
    # 每6小时执行一次渐进式历史数据归档
    'gradual-archive-historical-data': {
        'task': 'app.tasks.point_optimization_tasks.gradual_archive_historical_data',
        'schedule': crontab(hour='*/6', minute=30),  # 每6小时的30分
        'kwargs': {
            'days_per_batch': 5,
            'max_days': 30
        },
        'options': {
            'expires': 18000,  # 5小时后过期
            'retry': True,
            'retry_policy': {
                'max_retries': 2,
                'interval_start': 600,
                'interval_step': 600,
                'interval_max': 1800,
            }
        }
    },
    
    # 每周日凌晨3点清理90天前的已归档记录
    'cleanup-old-point-records': {
        'task': 'app.tasks.point_optimization_tasks.cleanup_old_point_records',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),  # 周日凌晨3点
        'kwargs': {
            'days_to_keep': 90
        },
        'options': {
            'expires': 7200,  # 2小时后过期
            'retry': True,
            'retry_policy': {
                'max_retries': 2,
                'interval_start': 300,
                'interval_step': 300,
                'interval_max': 900,
            }
        }
    },
    
    # 每周一凌晨4点归档上周的周数据
    'auto-archive-weekly-points': {
        'task': 'app.tasks.point_optimization_tasks.auto_archive_weekly_points',
        'schedule': crontab(hour=4, minute=0, day_of_week=1),  # 周一凌晨4点
        'options': {
            'expires': 7200,
            'retry': True,
            'retry_policy': {
                'max_retries': 2,
                'interval_start': 300,
                'interval_step': 300,
                'interval_max': 900,
            }
        }
    },
    
    # 每月1号凌晨5点归档上月的月数据
    'auto-archive-monthly-points': {
        'task': 'app.tasks.point_optimization_tasks.auto_archive_monthly_points',
        'schedule': crontab(hour=5, minute=0, day=1),  # 每月1号凌晨5点
        'options': {
            'expires': 7200,
            'retry': True,
            'retry_policy': {
                'max_retries': 2,
                'interval_start': 300,
                'interval_step': 300,
                'interval_max': 900,
            }
        }
    },
}

# Celery 任务路由配置
CELERY_ROUTES = {
    # 积分优化任务使用专门的队列
    'app.tasks.point_optimization_tasks.*': {
        'queue': 'point_optimization',
        'routing_key': 'point_optimization',
    },
    
    # 归档任务使用低优先级队列
    'app.tasks.point_optimization_tasks.auto_archive_*': {
        'queue': 'archive',
        'routing_key': 'archive',
        'priority': 3,
    },
    
    # 迁移任务使用中等优先级队列
    'app.tasks.point_optimization_tasks.gradual_migrate_*': {
        'queue': 'migration',
        'routing_key': 'migration',
        'priority': 5,
    },
    
    # 快照生成使用高优先级队列
    'app.tasks.point_optimization_tasks.generate_*': {
        'queue': 'snapshot',
        'routing_key': 'snapshot',
        'priority': 7,
    },
}

# 任务默认配置
CELERY_TASK_DEFAULT_QUEUE = 'default'
CELERY_TASK_DEFAULT_EXCHANGE = 'default'
CELERY_TASK_DEFAULT_EXCHANGE_TYPE = 'direct'
CELERY_TASK_DEFAULT_ROUTING_KEY = 'default'

# 队列配置
CELERY_TASK_QUEUES = {
    'default': {
        'exchange': 'default',
        'exchange_type': 'direct',
        'routing_key': 'default',
    },
    'point_optimization': {
        'exchange': 'point_optimization',
        'exchange_type': 'direct',
        'routing_key': 'point_optimization',
    },
    'archive': {
        'exchange': 'archive',
        'exchange_type': 'direct',
        'routing_key': 'archive',
    },
    'migration': {
        'exchange': 'migration',
        'exchange_type': 'direct',
        'routing_key': 'migration',
    },
    'snapshot': {
        'exchange': 'snapshot',
        'exchange_type': 'direct',
        'routing_key': 'snapshot',
    },
}

# 任务执行配置
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True

# 结果后端配置
CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'
CELERY_RESULT_EXPIRES = 3600  # 结果保存1小时

# 任务限制配置
CELERY_TASK_SOFT_TIME_LIMIT = 1800  # 软限制30分钟
CELERY_TASK_TIME_LIMIT = 3600       # 硬限制1小时
CELERY_TASK_MAX_RETRIES = 3
CELERY_TASK_DEFAULT_RETRY_DELAY = 60

# 工作进程配置
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_WORKER_DISABLE_RATE_LIMITS = False

# 监控配置
CELERY_SEND_TASK_EVENTS = True
CELERY_SEND_EVENTS = True
CELERY_TASK_SEND_SENT_EVENT = True

# 日志配置
CELERY_WORKER_HIJACK_ROOT_LOGGER = False
CELERY_WORKER_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
CELERY_WORKER_TASK_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

# 安全配置
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_TASK_ACKS_LATE = True

# 性能优化配置
CELERY_TASK_COMPRESSION = 'gzip'
CELERY_RESULT_COMPRESSION = 'gzip'
CELERY_TASK_IGNORE_RESULT = False

# 错误处理配置
CELERY_TASK_ANNOTATIONS = {
    '*': {
        'rate_limit': '100/m',  # 每分钟最多100个任务
    },
    'app.tasks.point_optimization_tasks.auto_archive_daily_points': {
        'rate_limit': '10/h',   # 归档任务每小时最多10个
        'time_limit': 1800,     # 30分钟超时
    },
    'app.tasks.point_optimization_tasks.generate_daily_point_snapshots': {
        'rate_limit': '10/h',
        'time_limit': 1200,     # 20分钟超时
    },
    'app.tasks.point_optimization_tasks.gradual_migrate_user_points': {
        'rate_limit': '20/h',
        'time_limit': 2400,     # 40分钟超时
    },
    'app.tasks.point_optimization_tasks.cleanup_old_point_records': {
        'rate_limit': '1/h',    # 清理任务每小时最多1个
        'time_limit': 3600,     # 1小时超时
    },
}

# 开发环境配置
if __name__ == '__main__':
    # 开发环境下的特殊配置
    CELERYBEAT_SCHEDULE.update({
        # 开发环境下每5分钟执行一次测试任务
        'test-point-optimization': {
            'task': 'app.tasks.point_optimization_tasks.gradual_migrate_user_points',
            'schedule': crontab(minute='*/5'),
            'kwargs': {
                'batch_size': 10,
                'max_users': 50
            },
            'options': {
                'expires': 240,
            }
        },
    })
    
    # 开发环境下降低任务频率
    CELERY_TASK_ANNOTATIONS.update({
        '*': {
            'rate_limit': '10/m',
        },
    })
