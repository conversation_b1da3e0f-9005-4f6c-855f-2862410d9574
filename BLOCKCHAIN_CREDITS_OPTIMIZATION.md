# 区块链监听服务 Credits 优化方案

## 🎯 优化目标

减少 Web3 API 调用次数，降低 Credits 消耗，同时保持监听服务的准确性和实时性。

## 📊 当前 Credits 消耗分析

### 高频调用（每30秒）
- `eth_blockNumber` - 获取最新区块号
- `eth_getBlockByNumber` - 获取区块详情（包含所有交易）

### 中频调用（每个交易）
- `eth_getCode` - 检查合约代码
- `eth_call` - 获取代币精度（decimals）

### 低频调用（资产同步时）
- `eth_getBalance` - 获取ETH余额
- `eth_call` - 获取ERC20余额（balanceOf）

## 🔧 优化措施

### 1. 智能间隔调整

**原来**：固定30秒间隔
**优化后**：根据链特性和同步状态动态调整

```python
# 配置示例
CHAIN_BLOCK_INTERVALS = {
    "ETH": {"base_interval": 12, "max_interval": 60},
    "BSC": {"base_interval": 3, "max_interval": 30},
    "ARB": {"base_interval": 1, "max_interval": 15},
}

# 动态计算
if blocks_behind == 0:
    interval = base_interval * 2  # 已同步，使用较长间隔
elif blocks_behind <= 3:
    interval = base_interval      # 接近最新，正常间隔
else:
    interval = base_interval // 2 # 落后较多，快速追赶
```

**预期节省**：30-50% 的区块查询调用

### 2. 地址类型缓存

**原来**：每个交易都调用 `eth_getCode` 检查合约
**优化后**：缓存已知的合约地址和EOA地址

```python
# 地址类型缓存
_address_cache = {
    'contracts': set(),  # 已知合约地址
    'eoa': set()        # 已知EOA地址
}

# 启发式判断
def _should_check_contract_code(tx_to):
    if tx_to in _address_cache['contracts']:
        return True
    if tx_to in _address_cache['eoa']:
        return False
    # 启发式：合约地址通常以000000结尾
    return tx_to.endswith('000000')
```

**预期节省**：70-90% 的 `eth_getCode` 调用

### 3. 代币精度缓存

**原来**：每次代币交易都查询精度
**优化后**：缓存代币精度，避免重复查询

```python
# 代币精度缓存
_decimals_cache = {}

async def get_evm_token_decimals_cached(web3, token_address):
    if token_address in _decimals_cache:
        return _decimals_cache[token_address]
    
    decimals = await get_evm_token_decimals(web3, token_address)
    _decimals_cache[token_address] = decimals
    return decimals
```

**预期节省**：80-95% 的代币精度查询调用

### 4. 交易过滤优化

**原来**：所有交易都入库，然后标记忽略
**优化后**：提前过滤，只处理相关交易

```python
# 提前检查是否涉及系统用户
from_user_mapping = self._get_user_mapping(tx.to_address, chain.chain_code)
to_user_mapping = self._get_user_mapping(tx.from_address, chain.chain_code)

if not from_user_mapping and not to_user_mapping:
    return False  # 直接跳过，不入库，不进行后续处理
```

**预期节省**：减少99%+ 的无关交易处理

### 5. 批量处理

**计划实现**：批量获取区块和交易信息

```python
# 批量获取多个区块
blocks = await batch_get_blocks(start_block, end_block)

# 批量检查合约代码
contract_codes = await batch_get_codes(addresses)
```

**预期节省**：20-30% 的总体 API 调用

## 📈 性能监控

### 监控指标

1. **API 调用统计**
   - 各类型 API 调用次数
   - 平均响应时间
   - 总调用次数

2. **缓存效果**
   - 缓存命中率
   - 节省的 API 调用次数

3. **处理效率**
   - 区块处理速度
   - 交易过滤率
   - 平均处理时间

### 监控报告示例

```
🔍 区块链监听服务性能报告
========================================
⏱️  运行时间: 3600 秒
📦 处理区块: 300 个
💳 处理交易: 15000 笔
🔽 过滤交易: 14950 笔 (99.67%)
⚡ 处理速度: 5.0 区块/分钟

📡 API 调用统计 (总计: 450 次):
  eth_blockNumber: 120 次 (平均 0.1s)
  eth_getBlockByNumber: 300 次 (平均 0.3s)
  eth_getCode: 30 次 (平均 0.2s)

💾 缓存统计:
  contract_code: 95.5% 命中率 (573/600)
  decimals: 88.2% 命中率 (45/51)

💰 估算 Credits 节省: 618 次调用
```

## 🎯 预期优化效果

### Credits 节省估算

| 优化项目 | 原调用次数 | 优化后调用次数 | 节省比例 |
|---------|-----------|---------------|----------|
| 区块查询间隔 | 120/小时 | 60/小时 | 50% |
| 合约代码检查 | 1000/小时 | 100/小时 | 90% |
| 代币精度查询 | 200/小时 | 20/小时 | 90% |
| 交易过滤 | 处理15000笔 | 处理50笔 | 99.67% |

**总体预期节省**：70-85% 的 API 调用次数

### 实施优先级

1. **高优先级**（立即实施）
   - ✅ 智能间隔调整
   - ✅ 地址类型缓存
   - ✅ 代币精度缓存
   - ✅ 交易提前过滤

2. **中优先级**（1-2周内）
   - 🔄 批量 API 调用
   - 🔄 更智能的启发式判断
   - 🔄 预填充热门合约地址

3. **低优先级**（长期优化）
   - ⏳ 本地区块链节点
   - ⏳ WebSocket 连接
   - ⏳ 事件订阅模式

## 🚀 使用方法

### 1. 启用优化配置

```python
# 在配置文件中启用优化
from app.config.blockchain_optimization import API_OPTIMIZATION

API_OPTIMIZATION = {
    "enable_contract_cache": True,
    "enable_decimals_cache": True,
    "skip_zero_value_tx": True,
}
```

### 2. 监控性能

```python
from app.utils.blockchain_performance_monitor import performance_monitor

# 在监听服务中定期输出报告
performance_monitor.log_report_if_needed(interval_seconds=300)
```

### 3. 查看实时统计

```python
# 获取性能摘要
summary = performance_monitor.get_performance_summary()
print(f"总 API 调用: {summary['total_api_calls']}")

# 获取缓存统计
cache_stats = performance_monitor.get_cache_summary()
print(f"合约缓存命中率: {cache_stats['contract_code']['hit_rate_percent']}%")
```

## 💡 进一步优化建议

1. **使用 WebSocket**：替代轮询模式，实时接收新区块
2. **本地节点**：部署本地区块链节点，减少外部 API 依赖
3. **事件过滤**：使用 eth_getLogs 直接获取相关事件
4. **数据库优化**：优化查询，减少重复检查

## 📝 注意事项

1. **缓存管理**：定期清理缓存，避免内存泄漏
2. **数据一致性**：确保缓存数据的准确性
3. **错误处理**：优化失败时的降级策略
4. **监控告警**：设置 Credits 使用量告警

通过这些优化措施，预计可以将 Credits 消耗降低 70-85%，同时保持监听服务的准确性和实时性。
