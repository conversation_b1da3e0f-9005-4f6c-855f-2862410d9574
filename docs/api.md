# Linux设备管理系统 API 文档

## 基础信息

- 基础URL: `https://api.pinpool.net/`
- 认证方式: JWT <PERSON> (在请求头中添加 `Authorization: Bearer <token>`)
- 响应格式: JSON

## 通用响应格式

```json
{
    "code": 200,       // 状态码
    "message": "成功",  // 响应消息
    "data": {}         // 响应数据
}
```

## 1. 认证相关 API

### 1.1 用户注册

- **接口**: `/auth/register`
- **方法**: `POST`
- **描述**: 注册新用户
- **请求体**:
  ```json
  {
    "username": "string",  // 用户名
    "email": "string",     // 邮箱
    "password": "string",  // 密码
    "invite_code": "string"       // 邀请码
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "id": 1,
      "username": "string",
      "email": "string",
      "role": "string",
      "token": "string"
    }
  }
  ```

### 1.2 用户登录

- **接口**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录获取token
- **请求体**:
  ```json
  {
    "username": "string",  // 用户名
    "password": "string",   // 密码
    "verifyCode": "string", // 用户填的验证码
    "verifyToken": "string" // 生成的验证码图像ID
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "string",
      "user": {
        "id": 1,
        "username": "string",
        "email": "string",
        "role": "string"
      }
    }
  }
  ```

### 1.3 获取用户列表

- **接口**: `/auth/users`
- **方法**: `GET`
- **描述**: 获取用户列表（仅管理员可用）
- **权限**: 需要管理员权限
- **查询参数**:
  - `page`: 页码（默认1）
  - `per_page`: 每页数量（默认10）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "items": [
        {
          "id": 1,
          "username": "string",
          "email": "string",
          "role": "string"
        }
      ],
      "total": 100,
      "page": 1,
      "per_page": 10
    }
  }
  ```

### 1.4 更新用户信息

- **接口**: `/auth/users/<user_id>`
- **方法**: `PUT`
- **描述**: 更新用户信息（仅管理员可用）
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "username": "string",  // 可选
    "email": "string",     // 可选
    "role": "string"       // 可选
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "用户信息更新成功",
    "data": {
      "id": 1,
      "username": "string",
      "email": "string",
      "role": "string"
    }
  }
  ```

### 1.5 获取用户资料

- **接口**: `/auth/profile`
- **方法**: `GET`
- **描述**: 获取当前登录用户的资料
- **权限**: 需要登录
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "username": "string",
      "email": "string",
      "role": "string"
    }
  }
  ```

### 1.6 获取用户统计汇总信息

- **接口**: `/auth/statistics`
- **方法**: `GET`
- **描述**: 获取用户的统计汇总信息，包括设备总数、在线设备数、总运行时间和总积分
- **权限**: 需要登录
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "total_devices": 2,         // 设备总数
      "online_devices": 1,        // 在线设备数
      "total_running_time": 10800, // 总运行时间（秒）
      "total_points": 300         // 总积分
    }
  }
  ```

### 1.7 获取用户每日统计数据

- **接口**: `/auth/statistics/daily`
- **方法**: `GET`
- **描述**: 获取用户的每日统计数据，包括每个服务的积分和运行时间
- **权限**: 需要登录
- **查询参数**:
  - `start_date`: 开始日期，格式：YYYY-MM-DD（可选）
  - `end_date`: 结束日期，格式：YYYY-MM-DD（可选）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "2024-02-13": {
        "service_name": ["service_a", "service_b"],  // 服务名称列表
        "points": [100, 200],                        // 对应服务的积分列表
        "running_time": [3600, 7200],                // 对应服务的运行时间列表（秒）
        "points_increase": [100, 200],               // 对应服务的积分增量列表
        "running_time_increase": [3600, 7200]        // 对应服务的运行时间增量列表（秒）
      }
    }
  }
  ```
  
### 1.8 获取登陆验证码图像

- **接口**: `/api/auth/get-captcha`
- **方法**: `GET`
- **描述**: 获取验证码图像，并返回验证码 ID

响应:
```json
{
  "data": byte_string
}
```
	•	响应头：
	•	Content-Type: image/png — 表示返回的是 PNG 格式的验证码图片。
	•	Captcha-ID: <captcha_id> — 验证码的唯一标识符，用于后续验证。

错误响应:

{
  "code": 500,
  "message": "获取验证码图像错误"
}

### 1.9 忘记密码

#### (1) 请求发送重置密码链接

- **接口**: `/api/auth/request_reset_password`
- **方法**: `POST`
- **描述**: 输入邮箱并发送重置链接

- **请求体**:
  ```json
  {
    "email": "string"   //邮箱
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "Send success"
  }
  ```
  
#### （2）点击链接重置密码

- **接口**: `/api/auth/reset_password/<token>`
- **方法**: `POST`
- **描述**: 输入新密码进行重置

- **请求体**:
  ```json
  {
    "password": "string"   //新密码
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "Reset success"
  }
  ```

### 1.10 获取当前用户的邀请历史

- **接口**：`/api/auth/invite_history`
- **方法**：`POST`
- **描述**：获取当前用户的邀请历史记录。

- **请求体**:

```json
{
  "page": 1,        // 必填，页码
  "per_page": 10,   // 必填，每页条数
  "status": "string" // 可选，可取值 "pending" 或 "success"；不传则返回全部
}
```

- **响应**:

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "items": [
      {
        "id": 1,
        "user_id": 2,
        "points": 0,
        "record_type": "invite",
        "task_type_id": null,
        "task_type": null,
        "invitee_id": 3,
        "created_at": "2025-03-14T12:34:56.789000",
        "account": "<EMAIL>",
        "invited_date": "14 Mar, 2025",
        "device_binding_count": 1,
        "device_binding_time": "2025-03-14T12:34:56.789000"
      }
    ],
    "page": 1,
    "per_page": 10,
    "total": 15
  }
}
```

## 2. 设备管理 API

### 2.1 创建设备

- **接口**: `/devices`
- **方法**: `POST`
- **描述**: 创建新设备（仅管理员可用）
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "name": "string",        // 设备名称
    "ip_address": "string",  // IP地址
    "mac_address": "string"  // MAC地址
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,                 // 如果设备已被当前用户绑定，则code=201；如果设备已被其它用户绑定，则code=400
    "message": "设备创建成功",
    "data": {
      "id": 1,
      "name": "string",
      "ip_address": "string",
      "mac_address": "string",
      "status": "string"
    }
  }
  ```

### 2.2 获取设备列表

- **接口**: `/devices`
- **方法**: `GET`
- **描述**: 获取设备列表
- **权限**: 需要登录
- **说明**: 
  - 管理员可以看到所有设备，普通用户只能看到被授权的设备
  - 每页最多返回 100 条记录，如果请求超过 100 条会自动限制为 100 条
- **查询参数**:
  - `page`: 页码（默认1）
  - `per_page`: 每页数量（默认10，最大100）
  - `name`: 设备名称，支持模糊搜索
  - `mac_address`: MAC地址，支持模糊搜索
  - `ip_address`: IP地址，支持模糊搜索
  - `status`: 设备状态，支持精确匹配
  - `tags[]`: 标签列表，支持多个标签，格式为tags[]=tag1&tags[]=tag2
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "items": [
        {
          "id": 1,
          "name": "string",
          "ip_address": "string",
          "mac_address": "string",
          "status": "string",
          "tags": ["string"],
          "hardware_info": "string",
          "system_app_configs": {},
          "service_configs": {},
          "project_states": {
            "running": 3,
            "stopped": 1,
            "created": 1,
            "updated": 2
          },
          "projects": [{
			"created_at": "2025-02-23T15:24:39",
			"data": {
					"auth_token": "",
					"proxy_list": "XXX"
			},
			"device_id": 59,
			"id": 71,
			"project_id": 4,
			"state": 1,     // 0: initializing 1: wait to configure 2: running 3: failure 4: offline
			"updated_at": "2025-02-23T15:24:39"
			}]
        }
      ],
      "total": 100,    // 总记录数
      "page": 1,       // 当前页码
      "per_page": 10   // 每页记录数
    }
  }
  ```

### 2.3 更新设备信息

- **接口**: `/devices/<device_id>`
- **方法**: `PUT`
- **描述**: 更新设备信息（仅管理员可用）
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "name": "string",        // 可选
    "ip_address": "string",  // 可选
    "mac_address": "string"  // 可选
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "设备更新成功",
    "data": {
      "id": 1,
      "name": "string",
      "ip_address": "string",
      "mac_address": "string",
      "status": "string"
    }
  }
  ```

### 2.4 设备授权

- **接口**: `/devices/<device_id>/authorize`
- **方法**: `POST`
- **描述**: 授权设备给用户（仅管理员可用）
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "user_id": 1,                     // 用户ID
    "permission_type": "read"         // 权限类型（可选，默认为"read"）
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "设备授权成功",
    "data": null
  }
  ```

### 2.5 批量设备授权

- **接口**: `/devices/batch_authorize`
- **方法**: `POST`
- **描述**: 根据标签批量授权设备（仅管理员可用）
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "tags": ["tag1", "tag2"],        // 标签列表
    "user_id": 1,                    // 用户ID
    "permission_type": "read"        // 权限类型（可选，默认为"read"）
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功授权 n 个设备",
    "data": {
      "count": 5
    }
  }
  ```

### 2.6 获取设备运行时信息

- **接口**: `/devices/<device_id>/runtime`
- **方法**: `GET`
- **描述**: 获取设备的运行时信息，包括启动时间、运行时长、网络状态、硬件使用情况等。如果设备存在但没有运行时信息，将返回空的 data
- **参数**:
  - `device_id`: 设备ID（路径参数）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "device_id": 123,
      "started_at": "2024-02-15T12:00:00Z",    // 设备启动时间
      "running_time": 3600,                     // 运行时长（秒）
      "lan_ip": "*************",               // 局域网 IP
      "public_ip": "*******",                  // 公网 IP
      "network": {                             // 网络信息
        "download_speed": 1000000,             // 下载速度（字节/秒）
        "upload_speed": 500000                 // 上传速度（字节/秒）
      },
      "disk": {                               // 磁盘信息
        "total": 1000000000,                  // 总容量（字节）
        "used": 500000000,                    // 已使用（字节）
        "free": 500000000                     // 剩余空间（字节）
      },
      "cpu": {                                // CPU 信息
        "usage": 50.5,                        // CPU 使用率（百分比）
        "cores": 4                            // CPU 核心数
      },
      "memory": {                             // 内存信息
        "total": 8000000000,                  // 总内存（字节）
        "used": 4000000000,                   // 已使用（字节）
        "free": 4000000000                    // 剩余内存（字节）
      },
      "updated_at": "2024-02-15T12:10:00Z"    // 最后更新时间
    }
  }
  ```

  如果设备存在但没有运行时信息：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {}
  }
  ```

- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "设备不存在",
    "data": null
  }
  ```

## 3. 积分管理 API

### 3.1 完成任务获取积分
- **接口**: `/api/points/task`
- **方法**: `POST`
- **权限**: 需要登录
- **请求体**:
  ```json
  {
    "task_type": "string"  // 任务类型（如'signin'）
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "积分添加成功",
    "data": {
      "points": 100
    }
  }
  ```
- **错误码**:
  - 404: 任务类型不存在
  - 400: 该任务已完成

### 3.2 获取邀请码
- **接口**: `/api/points/invite-code`
- **方法**: `GET`
- **权限**: 需要登录
- **响应**:
  ```json
  {
    "code": 200,
    "data": {
      "invite_code": "ABCDEF"
    }
  }
  ```

### 3.3 获取积分记录
- **接口**: `/api/points/records`
- **方法**: `GET`
- **权限**: 需要登录
- **查询参数**:
  - `record_type`: "invite", "task", "project", 不填就返回全部
- **响应**:
  ```json
  {
	  "code": 200,
	  "data": [
	    {
	      "id": 1,
	      "user_id": 42,
	      "points": 100,
	      "record_type": "task",
	      "task_type_id": 5,
	      "task_type": "signin",
	      "invitee_id": 10,
	      "invitee_email": "<EMAIL>",
	      "created_at": "2023-01-01T12:00:00Z"
	    }
	  ]
	}
  ```
  
### 3.4 获取任务完成状态
- **接口**: `/api/points/task`
- **方法**: `GET`
- **描述**: 获取当前用户已完成的任务列表，该接口返回一个包含任务名称的数组。
- **响应**:
  ```json
  {
    "code": 200,
    "data": {
      "completed_tasks": ["Twitter", "Telegram", "Discord"]
    },
    "message": "操作成功"
  }
  
### 3.5 Telegram加群


- **接口**: `/api/points/telegram`
- **方法**: `POST`
- **描述**: 生成专属的 Telegram 邀请链接
根据平台用户的 ID 生成带有标识的邀请链接，用户点击后将跳转至 Telegram 加群页面。邀请链接中嵌入平台用户 ID，方便后续通过 Webhook 识别用户身份并发放积分。

- **响应**: 重定向到生成的 Telegram 邀请链接 (HTTP 302)

- **示例响应**:
HTTP 状态码 302，响应头中的 `Location` 为 Telegram 邀请链接，例如：
```arduino
Location: https://t.me/joinchat/XXXXXX
```


### 3.6 Telegram Webhook（由Telegram官方调用）


- **接口**: `/api/points/telegram_webhook`
- **方法**: `POST`
- **描述**: Telegram Bot 的 Webhook 接口
当用户通过邀请链接申请加入 Telegram 群组时，Telegram 会向此接口发送 `chat_join_request` 更新。后台根据更新中 `invite_link` 的 `name` 字段（格式为 `invite_user_&lt;user_id&gt;`）提取平台用户 ID，并执行积分发放操作。
- **认证**: 无（由 Telegram 服务器调用）
- **响应**:

```json
{
  "code": 200,
  "message": "积分添加成功",
  "data": {
    "points": 1
  }
}
```

### 3.7 获取 Discord 授权链接


- **接口**: `/api/points/discord`
- **方法**: `POST`
- **描述**: 生成 Discord 授权链接。平台用户在点击后会被重定向到 Discord 的 OAuth2 授权页面，进行授权操作。
- **认证**: 需要 JWT Token

- **响应**: 重定向到 Discord 授权页面 (HTTP 302)

- **示例响应**:
HTTP 状态码 302，响应头中的 `Location` 为 Discord 的 OAuth2 授权链接，例如：

```bash
Location: https://discord.com/api/oauth2/authorize?client_id=YOUR_DISCORD_CLIENT_ID&redirect_uri=YOUR_DISCORD_REDIRECT_URI&response_type=code&scope=identify%20guilds
```


### 3.8 Discord 授权回调（由Discord官方调用）


- **接口**: `/api/points/discord_callback`
- **方法**: `POST`
- **描述**: Discord 授权回调接口
当用户在 Discord 完成授权后，Discord 会携带 `code` 参数重定向到此接口。后端使用 `code` 换取 `access_token`，获取用户信息，并通过 Bot Token 检查用户是否已加入目标服务器，进而执行积分发放操作。
- **认证**: 无
- **请求参数**:

`code` (string): Discord 返回的授权码

- **响应**:

**成功**（用户已加入目标服务器）:

```json
{
  "code": 200,
  "message": "积分添加成功",
  "data": {
    "points": 1
  }
}
```

**失败**（如用户未加入服务器或其它错误）:

```json
{
    "code": 402,
    "message": "检测到你尚未加入服务器，请先加入我们的 Discord！",
    "data": {}
}
```


### 3.4 获取积分排行榜
- **接口**: `/api/points/records/rank`
- **方法**: `GET`
- **权限**: 需要登录
- **描述**: 获取用户积分排行榜，包括前30名用户的排名和当前用户的积分情况
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "ranking_list": [
        {
          "email": "<EMAIL>",
          "points": 1500.0
        },
        {
          "email": "<EMAIL>",
          "points": 1200.0
        },
        // 最多返回30条记录，按积分从高到低排序
      ],
      "myself": {
        "email": "<EMAIL>",
        "points": 800.0
      }
    }
  }
  ```
- **说明**:
  - `ranking_list`: 包含最多30名用户的积分排名，按积分从高到低排序
  - `myself`: 当前用户的邮箱和积分信息，无论当前用户是否在前30名内

## 4. 项目管理 API

### 4.1 获取项目列表

- **接口**: `/api/projects`
- **方法**: `GET`
- **描述**: 获取项目列表
- **权限**: 需要登录
- **说明**: 管理员可以看到所有项目，普通用户只能看到被授权的项目
- **查询参数**:
  - `page`: 页码（默认1）
  - `per_page`: 每页数量（默认10）
  - `status`: 项目状态（可选，0: disabled, 1: enabled）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": 1,
          "name": "string",
          "description": "string",
          "service_config_id": 1,
          "status": 1,  // 0: disabled, 1: enabled
          "form_schema": {
            "email": {
              "label": "邮箱",
              "type": "email"
            },
            "password": {
              "label": "密码",
              "type": "password"
            }
          },
          "metrics": {
            "device_count": 5,         // 关联设备数量
            "total_running_time": 3600, // 累积运行时长（秒）
            "total_points": 1000       // 累积收益（点数）
          },
          "files": [
            {
              "id": 1,
              "name": "config.txt",
              "content": "{{email}}:{{password}}"
            }
          ],
          "created_at": "string",
          "updated_at": "string",
          "b_new_label": true,            // 展示项目新增标签
          "special_login_hint": "string"  // 特殊登录提示
        }
      ],
      "total": 100,
      "page": 1,
      "per_page": 10
    }
  }
  ```

### 4.2 创建项目

- **接口**: `/api/projects`
- **方法**: `POST`
- **描述**: 创建新项目
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "name": "string",           // 项目名称（必填）
    "description": "string",    // 项目描述（必填）
    "service_config_id": 1,     // 服务配置ID（必填）
    "service_compose": {        // 项目自定义服务配置（可选）
      "services": {
        "app": {
          "image": "custom-image:latest",
          "environment": {
            "API_KEY": "{{ api_key }}",
            "USERNAME": "{{ username }}",
            "EMAIL": "{{ email }}",
            "DEBUG": "{{ debug }}"
          }
        }
      },
      "volumes": {},           // 可选
      "networks": {}           // 可选
    },
    "status": 1,               // 可选，项目状态，默认为 1 (enabled)
    "form_schema": {           // 表单配置（可选）
      "mqtt": {
        "type": "object",
        "title": "MQTT配置",
        "description": "MQTT服务连接配置",
        "required": ["host", "port", "client_id"],
        "properties": {
          "host": {
            "type": "string",
            "title": "服务器地址",
            "default": "mqtt.example.com"
          },
          "port": {
            "type": "integer",
            "title": "端口",
            "default": 1883,
            "minimum": 1,
            "maximum": 65535
          },
          "client_id": {
            "type": "string",
            "title": "客户端ID",
            "description": "唯一的客户端标识符"
          },
          "username": {
            "type": "string",
            "title": "用户名",
            "description": "认证用户名（可选）"
          },
          "password": {
            "type": "string",
            "title": "密码",
            "description": "认证密码（可选）"
          }
        }
      },
      "log_level": {
        "type": "string",
        "title": "日志级别",
        "enum": ["debug", "info", "warn", "error"],
        "default": "info"
      }
    },
    "files": [
      {
        "id": 1,
        "name": "mqtt.conf",
        "content": "host={{mqtt.host}}\nport={{mqtt.port}}\nclient_id={{mqtt.client_id}}\n{% if mqtt.username %}username={{mqtt.username}}{% endif %}\n{% if mqtt.password %}password={{mqtt.password}}{% endif %}\nlog_level={{log_level}}"
      }
    ],
    "special_login_hint": "",   // （可选）特殊登录提示60个字符上限
    "show_new_label": 1,        // （必选）是否显示新增标签 0否 1是
    "show_day": 1               // （显示新增标签后必选默认7天）展示时长 1天，3天，7天，14天，30天
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "项目创建成功",
    "data": {
      "id": 1,
      "name": "string",
      "description": "string",
      "service_config_id": 1,
      "service_compose": {
        "services": {},
        "volumes": {},
        "networks": {}
      },
      "status": 1,
      "form_schema": {
        "mqtt": {
          "host": "mqtt.example.com",
          "port": 1883,
          "client_id": "device-001",
          "username": "user1",
          "password": "pass123"
        },
        "log_level": "info"
      },
      "files": [
        {
          "id": 1,
          "name": "mqtt.conf",
          "content": "host={{mqtt.host}}\nport={{mqtt.port}}\nclient_id={{mqtt.client_id}}\n{% if mqtt.username %}username={{mqtt.username}}{% endif %}\n{% if mqtt.password %}password={{mqtt.password}}{% endif %}\nlog_level={{log_level}}"
        }
      ],
      "created_at": "string",
      "updated_at": "string",
      "special_login_hint": "",
      "show_new_label": 1,
      "show_day": 1
    }
  }
  ```

### 4.3 获取项目详情

- **接口**: `/api/projects/<project_id>`
- **方法**: `GET`
- **描述**: 获取项目详细信息
- **权限**: 需要项目的读取权限
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "id": 1,
      "name": "string",
      "description": "string",
      "service_config_id": 1,
      "service_compose": {
        "services": {},
        "volumes": {},
        "networks": {}
      },
      "status": 1,
      "form_schema": {
        "mqtt": {
          "host": "mqtt.example.com",
          "port": 1883,
          "client_id": "device-001",
          "username": "user1",
          "password": "pass123"
        },
        "log_level": "info"
      },
      "metrics": {
        "device_count": 5,         // 关联设备数量
        "total_running_time": 3600, // 累积运行时长（秒）
        "total_points": 1000       // 累积收益（点数）
      },
      "files": [
        {
          "id": 1,
          "name": "mqtt.conf",
          "content": "host={{mqtt.host}}\nport={{mqtt.port}}\nclient_id={{mqtt.client_id}}\n{% if mqtt.username %}username={{mqtt.username}}{% endif %}\n{% if mqtt.password %}password={{mqtt.password}}{% endif %}\nlog_level={{log_level}}"
        }
      ],
      "created_at": "string",
      "updated_at": "string",
      "special_login_hint": "",
      "show_new_label": 1,
      "show_day": 1,
      "b_new_label": true,            // 展示项目新增标签
      "special_login_hint": "string"  // 特殊登录提示
    }
  }
  ```

### 4.4 更新项目

- **接口**: `/api/projects/<project_id>`
- **方法**: `PUT`
- **描述**: 更新项目信息
- **权限**: 需要管理员权限
- **说明**: 如果项目正在被设备使用，则无法禁用项目
- **请求体**:
  ```json
  {
    "name": "string",        // 可选
    "description": "string", // 可选
    "service_compose": {     // 可选，项目自定义服务配置
      "services": {
        "app": {
          "image": "custom-image:latest",
          "environment": {
            "API_KEY": "{{ api_key }}"
          }
        }
      }
    },
    "status": 0,            // 可选，0: disabled, 1: enabled
    "form_schema": {        // 可选，表单配置
      "mqtt": {
        "type": "object",
        "title": "MQTT配置",
        "description": "MQTT服务连接配置",
        "required": ["host", "port", "client_id"],
        "properties": {
          "host": {
            "type": "string",
            "title": "服务器地址",
            "default": "mqtt.example.com"
          },
          "port": {
            "type": "integer",
            "title": "端口",
            "default": 1883,
            "minimum": 1,
            "maximum": 65535
          },
          "client_id": {
            "type": "string",
            "title": "客户端ID",
            "description": "唯一的客户端标识符"
          },
          "username": {
            "type": "string",
            "title": "用户名",
            "description": "认证用户名（可选）"
          },
          "password": {
            "type": "string",
            "title": "密码",
            "description": "认证密码（可选）"
          }
        }
      },
      "log_level": {
        "type": "string",
        "title": "日志级别",
        "enum": ["debug", "info", "warn", "error"],
        "default": "info"
      }
    },
    "special_login_hint": "",   // （可选）特殊登录提示60个字符上限
    "show_new_label": 1,        // （必选）是否显示新增标签 0否 1是
    "show_day": 1               // （显示新增标签后必选默认7天）展示时长 1天，3天，7天，14天，30天
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "项目更新成功",
    "data": {
      "id": 1,
      "name": "string",
      "description": "string",
      "service_config_id": 1,
      "service_compose": {
        "services": {},
        "volumes": {},
        "networks": {}
      },
      "status": 0,
      "form_schema": {
        "mqtt": {
          "host": "mqtt.example.com",
          "port": 1883,
          "client_id": "device-001",
          "username": "user1",
          "password": "pass123"
        },
        "log_level": "info"
      },
      "files": [
        {
          "id": 1,
          "name": "mqtt.conf",
          "content": "host={{mqtt.host}}\nport={{mqtt.port}}\nclient_id={{mqtt.client_id}}\n{% if mqtt.username %}username={{mqtt.username}}{% endif %}\n{% if mqtt.password %}password={{mqtt.password}}{% endif %}\nlog_level={{log_level}}"
        }
      ],
      "created_at": "string",
      "updated_at": "string",
      "special_login_hint": "",
      "show_new_label": 1,
      "show_day": 1,
      "b_new_label": true,            // 展示项目新增标签
      "special_login_hint": "string"  // 特殊登录提示
    }
  }
  ```

### 4.5 删除项目

- **接口**: `/api/projects/<project_id>`
- **方法**: `DELETE`
- **描述**: 删除项目
- **权限**: 需要管理员权限
- **说明**: 如果项目正在被设备使用，则无法删除
- **响应**:
  ```json
  {
    "code": 200,
    "message": "项目删除成功",
    "data": null
  }
  ```

### 4.6 获取设备的项目列表

- **接口**: `/api/projects/devices/<device_id>`
- **方法**: `GET`
- **描述**: 获取设备关联的所有项目配置
- **权限**: 需要设备的读取权限
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "project": {
          "id": 1,
          "name": "string",
          "description": "string",
          "form_schema": {
            "mqtt": {
              "host": "mqtt.example.com",
              "port": 1883,
              "client_id": "device-001",
              "username": "user1",
              "password": "pass123"
            },
            "log_level": "info"
          },
          "files": [
            {
              "id": 1,
              "name": "mqtt.conf",
              "content": "host={{mqtt.host}}\nport={{mqtt.port}}\nclient_id={{mqtt.client_id}}\n{% if mqtt.username %}username={{mqtt.username}}{% endif %}\n{% if mqtt.password %}password={{mqtt.password}}{% endif %}\nlog_level={{log_level}}"
            }
          ],
          "b_new_label": true,            // 展示项目新增标签
          "special_login_hint": "string"  // 特殊登录提示
        },
        "state": "created",  // created: 新创建, updated: 已更新
        "metrics": {
          "running_time": 3600,  // 设备所有服务的运行时间总和，单位为秒
          "total_points": 1000   // 设备所有服务的积分总和
        },
        "data": {
          "mqtt": {
            "host": "mqtt.example.com",
            "port": 1883,
            "client_id": "device-001",
            "username": "user1",
            "password": "pass123"
          },
          "log_level": "info"
        },
        "created_at": "string",
        "updated_at": "string"
      }
    ]
  }
  ```

### 4.7 创建设备项目配置

- **接口**: `/api/projects/devices/<device_id>/<project_id>`
- **方法**: `POST`
- **描述**: 为设备创建项目配置
- **权限**: 需要设备的写入权限
- **说明**: 
  - 请求体中的字段必须完全匹配项目的 form_schema
  - 创建后会自动生成设备的服务配置，包括：
    - 基础服务配置（来自 service_config）
    - 项目自定义配置（来自 service_compose）
    - 配置文件渲染（使用 form_schema 中定义的变量）
- **请求体**:
  ```json
  {
    "mqtt": {
      "host": "mqtt.example.com",
      "port": 1883,
      "client_id": "device-001",
      "username": "user1",
      "password": "pass123"
    },
    "log_level": "info"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "device_id": 1,
      "project_id": 1,
      "state": "created",
      "data": {
        "mqtt": {
          "host": "mqtt.example.com",
          "port": 1883,
          "client_id": "device-001",
          "username": "user1",
          "password": "pass123"
        },
        "log_level": "info"
      },
      "created_at": "string",
      "updated_at": "string"
    }
  }
  ```

### 4.8 更新设备项目配置

- **接口**: `/api/projects/devices/<device_id>/<project_id>`
- **方法**: `PUT`
- **描述**: 更新设备的项目配置
- **权限**: 需要设备的写入权限
- **说明**: 
  - 更新后会自动重新生成设备的服务配置
  - 配置生成规则同创建时相同
- **请求体**:
  ```json
  {
    "mqtt": {
      "host": "new-mqtt.example.com",
      "port": 8883,
      "client_id": "device-001-new",
      "username": "newuser",
      "password": "newpass"
    },
    "log_level": "debug"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "device_id": 1,
      "project_id": 1,
      "state": "updated",
      "data": {
        "mqtt": {
          "host": "new-mqtt.example.com",
          "port": 8883,
          "client_id": "device-001-new",
          "username": "newuser",
          "password": "newpass"
        },
        "log_level": "debug"
      },
      "created_at": "string",
      "updated_at": "string"
    }
  }
  ```

### 4.9 删除设备项目配置

- **接口**: `/api/projects/devices/<device_id>/<project_id>`
- **方法**: `DELETE`
- **描述**: 删除设备的项目配置
- **权限**: 需要设备的写入权限
- **响应**:
  ```json
  {
    "code": 200,
    "message": "项目配置删除成功",
    "data": null
  }
  ```

### 4.10 检查设备项目记录健康状态

- **接口**: `/api/projects/health`
- **方法**: `GET`
- **描述**: 检查当前用户的所有设备是否都有对应的设备项目记录
- **权限**: 需要登录
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "healthy": true,
      "message": "All device project records exist",
      "missing_records": []
    }
  }
  ```

  或者当存在缺失记录时:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "healthy": false,
      "message": "Missing 2 device project records",
      "missing_records": [
        {
          "device_id": 1,
          "device_name": "Device 1",
          "project_id": 2,
          "project_name": "Project 2"
        },
        {
          "device_id": 3,
          "device_name": "Device 3",
          "project_id": 1,
          "project_name": "Project 1"
        }
      ]
    }
  }
  ```

### 4.11 创建缺失的设备项目记录

- **接口**: `/api/projects/missing`
- **方法**: `POST`
- **描述**: 为当前用户的设备创建所有缺失的设备项目记录
- **权限**: 需要登录
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "success": true,
      "message": "Created 2 device project records"
    }
  }
  ```

  或者当没有需要创建的记录时:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "success": true,
      "message": "All device project records already exist"
    }
  }
  ```

  或者当发生错误时:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "success": false,
      "message": "Error validating device projects: [错误详情]"
    }
  }
  ```

## 5. 公共服务配置 API

### 5.1 获取公共服务配置列表

- **接口**: `/api/service-configs`
- **方法**: `GET`
- **描述**: 获取所有公共服务配置模板列表
- **权限**: 需要管理员权限
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "name": "MQTT服务",
        "description": "标准MQTT服务配置",
        "docker_compose": "version: '3'\nservices:\n  mqtt:\n    image: eclipse-mosquitto:latest\n    ports:\n      - '1883:1883'",
        "env": {
          "MQTT_USER": "default",
          "MQTT_PASSWORD": "default"
        }
      }
    ]
  }
  ```

### 5.2 创建公共服务配置

- **接口**: `/api/service-configs`
- **方法**: `POST`
- **描述**: 创建新的公共服务配置模板
- **权限**: 需要管理员权限
- **请求体**:
  ```json
  {
    "name": "string",                 // 服务配置名称（必填）
    "description": "string",          // 服务配置描述（可选）
    "docker_compose": "string",       // docker-compose 配置内容（必填）
    "env": {                         // 默认环境变量（可选）
      "key": "value"
    }
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "服务配置创建成功",
    "data": {
      "name": "string",
      "description": "string",
      "docker_compose": "string",
      "env": {
        "key": "value"
      }
    }
  }
  ```

### 5.3 获取公共服务配置详情

- **接口**: `/api/service-configs/<config_id>`
- **方法**: `GET`
- **描述**: 获取单个公共服务配置的详细信息
- **权限**: 需要管理员权限
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "name": "string",
      "description": "string",
      "docker_compose": "string",
      "env": {
        "key": "value"
      }
    }
  }
  ```

### 5.4 更新公共服务配置

- **接口**: `/api/service-configs/<config_id>`
- **方法**: `PUT`
- **描述**: 更新公共服务配置信息
- **权限**: 需要管理员权限
- **说明**: 更新服务配置时需要注意，如果该配置已被项目使用，更新可能会影响到相关项目
- **请求体**:
  ```json
  {
    "name": "string",           // 可选
    "description": "string",    // 可选
    "docker_compose": "string", // 可选
    "env": {                   // 可选
      "key": "value"
    }
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "服务配置更新成功",
    "data": {
      "name": "string",
      "description": "string",
      "docker_compose": "string",
      "env": {
        "key": "value"
      }
    }
  }
  ```

### 5.5 删除公共服务配置

- **接口**: `/api/service-configs/<config_id>`
- **方法**: `DELETE`
- **描述**: 删除公共服务配置
- **权限**: 需要管理员权限
- **说明**: 如果该配置正在被项目使用，则无法删除
- **响应**:
  ```json
  {
    "code": 200,
    "message": "服务配置删除成功",
    "data": null
  }
  ```

## 6. 仪表盘 API

### 6.1 获取统计数据

- **接口**: `/dashboard/statistics`
- **方法**: `GET`
- **描述**: 获取仪表盘统计数据
- **权限**: 需要登录
- **说明**: 管理员可以看到所有设备的统计，普通用户只能看到被授权设备的统计
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "deviceCount": 100,
      "statusStats": {
        "online": 80,
        "offline": 20
      }
    }
  }
  ```

## 7. 指标数据 API

#### 4. 服务指标上报接口

上报服务和设备的运行指标数据。

- 路径: /api/remote/metrics
- 方法: POST
- 请求头:
Authorization: <API_TOKEN>
Content-Type: application/json
- 请求体:
{
  "encrypted": "加密后的指标数据",
  "nonce": "加密用的nonce"
}
- 响应体:
{
  "status": "ok"
}
加密前的指标数据格式：
{
  "services": [
    {
      "service_name": "服务名称",
      "started_at": "2024-03-20T08:00:00Z",    // 服务启动时间
      "running_time": 3600,                     // 自启动以来的总运行时长（秒）
      "total_points": 100,                      // 总积分
      "daily_points": 10,                       // 今日积分（可选）
      "updated_at": "2024-03-20T10:00:00Z"
    }
  ],
  "device": {
    "started_at": "2024-03-20T08:00:00Z",      // 设备启动时间
    "running_time": 3600,                       // 自启动以来的总运行时长（秒）
    "lan_ip": "*************",                 // 局域网 IP
    "public_ip": "***********",                // 公网 IP
    "network": {
      "download_speed": 100000000,             // 下载带宽（bps）
      "upload_speed": 50000000                 // 上传带宽（bps）
    },
    "disk": {
      "total": 1000000000000,                 // 总空间（字节）
      "used": 500000000000,                   // 已用空间（字节）
      "free": 500000000000                    // 可用空间（字节）
    },
    "cpu": {
      "usage": 35.5,                          // CPU 使用率（百分比）
      "cores": 8                              // CPU 核心数
    },
    "memory": {
      "total": 16000000000,                   // 总内存（字节）
      "used": 8000000000,                     // 已用内存（字节）
      "free": 8000000000                      // 可用内存（字节）
    },
    "updated_at": "2024-03-20T10:00:00Z"
  }
}
说明：

- agent 每小时自动上报一次所有服务和设备的指标数据
- 服务指标包含：
  - 服务启动时间
  - 运行时长
  - 总积分
  - 每日积分（可选字段）
- 设备指标包含：
  - 设备启动时间
  - 运行时长
  - 网络信息（局域网IP、公网IP、带宽）
  - 硬盘使用情况
  - CPU 使用情况
  - 内存使用情况
- 服务器端应该根据 updated_at 时间戳来判断数据的时效性
- 指标数据使用与其他接口相同的加密方式（AES-GCM）进行加密
- 每次上报使用新的 nonce 确保安全性

## 8 通知服务
 
 ### 8.1 获取当前用户消息列表
 
 - **接口**: `/api/user-notify`
 - **方法**: `GET`
 - **描述**: 获取当前用户的通知分页列表，可通过通知类型和优先级过滤。
 - **权限**: 需要 JWT Token 认证
 - **查询参数**:
   - `page` (int): 页码，默认值为 1
   - `per_page` (int): 每页显示通知数量，默认 10，最大限制为 100
   - `b_popup` (int): 默认b_popup=，有值只出现弹窗消息b_popup=1
   - `unread_only` (int): 默认unread_only=，获取未读unread_only=1
   - `notify_type[]` (string, 可选): 根据通知类型进行过滤     
   """通知类型枚举"""
    ANNOUNCEMENT = "announcement"  # 即时公告
    SYSTEM = 'system'
    ACTIVITY = "activity"  # 活动公告
    UPDATE = "update"  # 版本公告
    AIRDROP = "airdrop"  # 空投
    TRADE = "trade"  # 交易
    SECURITY = "security"  # 安全
   - `notify_priority[]` (int, 可选): 根据通知优先级进行过滤（0 - 低优先级，1 - 普通优先级，2 - 高优先级）
   - 
- **响应**:
```json
  {
   "code": 200,
   "data": {
       "items": [
           {
               "id": 1,
               "content": "测试通知内容",
               "is_read": true,
               "notify_priority": 1,
               "notify_type": "system",
               "read_timestamp": "Thu, 17 Apr 2025 03:55:13 GMT",
               "title": "测试通知",
               "popup_show": []   //数组长度大于0需要弹窗 1即时弹窗 2打开APP 4刷新页面[已被废弃]
           }
       ],
       "page": 1,
       "per_page": 20,
       "total": 1,
       "unread_count": 0
   },
   "message": "操作成功"
}
```

  
### 8.2 单条通知标记为已读
 
 - **接口**: `/api/user-notify/<id>/read`
 - **方法**: `PUT`
 - **描述**: 将指定的单条通知标记为已读。
 - **权限**: 需要 JWT Token 认证
 - **URL 参数**:
   - `id` (int): 通知的唯一标识 ID
 - **响应**:
```json
{
    "code": 200,
    "data": {
        "content": "测试通知内容",
        "id": 1,
        "is_read": true,
        "notify_priority": 1,
        "notify_type": "system",
        "read_timestamp": "Thu, 17 Apr 2025 03:55:13 GMT",
        "title": "测试通知"
    },
    "message": "操作成功"
}
```

 
 ### 8.3 通知删除
 
 - **接口**: `/api/user-notify/<id>`
 - **方法**: `DELETE`
 - **描述**: 删除指定通知
 - **权限**: 需要 JWT Token 认证
 - **URL 参数**:
   - `id` (int): 通知的唯一标识 ID
 - **响应**:
   ```json
   {
     "code": 200,
     "message": "操作成功",
     "data": null
   }
   ```
   
 ### 8.4 批量标记已读
 
 - **接口**: `/api/user-notify/read`
 - **方法**: `PUT`
 - **描述**: 批量标记已读
 - **权限**: 需要 JWT Token 认证
 - **URL 参数**:
   - `notify_type[]` (string, 可选): 根据通知类型进行过滤     
   - `notify_priority[]` (int, 可选): 根据通知优先级进行过滤（0 - 低优先级，1 - 普通优先级，2 - 高优先级）
   
 - **响应**:
   ```json
   {
     "code": 200,
     "message": "操作成功",
     "data": null
   }
   ```

## 邮件验证码服务

### 发送验证码

**Endpoint**  
`POST /api/auth/send-code`

**请求参数**  
```json
{
  "email": "<EMAIL>"
}
```

**响应示例**  
成功：
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

错误：
```json
{
  "code": 400,
  "message": "邮箱格式错误 | 缺少必要参数",
  "data": null
}
```

### 验证验证码

**Endpoint**  
`POST /api/auth/verify-code`

**请求参数**  
```json
{
  "code": "123456"
}
```

**响应示例**  
成功：
```json
{
  "code": 200,
  "message": "验证成功",
  "data": null
}
```

错误：
```json
{
  "code": 400,
  "message": "验证码错误或已过期 | 缺少必要参数",
  "data": null
}
```

**注意事项**
1. 验证码有效期为10分钟
2. 验证码使用后立即失效
3. 需要配合邮箱参数使用（通过JWT识别用户）

**技术细节**

1. 速率限制：
   - 每个邮箱每分钟最多发送3次验证码
   - 每个IP地址每小时最多发送20次验证码

2. 邮件模板示例：
```html
<!DOCTYPE html>
<html>
<body>
  <h2>您的验证码是：{{ code }}</h2>
  <p>有效期10分钟，请勿泄露给他人</p>
</body>
</html>
```


3. 错误代码说明：
| 错误码 | 说明                  |
|--------|-----------------------|
| 400    | 参数验证失败          |
| 429    | 请求过于频繁          |
| 500    | 邮件服务暂时不可用     |

## 9. 钱包服务

### 9.1 网络列表
- **接口**: `/api/wallets/chains`
 - **方法**: `GET`
 - **描述**: 获取支持的网络列表
 - **权限**: 需要 JWT Token 认证
 - **响应**
```json 
{
    "code": 200,
    "data": [
        {
            "name": "Ethereum",
            "code": "ETH"
        },
        {
            "name": "Solana",
            "code": "SOL"
        }
    ]
}
```
 
### 9.2 钱包生成
- **接口**: `/api/wallets`
 - **方法**: `POST`
 - **描述**: 为当前用户批量生成钱包
 - **权限**: 需要 JWT Token 认证
 - **请求** 
 - **响应**
```json
{
    "code": 200,
    "data": {
        "device_id": 0,
        "group_id": 1,
        "group_name": "wallet1",
        "wallets": [
            {
                "chain_code": "ETH",
                "id": 1, 
                "wallet_address": "******************************************"
            },
            {
                "chain_code": "BSC",
                "id": 2,
                "wallet_address": "******************************************"
            },
            {
                "chain_code": "BASE",
                "id": 3,
                "wallet_address": "******************************************"
            },
            {
                "chain_code": "SOL",
                "id": 4,
                "wallet_address": "DgFLYH29J6HcrG3VxaH4GyLCteyvPdffqmRZWLpiJs2p"
            }
        ]
    },
    "message": "success"
}
```

### 9.3 钱包列表
- **接口**: `/api/wallets`
 - **方法**: `GET`
 - **描述**: 获取当前用户钱包列表
 - **权限**: 需要 JWT Token 认证
 - **请求** 
   - page 页码
   - per_page 分页大小
   - chain_code - 要查询的钱包网络编码
   - token_code - 要查询的币种类型
   - exist_balance - 是否只查询有余额的钱包列表
   
 - **响应**
 ```json
{
    "code": 200,
    "data": {
        "total_balance_usd":'1.0',
        "current_page": 2,
        "items": [
            {
                "group_id": 6,
                // FE need `wallet_name` field
                "wallets": [
                    {
                        "chain_code": "ETH",
                        "chain_name": "Ethereum",
                        "wallet_address": "0xaddress5",
                        "balance": "0.01",
                        "token_price":"1",
                        "balance_usd": "12",
                        "token_code":"ETH",
                        "token_name":"ETH"
                        "is_native": true,
                        "wallet_id": 1
                    },
                    {
                        "chain_code": "SOL",
                        "wallet_address": "0xaddress6",
                        "balance": "0",
                        "balance_usd": "0"
                    },
                ]
            },
            {
                "group_id": 7,
                "wallets": [
                    {
                        "chain_code": "BNB",
                        "wallet_address": "0xaddress6"
                    }
                ]
            },
            {
                "group_id": 8,
                "wallets": [
                    {
                        "chain_code": "BASE",
                        "wallet_address": "0xaddress7"
                    }
                ]
            },
            {
                "group_id": 9,
                "wallets": [
                    {
                        "chain_code": "SOL",
                        "wallet_address": "0xaddress8"
                    }
                ]
            },
            {
                "group_id": 10,
                "wallets": [
                    {
                        "chain_code": "SOL",
                        "wallet_address": "0xaddress9"
                    }
                ]
            }
        ],
        "pages": 3,
        "total": 15
    },
    "message": "操作成功"
}
```

### 9.4 钱包地址合法性校验
- **接口**: `/api/wallets/{address}/valid`
 - **方法**: `GET`
 - **描述**: 校验钱包地址是否合法
 - **权限**: 需要 JWT Token 认证
 - **请求** 
   - chain_code - 网络编码，例：ETH
 - **响应**
 ```json
{
    "code": 200,
    "data": true
}
```

### 9.5 交易费用和时间预估
- **接口**: `/api/wallets/transfer/estimate`
 - **方法**: `POST`
 - **描述**: 预估交易花费时间和费用
 - **权限**: 需要 JWT Token 认证
 - **请求** 
```json
{
  "chain_code": "ETH",
  "token_code": "ETH",
  "wallet_id": 123,
  "sender_address": "0x...",
  "receiver_address": "0x....",
  "value": "0.2"
}
```
 - **响应**
```json
{
    "code":200,
    "data":{
        "gas_price": "5000",
        "gas_limit": "1",
        "total_gas": "0.0005",
        "total_gas_wei": "5000",
        "total_gas_usd": "12.0",
        "estimate_transfer_seconds": "60"
    }
}
```
### 9.6 确认交易
- **接口**: `/api/wallets/transfer`
 - **方法**: `POST`
 - **描述**: 确认交易
 - **权限**: 需要 JWT Token 认证
 - **请求** 
```json
{
  "chain_code": "ETH",
  "token_code": "ETH",
  "wallet_id": 123,
  "sender_address": "0x...",
  "receiver_address": "0x....",
  "value": "0.2"
}
```
 - **响应**
 将返回交易的 tx_hash
```json
{
    "code":200,
    "data":{
      "tx_hash": "0x..."
    }
}
```
 
### 9.7 查询交易
- **接口**: `/wallets/transfer`
 - **方法**: `GET`
 - **描述**: 查询交易的状态、gas费用、确认时间等详情
 - **权限**: 需要 JWT Token 认证
 - **请求**
   - tx_hash - 交易的 tx_hash
 - **响应**
```json
{
    "code":200,
    "data":{
        "tx_hash": "0x123",
        "status":"success",
        "from_address":"0x1",
        "to_address":"0x2",
        "gas_price": "5000",
        "gas_limit": "1",
        "total_gas": "0.0005",
        "total_gas_wei": "5000",
        "total_gas_usd": "12.0",
        "timestamp":"1742560442",
        "chain_code": "ETH",
        "token_code": "ETH",
        "is_native":true
    }
}
```

### 9.8 签名服务（设备用）
- **接口**: `/remote/jsonrpc`
 - **方法**: `POST`
 - **描述**: 使用指定的钱包私钥对 message 进行签名
- **请求**
  - 请求头:
    - Authorization: <API_TOKEN>
    - Content-Type: application/json
  - 请求体
```json
{
  "encrypted": "加密后的签名请求",
  "nonce": "加密用的nonce"
}
```
加密前的请求体
```json
{
    "jsonrpc": "2.0",
    "method": "sign_message",
    "params": ["SOL", "HMKZJDVAsLMWXxLSxg27h7ceHCe22yYMrk7NWjhYJPu7", "test message"],
    "id": 123
}
```
 - **响应**
```json
{
  "encrypted": "加密后的签名响应",
  "nonce": "加密用的nonce"
}
```
解密后的响应内容：
```json
{
      "jsonrpc": "2.0",
      "method": "sign_message",
      "result": {
        "signed_message": "signed_message"
      },
      "id": 123
}

```