from datetime import datetime, timezone, timedelta
import pytest
from unittest.mock import patch, MagicMock

from app.enums.biz_enums import DeviceStatus, FixtureProjectName
from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsDetail
from app.models.project import Project
from app.tasks.monitoring import check_device_status
from app.models.service_config import ServiceConfig
from app.models.user import User
from app.services.points_service import PointsService
from app.services.lighter_points_integration_service import LighterPointsIntegrationService
from app.utils.redis_client import RedisClient


@pytest.fixture
def setup_test_data(app):
    """Setup test devices and metrics data"""
    with app.app_context():
        # Create test service config first
        service_config = ServiceConfig(
            name="test-service-config",
            description="Test Service Config",
            docker_compose={
                "version": "3",
                "services": {
                    "test": {
                        "image": "test:latest"
                    }
                }
            }
        )
        db.session.add(service_config)
        db.session.commit()

        # Create test devices with new status values
        device1 = Device(
            name="test-device-1",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            status=2  # running (previously 1 for online)
        )
        device2 = Device(
            name="test-device-2", 
            ip_address="***********",
            mac_address="00:11:22:33:44:66",
            status=1  # wait to configure (previously 1 for online)
        )
        device3 = Device(
            name="test-device-3", 
            ip_address="***********",
            mac_address="00:11:22:33:44:77",
            status=0  # initializing
        )
        db.session.add_all([device1, device2, device3])
        db.session.commit()

        # Create test project with service_config_id
        project = Project(
            name="test-project",
            description="Test Project",
            status="enabled",
            service_config_id=service_config.id
        )
        db.session.add(project)
        db.session.commit()

        # Create device project associations with different states
        device_project1 = DeviceProject(
            device_id=device1.id,
            project_id=project.id,
            state="running"
        )
        device_project2 = DeviceProject(
            device_id=device2.id,
            project_id=project.id,
            state="created"  # Initial state
        )
        device_project3 = DeviceProject(
            device_id=device3.id,
            project_id=project.id,
            state="updated"  # Configured but not running
        )
        db.session.add_all([device_project1, device_project2, device_project3])
        db.session.commit()

        # Refresh objects
        db.session.refresh(device_project1)
        db.session.refresh(device_project2)
        db.session.refresh(device_project3)
        db.session.refresh(project)

        yield {
            "device1": device1,
            "device2": device2,
            "device3": device3,
            "project1": device_project1,
            "project2": device_project2,
            "project3": device_project3,
            "project_obj": project,
            "service_config": service_config
        }

        # Cleanup in reverse order of creation
        db.session.query(ServiceMetricsDetail).delete()
        db.session.query(DeviceProject).delete()
        db.session.query(Project).delete()
        db.session.query(Device).delete()
        db.session.query(ServiceConfig).delete()
        db.session.commit()



@pytest.fixture
def setup_points_test_data(app):
    """Set up test data for continuous running points tests."""
    with app.app_context():
        # Create a test user
        user = User(
            username="testuser",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()
        
        # Create a service config first
        service_config = ServiceConfig(
            name="test-service-points",
            description="Test Service Config for Points",
            docker_compose="version: '3'\nservices:\n  test:\n    image: test:latest",
            default_env={"TEST_ENV": "test"}
        )
        db.session.add(service_config)
        db.session.commit()  # Commit to get the service_config.id
        
        # Create a test device
        device = Device(
            name="Test Device Points",
            status=1,
            owner=user,
            ip_address="***********00",
            mac_address="00:11:22:33:44:56"
        )
        db.session.add(device)
        db.session.commit()  # Commit to get the device.id
        
        # Create a test project
        project = Project(
            name="test-project-points",
            description="Test Project for Points",
            service_config_id=service_config.id
        )
        db.session.add(project)
        db.session.commit()  # Commit to get the project.id
        
        # Create a device project association
        device_project = DeviceProject(
            device=device,
            project=project,
            state="running",
            continuous_running_checks=0
        )
        db.session.add(device_project)
        db.session.commit()
        
        # Store IDs for later use
        test_data = {
            "user_id": user.id,
            "device_id": device.id,
            "project_id": project.id,
            "device_project_id": device_project.id,
            "service_config_id": service_config.id
        }
        
        yield test_data
        
        # Clean up after the test
        db.session.query(ServiceMetricsDetail).delete()
        db.session.query(DeviceProject).delete()
        db.session.query(Project).delete()
        db.session.query(Device).delete()
        db.session.query(User).delete()
        db.session.query(ServiceConfig).delete()
        db.session.commit()


@pytest.fixture(autouse=True)
def mock_celery_task():
    """Mock Celery task"""
    with patch('celery.shared_task', lambda *args, **kwargs: lambda f: f):
        yield

def test_check_device_status_failure(app, setup_test_data):
    """Test devices going to failure state when metrics are outdated"""
    with app.app_context():
        device1 = setup_test_data["device1"]
        
        # Get device from current session
        device1 = db.session.merge(device1)  # Merge instead of adding directly
        device1.status = 2  # Set to running
        db.session.commit()
        
        # Create outdated metrics (10 minutes old)
        old_time = datetime.now(timezone.utc) - timedelta(minutes=10)
        metrics = ServiceMetricsDetail(
            device_id=device1.id,
            service_name="test_service",
            started_at=old_time,
            updated_at=old_time,
            points=100,
            running_time=3600
        )
        db.session.add(metrics)
        db.session.commit()

        # Remove any device projects to ensure status is determined by metrics
        DeviceProject.query.filter_by(device_id=device1.id).delete()
        db.session.commit()

        # Run the check
        check_device_status()

        # Get fresh instance from DB
        device1 = db.session.get(Device, device1.id)
        assert device1.status == DeviceStatus.WAIT_TO_CONFIGURE.code, "Device should be marked as wait to configure (1) due to no device_project related"

def test_check_device_status_running(app, setup_test_data):
    """Test devices staying in running state with recent metrics"""
    with app.app_context():
        device1 = db.session.merge(setup_test_data["device1"])
        current_time = datetime.now(timezone.utc)

        # Create recent metrics (within threshold)
        metrics = ServiceMetricsDetail(
            device_id=device1.id,
            service_name="test_service",
            started_at=current_time,
            updated_at=current_time,
            points=100,
            running_time=3600,
            status_code=200  # Valid status code
        )
        db.session.add(metrics)
        db.session.commit()

        RedisClient.get_instance().setex(f'device_{device1.id}_tag', 1800, 1)

        # Run the check
        check_device_status()

        # Get fresh instance
        device1 = db.session.get(Device, device1.id)
        assert device1.status == DeviceStatus.RUNNING.code, "Device should stay in running state (2) with recent metrics"

def test_check_project_status_running_to_stopped(app, setup_test_data):
    """Test project status updates from running to stopped based on metrics"""
    with app.app_context():
        device1 = db.session.merge(setup_test_data["device1"])
        project = db.session.merge(setup_test_data["project1"])
        old_time = datetime.now(timezone.utc) - timedelta(minutes=36)  # Just over 35-minute threshold

        # Create outdated metrics
        metrics = ServiceMetricsDetail(
            device_id=device1.id,
            service_name=f"{project.project.name}_service",
            started_at=old_time,
            updated_at=old_time,
            points=100,
            running_time=3600,
            status_code=200  # Valid status code but outdated
        )
        db.session.add(metrics)
        db.session.commit()

        # Run the check
        check_device_status()

        # Get fresh instance
        project = db.session.get(DeviceProject, project.id)
        assert project.state == "stopped", "Project should be marked as stopped due to outdated metrics"

def test_check_project_status_updated_to_stopped(app, setup_test_data):
    """Test project status updates from updated to stopped when metrics are missing or invalid"""
    with app.app_context():
        device3 = db.session.merge(setup_test_data["device3"])
        project = db.session.merge(setup_test_data["project3"])
        current_time = datetime.now(timezone.utc)
        
        # Create metrics with invalid status code
        metrics = ServiceMetricsDetail(
            device_id=device3.id,
            service_name=f"{project.project.name}_service",  # Use project.project.name to match the pattern
            started_at=current_time,
            updated_at=current_time,
            points=100,
            running_time=3600,
            status_code=500  # Invalid status code
        )
        db.session.add(metrics)
        db.session.commit()

        # Check - should change state to stopped immediately
        check_device_status()

        # Get fresh instance
        project = db.session.get(DeviceProject, project.id)
        assert project.state == "stopped", "Project should be in stopped state when metrics have invalid status code"

        # Update metrics to have valid status code but outdated timestamp
        metrics.status_code = 200
        metrics.updated_at = current_time - timedelta(minutes=36)  # Just over the 35-minute threshold
        db.session.add(metrics)
        db.session.commit()

        # Check again - should stay stopped
        check_device_status()

        # Get fresh instance
        project = db.session.get(DeviceProject, project.id)
        assert project.state == "stopped", "Project should stay in stopped state when metrics are outdated"

        # Update metrics to be recent and valid
        metrics.updated_at = current_time
        db.session.add(metrics)
        db.session.commit()

        # Check again - should change to running
        check_device_status()

        # Get fresh instance
        project = db.session.get(DeviceProject, project.id)
        assert project.state == "running", "Project should change to running state with recent valid metrics"

def test_no_metrics_device_failure(app, setup_test_data):
    """Test devices with no metrics are marked as failure"""
    with app.app_context():
        device2 = db.session.merge(setup_test_data["device2"])
        
        # Ensure device starts as running
        device2.status = 2
        db.session.commit()
        
        # Remove any device projects to ensure status is determined by metrics
        #DeviceProject.query.filter_by(device_id=device2.id).all()
        #db.session.commit()
        
        # Run the check
        check_device_status()

        # Get fresh instance and verify it's in failure state
        device2 = db.session.get(Device, device2.id)
        assert device2.status == 1, "Device should be marked as failure (3) when no metrics exist"

def test_failure_device_becomes_running(app, setup_test_data):
    """Test failure device becomes running when recent metrics appear"""
    with app.app_context():
        device1 = db.session.merge(setup_test_data["device1"])

        device1.status = 2
        db.session.commit()
        
        # Create recent metrics (within threshold)
        current_time = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device1.id,
            service_name="test_service",
            started_at=current_time,
            updated_at=current_time,
            points=100,
            running_time=3600
        )
        db.session.add(metrics)
        db.session.commit()
        RedisClient.get_instance().setex(f'device_{device1.id}_tag', 1800, 1)
        # Run the check
        check_device_status()

        # Get fresh instance
        device1 = db.session.get(Device, device1.id)
        assert device1.status == 2, "Device should become failure (3) due to recent metrics"

def test_stopped_project_becomes_running(app, setup_test_data):
    """Test stopped project becomes running when recent metrics appear"""
    with app.app_context():
        device1 = db.session.merge(setup_test_data["device1"])
        project = db.session.merge(setup_test_data["project1"])
        project_obj = db.session.merge(setup_test_data["project_obj"])
        
        # Set project as stopped initially
        project.state = "stopped"
        db.session.commit()
        
        # Create recent metrics
        current_time = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device1.id,
            service_name=f"{project_obj.name}_service",
            started_at=current_time,
            updated_at=current_time,
            points=100,
            running_time=3600,
            status_code=200  # Valid status code
        )
        db.session.add(metrics)
        db.session.commit()

        # Run the check
        check_device_status()

        # Get fresh instance
        project = db.session.get(DeviceProject, project.id)
        assert project.state == "running", "Project should become running due to recent metrics"

def test_device_offline_after_long_period(app, setup_test_data):
    """Test device goes to offline state after 7 days without metrics"""
    with app.app_context():
        device1 = db.session.merge(setup_test_data["device1"])
        
        # Set device as failure initially
        device1.status = 3
        db.session.commit()
        
        # update any device projects to ensure status is determined by metrics
        DeviceProject.query.filter_by(device_id=device1.id).update({"state": 'stopped'})
        db.session.commit()
        
        # Create very old metrics (8 days old)
        old_time = datetime.now(timezone.utc) - timedelta(days=8)
        metrics = ServiceMetricsDetail(
            device_id=device1.id,
            service_name="test_service",
            started_at=old_time,
            updated_at=old_time,
            points=100,
            running_time=3600
        )
        db.session.add(metrics)
        db.session.commit()

        # Run the check
        check_device_status()

        # Get fresh instance
        device1 = db.session.get(Device, device1.id)
        assert device1.status == 3, "Device should be marked as offline (4) after 7+ days without metrics"

# Tests for continuous_running_checks functionality
def test_check_device_status_awards_points(app, setup_points_test_data):
    """Test that check_device_status awards points after 12 continuous checks."""
    with app.app_context():
        # Get test data by IDs
        test_data = setup_points_test_data
        device = db.session.get(Device, test_data["device_id"])
        project = db.session.get(Project, test_data["project_id"])
        device_project = db.session.get(DeviceProject, test_data["device_project_id"])

        # Create recent metrics for the project
        now = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device.id,
            service_name=f"{project.name}-service",
            started_at=now,
            updated_at=now,
            status_code=200  # Valid status code
        )
        db.session.add(metrics)
        db.session.commit()

        # Mock the PointsService.add_project_running_points method
        with patch.object(PointsService, 'add_project_running_points', return_value=(True, None)) as mock_add_points:
            # Run check_device_status 12 times (should award points)
            for _ in range(12):
                check_device_status()
                # Update metrics timestamp to simulate continuous running
                metrics.updated_at = datetime.now(timezone.utc)
                db.session.add(metrics)
                db.session.commit()

            # Verify points were awarded
            mock_add_points.assert_called_once_with(device.id, project.id)

def test_check_device_status_resets_counter_when_stopped(app, setup_points_test_data):
    """Test that check_device_status resets the counter when a project stops running."""
    with app.app_context():
        # Get test data by IDs
        test_data = setup_points_test_data
        device = db.session.get(Device, test_data["device_id"])
        project = db.session.get(Project, test_data["project_id"])
        device_project = db.session.get(DeviceProject, test_data["device_project_id"])

        # Set initial counter value
        device_project.continuous_running_checks = 5
        db.session.add(device_project)
        db.session.commit()

        # Create outdated metrics (older than threshold)
        now = datetime.now(timezone.utc)
        old_time = now - timedelta(minutes=8)  # Just over the 7-minute threshold
        metrics = ServiceMetricsDetail(
            device_id=device.id,
            service_name=f"{project.name}-service",
            started_at=old_time,
            updated_at=old_time,
            status_code=500  # Invalid status code
        )
        db.session.add(metrics)
        db.session.commit()

        # Run check_device_status
        check_device_status()

        # Refresh device_project from database
        db.session.refresh(device_project)

        # Verify state changed to stopped and counter reset
        assert device_project.state == "stopped"
        assert device_project.continuous_running_checks == 0

def test_check_device_status_awards_points_multiple_times(app, setup_points_test_data):
    """Test that check_device_status awards points multiple times after each hour of running."""
    with app.app_context():
        # Get test data by IDs
        test_data = setup_points_test_data
        device = db.session.get(Device, test_data["device_id"])
        project = db.session.get(Project, test_data["project_id"])
        device_project = db.session.get(DeviceProject, test_data["device_project_id"])

        # Create recent metrics for the project
        now = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device.id,
            service_name=f"{project.name}-service",
            started_at=now,
            updated_at=now,
            status_code=200  # Valid status code
        )
        db.session.add(metrics)
        db.session.commit()

        # Mock the PointsService.add_project_running_points method
        with patch.object(PointsService, 'add_project_running_points', return_value=(True, None)) as mock_add_points:
            # Run check_device_status 24 times (should award points twice)
            for i in range(24):
                check_device_status()

                # Update metrics timestamp to simulate continuous running
                metrics.updated_at = datetime.now(timezone.utc)
                db.session.add(metrics)
                db.session.commit()

                # Verify points are awarded at the right times
                if i == 11:  # After 12 checks
                    assert mock_add_points.call_count == 1
                elif i == 23:  # After 24 checks
                    assert mock_add_points.call_count == 2
            
            # Verify points were awarded twice
            assert mock_add_points.call_count == 2
            
            # Refresh device_project from database
            db.session.refresh(device_project)
            assert device_project.continuous_running_checks == 24


@pytest.fixture
def setup_lighter_test_data(app):
    """Set up test data for Lighter project tests."""
    with app.app_context():
        # Create a test user
        user = User(
            username="lighteruser",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        # Create a service config first
        service_config = ServiceConfig(
            name="lighter-service-config",
            description="Lighter Service Config",
            docker_compose="version: '3'\nservices:\n  lighter:\n    image: lighter:latest",
            default_env={"LIGHTER_ENV": "test"}
        )
        db.session.add(service_config)
        db.session.commit()

        # Create a test device
        device = Device(
            name="Lighter Test Device",
            status=2,  # running
            owner=user,
            ip_address="*************",
            mac_address="00:11:22:33:44:99"
        )
        db.session.add(device)
        db.session.commit()

        # Create Lighter project
        lighter_project = Project(
            name=FixtureProjectName.LIGHTER_PROJECT_NAME,
            description="Lighter Project for Testing",
            service_config_id=service_config.id
        )
        db.session.add(lighter_project)
        db.session.commit()

        # Create a device project association for Lighter
        device_project = DeviceProject(
            device=device,
            project=lighter_project,
            state="running",
            continuous_running_checks=0
        )
        db.session.add(device_project)
        db.session.commit()

        test_data = {
            "user_id": user.id,
            "device_id": device.id,
            "project_id": lighter_project.id,
            "device_project_id": device_project.id,
            "service_config_id": service_config.id
        }

        yield test_data

        # Clean up
        db.session.query(ServiceMetricsDetail).delete()
        db.session.query(DeviceProject).delete()
        db.session.query(Project).delete()
        db.session.query(Device).delete()
        db.session.query(User).delete()
        db.session.query(ServiceConfig).delete()
        db.session.commit()


def test_lighter_project_awards_points_after_24_hours(app, setup_lighter_test_data):
    """Test that Lighter project awards points after 24 hours (288 checks)."""
    with app.app_context():
        test_data = setup_lighter_test_data
        device = db.session.get(Device, test_data["device_id"])
        project = db.session.get(Project, test_data["project_id"])
        device_project = db.session.get(DeviceProject, test_data["device_project_id"])

        # Create recent metrics for the Lighter project
        now = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device.id,
            service_name=f"{project.name}-service",
            started_at=now,
            updated_at=now,
            status_code=200
        )
        db.session.add(metrics)
        db.session.commit()

        # Mock the LighterPointsIntegrationService.add_lighter_points method
        with patch.object(LighterPointsIntegrationService, 'add_lighter_points', return_value=(True, None)) as mock_add_points:
            # Run check_device_status 288 times (24 hours worth of 5-minute checks)
            for i in range(288):
                check_device_status()
                # Update metrics timestamp to simulate continuous running
                metrics.updated_at = datetime.now(timezone.utc)
                db.session.add(metrics)
                db.session.commit()

            # Verify Lighter points were awarded once after 288 checks
            mock_add_points.assert_called_once_with(
                user_id=device.owner_id,
                points=0.01,
                record_type='lighter_device',
                related_id=device.id
            )


def test_lighter_project_awards_points_multiple_times(app, setup_lighter_test_data):
    """Test that Lighter project awards points multiple times after each 24-hour period."""
    with app.app_context():
        test_data = setup_lighter_test_data
        device = db.session.get(Device, test_data["device_id"])
        project = db.session.get(Project, test_data["project_id"])
        device_project = db.session.get(DeviceProject, test_data["device_project_id"])

        # Create recent metrics for the Lighter project
        now = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device.id,
            service_name=f"{project.name}-service",
            started_at=now,
            updated_at=now,
            status_code=200
        )
        db.session.add(metrics)
        db.session.commit()

        # Mock the LighterPointsIntegrationService.add_lighter_points method
        with patch.object(LighterPointsIntegrationService, 'add_lighter_points', return_value=(True, None)) as mock_add_points:
            # Run check_device_status 576 times (48 hours worth of 5-minute checks)
            for i in range(576):
                check_device_status()
                # Update metrics timestamp to simulate continuous running
                metrics.updated_at = datetime.now(timezone.utc)
                db.session.add(metrics)
                db.session.commit()

            # Verify Lighter points were awarded twice (at 288 and 576 checks)
            assert mock_add_points.call_count == 2


def test_lighter_project_no_points_without_owner(app, setup_lighter_test_data):
    """Test that Lighter project doesn't award points if device has no owner."""
    with app.app_context():
        test_data = setup_lighter_test_data
        device = db.session.get(Device, test_data["device_id"])
        project = db.session.get(Project, test_data["project_id"])

        # Remove device owner
        device.owner_id = None
        db.session.add(device)
        db.session.commit()

        # Create recent metrics for the Lighter project
        now = datetime.now(timezone.utc)
        metrics = ServiceMetricsDetail(
            device_id=device.id,
            service_name=f"{project.name}-service",
            started_at=now,
            updated_at=now,
            status_code=200
        )
        db.session.add(metrics)
        db.session.commit()

        # Mock the LighterPointsIntegrationService.add_lighter_points method
        with patch.object(LighterPointsIntegrationService, 'add_lighter_points', return_value=(True, None)) as mock_add_points:
            # Run check_device_status 288 times
            for i in range(288):
                check_device_status()
                # Update metrics timestamp to simulate continuous running
                metrics.updated_at = datetime.now(timezone.utc)
                db.session.add(metrics)
                db.session.commit()

            # Verify no Lighter points were awarded
            mock_add_points.assert_not_called()