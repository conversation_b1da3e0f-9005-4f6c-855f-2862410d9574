import pytest

from app.models.points import TaskType, PointRecord


@pytest.fixture
def setup_task_type(db_session):
    task = TaskType(name='daily_login', points=10)
    db_session.add(task)
    db_session.commit()
    return task


def test_task_points(client, ubi_project, normal_user_headers, setup_task_type):

    # First completion
    response = client.post('/api/points/task', 
                         json={'task_type': 'daily_login'},
                         headers=normal_user_headers)
    assert response.status_code == 200
    assert response.json['data']['points'] == 10
    
    # Duplicate completion
    response = client.post('/api/points/task', 
                         json={'task_type': 'daily_login'},
                         headers=normal_user_headers)
    assert response.status_code == 400
    
    # Check record count
    records = PointRecord.query.all()
    assert len(records) == 1