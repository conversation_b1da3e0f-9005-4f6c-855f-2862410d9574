import pytest
from flask import json

from app.models import User, db
from app.models.wallet import WalletGroup, WalletGenerated
from app.models.blockchain import Blockchain, Token, TokenType, ChainProtocol


@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def test_blockchains(app):
    """创建测试区块链数据"""
    with app.app_context():
        # 清理现有数据
        Blockchain.query.delete()
        Token.query.delete()
        db.session.commit()

        # 创建测试区块链（只创建实际支持的链：ETH, BSC, ARB, SOL）
        blockchains = [
            Blockchain(
                chain_name="Ethereum",
                chain_code="ETH",
                coin_type=60,
                chain_protocol=ChainProtocol.EVM,
                extend_field={"rpc_url": "http://127.0.0.1:7545"},
            ),
            Blockchain(
                chain_name="Solana",
                chain_code="SOL",
                coin_type=501,
                chain_protocol=ChainProtocol.SOL,
                extend_field={"rpc_url": "http://127.0.0.1:7545"},
            ),
            Blockchain(
                chain_name="Binance Smart Chain",
                chain_code="BSC",
                coin_type=60,
                chain_protocol=ChainProtocol.EVM,
                extend_field={"rpc_url": "http://127.0.0.1:7545"},
            ),
            Blockchain(
                chain_name="Arbitrum",
                chain_code="ARB",
                coin_type=60,
                chain_protocol=ChainProtocol.EVM,
                extend_field={"rpc_url": "http://127.0.0.1:7545"},
            )
        ]

        for blockchain in blockchains:
            db.session.add(blockchain)
        db.session.flush()

        # 创建测试代币
        tokens = [
            # Ethereum 代币
            Token(
                token_code="eth_native",
                token_symbol="ETH",
                token_name="Ethereum",
                chain_id=blockchains[0].id,
                decimals=18,
                token_type=TokenType.NATIVE
            ),
            # Solana 代币
            Token(
                token_code="sol_native",
                token_symbol="SOL",
                token_name="Solana",
                chain_id=blockchains[1].id,
                decimals=9,
                token_type=TokenType.NATIVE
            ),
            # BSC 代币
            Token(
                token_code="bnb_native",
                token_symbol="BNB",
                token_name="Binance Coin",
                chain_id=blockchains[2].id,
                decimals=18,
                token_type=TokenType.NATIVE
            ),
            # Arbitrum 代币
            Token(
                token_code="arb_native",
                token_symbol="ETH",
                token_name="Ethereum on Arbitrum",
                chain_id=blockchains[3].id,
                decimals=18,
                token_type=TokenType.NATIVE
            )
        ]

        for token in tokens:
            db.session.add(token)

        db.session.commit()

        return {
            'blockchains': blockchains,
            'tokens': tokens
        }


@pytest.fixture
def test_user(app, test_blockchains):
    with app.app_context():
        User.query.filter_by(username="testuser").delete()
        WalletGroup.query.delete()
        WalletGenerated.query.delete()
        db.session.commit()

        user = User(username="testuser", email="<EMAIL>", role="admin")
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()

    yield user

    with app.app_context():
        User.query.filter_by(username="testuser").delete()
        WalletGroup.query.delete()
        WalletGenerated.query.delete()
        db.session.commit()


@pytest.fixture
def auth_headers(client, test_user):
    response = client.post(
        "/api/auth/login", json={"username": "testuser", "password": "password123"}
    )
    data = json.loads(response.data)
    return {"Authorization": f"Bearer {data['data']['token']}"}


@pytest.fixture
def normal_user(app):
    with app.app_context():
        User.query.filter_by(username="normal_user").delete()
        db.session.commit()

        user = User(username="normal_user", email="<EMAIL>", role="user")
        user.set_password("Password123!")
        db.session.add(user)
        db.session.commit()

    yield user

    with app.app_context():
        User.query.filter_by(username="normal_user").delete()
        db.session.commit()


def test_wallet_group_creation(app, client, auth_headers, test_blockchains):
    # 测试正常创建流程
    response = client.post(
        "/api/wallets",
        headers=auth_headers,
        json={}
    )
    assert response.status_code == 200
    data = json.loads(response.data)
    assert "group_id" in data["data"]
    assert len(data["data"]["wallets"]) == 4  # 4个区块链

    # 验证数据库记录
    with client.application.app_context():
        group = WalletGroup.query.first()
        assert group is not None
        wallet = WalletGenerated.query.filter_by(group_id=group.id).first()
        assert wallet.chain_code == "ETH"


def test_create_wallet_unauthorized(client):
    # 测试未授权访问
    response = client.post("/api/wallets")
    assert response.status_code == 401


def test_wallet_pagination(client, auth_headers, test_blockchains):
    # 创建15条测试数据
    with client.application.app_context():
        user = User.query.filter_by(username="testuser").first()
        with db.session.begin_nested():
            for i in range(15):
                group = WalletGroup(user_id=user.id, nonce="123")
                db.session.add(group)
                db.session.flush()
                wallet = WalletGenerated(
                    group_id=group.id,
                    user_id=user.id,
                    encrypt_key='test_key_id',
                    chain_code="ETH" if i % 2 == 0 else "SOL",
                    address=f"0xaddress{i}"
                )
                db.session.add(wallet)
        db.session.commit()

    # 测试分页
    response = client.get("/api/wallets?page=2&per_page=5", headers=auth_headers)
    data = json.loads(response.data)
    assert data["data"]["current_page"] == 2
    assert len(data["data"]["items"]) == 5
    assert data["data"]["total"] == 15


def test_chain_code_filter(client, auth_headers, test_blockchains):
    # 创建15条测试数据
    with client.application.app_context():
        user = User.query.filter_by(username="testuser").first()
        with db.session.begin_nested():
            for i in range(15):
                group = WalletGroup(user_id=user.id, nonce="123")
                db.session.add(group)
                db.session.flush()
                wallet = WalletGenerated(
                    group_id=group.id,
                    user_id=user.id,
                    encrypt_key='test_key_id',
                    chain_code="ETH" if i % 2 == 0 else "SOL",
                    address=f"******************************************"
                )
                db.session.add(wallet)
        db.session.commit()

    # 测试链类型过滤
    response = client.get("/api/wallets?chain_code=ETH", headers=auth_headers)
    data = json.loads(response.data)
    for item in data["data"]["items"]:
        assert all(w["chain_code"] == "ETH" for w in item["wallets"])


def test_invalid_chain_type(client, auth_headers, test_blockchains):
    # 测试无效链类型参数
    response = client.get(
        "/api/wallets?chain_code=INVALID",
        headers=auth_headers
    )
    assert response.status_code == 400


def test_chains(client, auth_headers, test_blockchains):
    response = client.get(
        "/api/wallets/chains",
        headers=auth_headers
    )
    assert response.status_code == 200
    data = json.loads(response.data)
    assert len(data["data"]) == 4  # 4个区块链

    # 验证返回的链数据
    chain_codes = [chain["code"] for chain in data["data"]]
    assert "ETH" in chain_codes
    assert "SOL" in chain_codes
    assert "BSC" in chain_codes
    assert "ARB" in chain_codes
