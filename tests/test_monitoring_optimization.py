#!/usr/bin/env python3
"""
测试 monitoring 优化后的逻辑
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.base import db
from app.models.device import Device
from app.models.project import Project
from app.models.device_project import DeviceProject
from app.models.blockchain import AssetType, AssetTypeEnum
from app.models.metrics import ServiceMetrics
from app.enums.biz_enums import DeviceStatus, FixtureProjectName
from app.tasks.monitoring import check_device_status, handle_light_project


def test_monitoring_optimization(app):
    """测试 monitoring 优化逻辑"""

    with app.app_context():
        try:
            # 清理测试数据
            DeviceProject.query.delete()
            AssetType.query.delete()
            Project.query.delete()
            Device.query.delete()
            db.session.commit()

            print("🧪 开始测试 monitoring 优化逻辑...")

            # 1. 创建测试设备（状态为 running）
            device = Device()
            device.name = "test-device"
            device.description = "Test Device"
            device.status = DeviceStatus.RUNNING.code  # 设置为 running 状态
            device.owner_id = 1  # 假设用户ID为1
            db.session.add(device)
            db.session.commit()
            print(f"✅ 创建测试设备: ID={device.id}, status={device.status}")

            # 2. 创建 Lighter 项目
            lighter_project = Project()
            lighter_project.name = FixtureProjectName.LIGHTER_PROJECT_NAME
            lighter_project.description = "Lighter Project for Testing"
            lighter_project.service_config_id = 1  # 假设存在的配置ID
            db.session.add(lighter_project)
            db.session.commit()
            print(f"✅ 创建 Lighter 项目: ID={lighter_project.id}, name={lighter_project.name}")

            # 3. 创建 Lighter 资产类型
            lighter_asset_type = AssetType()
            lighter_asset_type.name = "Lighter积分"
            lighter_asset_type.type = AssetTypeEnum.POINTS
            lighter_asset_type.decimals = 2
            lighter_asset_type.project_id = lighter_project.id
            db.session.add(lighter_asset_type)
            db.session.commit()
            print(f"✅ 创建 Lighter 资产类型: ID={lighter_asset_type.id}")


        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            # 清理测试数据
            try:
                DeviceProject.query.delete()
                AssetType.query.delete()
                Project.query.delete()
                Device.query.delete()
                db.session.commit()
                print("🧹 清理测试数据完成")
            except Exception as e:
                print(f"⚠️ 清理测试数据失败: {e}")


def test_device_not_running(app):
    """测试设备状态不是 running 时的逻辑"""

    with app.app_context():
        try:
            print("\n🧪 测试设备状态不是 running 的情况...")

            # 创建状态不是 running 的设备
            device = Device()
            device.name = "test-device-not-running"
            device.description = "Test Device Not Running"
            device.status = DeviceStatus.WAIT_TO_CONFIGURE.code  # 不是 running 状态
            device.owner_id = 1
            db.session.add(device)
            db.session.commit()
            print(f"✅ 创建非 running 状态设备: ID={device.id}, status={device.status}")

            # 创建 Lighter 项目
            lighter_project = Project()
            lighter_project.name = FixtureProjectName.LIGHTER_PROJECT_NAME
            lighter_project.description = "Lighter Project for Testing"
            lighter_project.service_config_id = 1
            db.session.add(lighter_project)
            db.session.commit()

            # 调用监控逻辑
            check_device_status()

            # 验证没有创建 device_project
            device_project = DeviceProject.query.filter_by(
                device_id=device.id,
                project_id=lighter_project.id
            ).first()

            if device_project is None:
                print("✅ 验证通过：非 running 状态设备没有创建 Lighter device_project")
                return True
            else:
                print("❌ 验证失败：非 running 状态设备创建了 Lighter device_project")
                return False

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

        finally:
            # 清理测试数据
            try:
                DeviceProject.query.delete()
                Project.query.delete()
                Device.query.delete()
                db.session.commit()
            except Exception as e:
                print(f"⚠️ 清理测试数据失败: {e}")


def test_service_metrics_functionality(app):
    """测试 ServiceMetrics 记录功能"""
    try:
        device = Device()
        device.name = "test-device-not-running"
        device.description = "Test Device Not Running"
        device.status = DeviceStatus.WAIT_TO_CONFIGURE.code  # 不是 running 状态
        device.owner_id = 2
        db.session.add(device)
        db.session.commit()

        print("📊 测试 ServiceMetrics 记录功能...")
        lighter_project = Project()
        lighter_project.name = FixtureProjectName.LIGHTER_PROJECT_NAME
        lighter_project.description = "Lighter Project for Testing"
        lighter_project.service_config_id = 1  # 假设存在的配置ID
        db.session.add(lighter_project)
        db.session.commit()

        # 创建 device_project 关联
        device_project = DeviceProject()
        device_project.device_id = device.id
        device_project.project_id = lighter_project.id
        device_project.continuous_running_checks = 0
        db.session.add(device_project)
        db.session.commit()
        print(f"✅ 创建 device_project: ID={device_project.id}")

        # 验证初始状态：没有 ServiceMetrics 记录
        initial_metrics = ServiceMetrics.query.filter_by(
            device_id=device.id,
            project_id=lighter_project.id,
            service_name=FixtureProjectName.LIGHTER_PROJECT_NAME
        ).first()
        print(f"📊 初始状态 - ServiceMetrics 存在: {initial_metrics is not None}")

        # 模拟第一次调用 handle_lighter_project（不满足积分条件）
        print("🔄 第一次调用 handle_lighter_project（不满足积分条件）...")
        handle_light_project(device_project)

        # 检查 ServiceMetrics 记录
        metrics_after_first = ServiceMetrics.query.filter_by(
            device_id=device.id,
            project_id=lighter_project.id,
            service_name=FixtureProjectName.LIGHTER_PROJECT_NAME
        ).first()

        if metrics_after_first:
            print(f"✅ 第一次调用后创建了 ServiceMetrics:")
            print(f"   - 积分: {metrics_after_first.points}")
            print(f"   - 运行时长: {metrics_after_first.running_time}秒")
            print(f"   - continuous_running_checks: {device_project.continuous_running_checks}")
        else:
            print("❌ 第一次调用后未创建 ServiceMetrics")
            return False

        # 模拟多次调用，直到满足积分条件（288次）
        print("🔄 模拟多次调用直到满足积分条件...")
        for i in range(287):  # 已经调用过1次，再调用287次达到288次
            handle_light_project(device_project)

        # 检查最终的 ServiceMetrics 记录
        final_metrics = ServiceMetrics.query.filter_by(
            device_id=device.id,
            project_id=lighter_project.id,
            service_name=FixtureProjectName.LIGHTER_PROJECT_NAME
        ).first()

        if final_metrics:
            expected_running_time = 288 * 300  # 288次 * 300秒
            expected_points = 1  # 0.01 * 100 (假设精度为2位小数)

            print(f"✅ 最终 ServiceMetrics 记录:")
            print(f"   - 积分: {final_metrics.points} (期望: {expected_points})")
            print(f"   - 运行时长: {final_metrics.running_time}秒 (期望: {expected_running_time}秒)")
            print(f"   - continuous_running_checks: {device_project.continuous_running_checks}")

            # 验证数据正确性
            if (final_metrics.points == expected_points and
                    final_metrics.running_time == expected_running_time):
                print("🎉 ServiceMetrics 记录功能测试通过！")
                return True
            else:
                print("❌ ServiceMetrics 记录数据不正确")
                return False
        else:
            print("❌ 最终未找到 ServiceMetrics 记录")
            return False

    except Exception as e:
        print(f"❌ ServiceMetrics 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # 清理测试数据
        try:
            ServiceMetrics.query.filter_by(device_id=device.id).delete()
            DeviceProject.query.filter_by(device_id=device.id).delete()
            db.session.commit()
        except Exception as e:
            print(f"⚠️ 清理 ServiceMetrics 测试数据失败: {e}")

