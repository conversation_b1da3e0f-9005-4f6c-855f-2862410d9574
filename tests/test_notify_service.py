import pytest
import json
from datetime import datetime, timezone, timedelta
from app.enums.biz_enums import NotifyType, NotifyStatus, NotifyPriority, NotifyPopup
from app.models import User
from app.models.base import db
from app.models.notify import Notify, UserNotify
from app.services.notify_service import NotifyService

@pytest.fixture
def client(app):
    return app.test_client()

@pytest.fixture
def admin_user(app):
    """创建管理员用户"""
    with app.app_context():
        user = User(username="admin", email="<EMAIL>", role="admin")
        user.set_password("admin")
        db.session.add(user)
        db.session.commit()

        yield user

        User.query.filter_by(username="admin").delete()
        db.session.commit()

@pytest.fixture
def admin_token(app, client, admin_user):
    """获取管理员token"""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "admin"}
    )
    return json.loads(response.data)["data"]["token"]

@pytest.fixture
def auth_headers(client, admin_token):
    """获取认证头"""
    return {"Authorization": f"Bearer {admin_token}"}

def test_create_notification(app):
    """测试创建通知"""
    with app.app_context():
        # 创建测试用户
        from app.models.user import User
        user = User(username="testuser", email="<EMAIL>")
        db.session.add(user)
        db.session.commit()

        # 创建通知
        notification = NotifyService.create_notification(
            creator=user.id,
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh_CN":"Test Notification"},
            content={"zh_CN":"This is a test notification"},
            notify_priority=NotifyPriority.NORMAL,
            user_ids=[user.id]
        )

        # 验证通知数据
        assert notification['notify_type'] == NotifyType.ANNOUNCEMENT
        assert notification['title']['zh_CN'] == "Test Notification"
        assert notification['notify_status'] == NotifyStatus.DRAFT


def test_get_user_notifications(app):
    """测试获取用户通知列表"""
    with app.app_context():
        # 创建测试数据
        user_id = 1
        for i in range(5):
            notify = Notify(
                notify_type=NotifyType.ANNOUNCEMENT,
                title={"zh_CN": f"Test {i}"},
                content={"zh_CN": f"Content {i}"},
                notify_status=NotifyStatus.PUBLISHED,
                popup_show=NotifyPopup.POPUP_SHOW_YES,
                popup_mode=5,
                popup_date_end=(datetime.utcnow() + timedelta(days=3)),
            )
            db.session.add(notify)
            db.session.flush()

            user_notify = UserNotify(
                user_id=user_id,
                notify_id=notify.id,
                is_read=False
            )
            db.session.add(user_notify)

        db.session.commit()

        # 测试基本查询
        notifications, total = NotifyService.get_user_notifications('zh_CN', user_id)
        assert len(notifications) == 5
        assert total == 5

        # 测试分页
        notifications, total = NotifyService.get_user_notifications('zh_CN',
            user_id, page=1, per_page=2
        )
        assert len(notifications) == 2
        assert total == 5

        # 测试类型过滤
        notifications, total = NotifyService.get_user_notifications('zh_CN',
            user_id, notify_type_list=[NotifyType.ANNOUNCEMENT]
        )
        assert len(notifications) == 5

        # 测试未读过滤
        a = NotifyService.get_and_read_user_notification('zh_CN', notifications[0]['id'], user_id)
        notifications, total = NotifyService.get_user_notifications('zh_CN',
            user_id, unread_only=True
        )
        assert len(notifications) == 4


def test_mark_as_read(app):
    """测试标记通知为已读"""
    with app.app_context():
        # 创建测试数据
        notify = Notify(
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh_CN":"Test"},
            content={"zh_CN":"Content"},
            notify_status=NotifyStatus.PUBLISHED,
            popup_show=NotifyPopup.POPUP_SHOW_YES,
            popup_mode=5,
            popup_date_end=(datetime.utcnow() + timedelta(days=3)),
        )
        db.session.add(notify)
        db.session.flush()

        user_notify = UserNotify(
            user_id=1,
            notify_id=notify.id,
            is_read=False
        )
        db.session.add(user_notify)
        db.session.commit()

        # 测试标记已读
        result = NotifyService.get_and_read_user_notification('zh_CN', notify.id, 1)
        assert result['is_read'] == True
        assert result['read_timestamp'] is not None

        # 测试不存在的通知
        with pytest.raises(ValueError):
            NotifyService.get_and_read_user_notification('zh_CN',999, 1)


def test_delete_notification(app):
    """测试删除通知"""
    with app.app_context():
        normal_user = User(username="test_user", email="<EMAIL>", role="user")
        db.session.add(normal_user)
        admin_user = User(username="admin", email="<EMAIL>", role="admin")
        db.session.add(admin_user)
        db.session.commit()

        # 创建测试数据
        notify = Notify(
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh":"Test"},
            content={"zh":"Content"},
            notify_status=NotifyStatus.PUBLISHED,
            popup_show=NotifyPopup.POPUP_SHOW_YES,
            popup_mode=5,
            popup_date_end=(datetime.utcnow() + timedelta(days=3)),
        )
        db.session.add(notify)

        user_notify = UserNotify(
            user_id=normal_user.id,
            notify_id=notify.id,
            is_read=False
        )
        db.session.add(user_notify)
        db.session.commit()

        # 测试普通用户删除
        NotifyService.delete_user_notify(user_notify.id, normal_user.id)
        deleted_notify = db.session.get(UserNotify, user_notify.id)
        assert deleted_notify is None

        user_notify_2 = UserNotify(
            user_id=normal_user.id,
            notify_id=notify.id,
            is_read=False
        )
        db.session.commit()

        # 测试管理员删除
        result = NotifyService.delete_notification(notify.id, admin_user.id)
        assert result['is_deleted'] == True

def test_add_notify(app, client, admin_user, auth_headers):
    """测试创建通知"""
    with app.app_context():
        popup_show = 1
        popup_mode = [1,4]
        popup_mode_val = 0
        popup_date_end = '2025-08-05 15:35:22'
        for val in popup_mode :
            popup_mode_val |= val

        # 更新项目
        response = client.post(
            f"/api/notify",
            headers=auth_headers,
            json={
                "notify_type": NotifyType.ANNOUNCEMENT,
                "title": {"zh_CN": "Test Notify Add"},
                "content": {"zh_CN": "This is a test notification"},
                "notify_priority": NotifyPriority.NORMAL,
                "user_ids": [admin_user.id],
                "popup_show": popup_show,
                "popup_mode": popup_mode,
                "popup_date_end": popup_date_end,
            },
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["message"] == "操作成功"
        assert data["data"]["popup_show"] == popup_show
        assert data["data"]["popup_mode"] == popup_mode_val
        assert data["data"]["popup_date_end"] == '2025-08-05T15:35:22'

        # 清理测试数据
        Notify.query.filter_by(id=data["data"]["id"]).delete()
        db.session.commit()

def test_save_notify(app, client, admin_user, auth_headers):
    """测试更新项目"""
    with app.app_context():
        popup_show = 1
        popup_mode = [2, 4]
        popup_mode_val = 0
        popup_date_end = '2025-08-15 15:35:22'
        for val in popup_mode:
            popup_mode_val |= val

        # 创建通知
        notification = NotifyService.create_notification(
            creator=admin_user.id,
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh_CN": "Test Notify Save"},
            content={"zh_CN": "This is a test notification"},
            notify_priority=NotifyPriority.NORMAL,
            user_ids=[admin_user.id],
            popup_show=popup_show,
            popup_mode=popup_mode,
            popup_date_end=popup_date_end,
        )

        notify_id = notification['id']

        popup_mode_save = [1, 4]
        popup_mode_val_save = 0
        for val in popup_mode_save:
            popup_mode_val_save |= val
        # 更新为禁用状态
        response = client.put(
            f"/api/notify/{notify_id}",
            headers=auth_headers,
            json={
                "op": "update",
                "popup_mode": popup_mode_save,
                "popup_date_end": popup_date_end,
            },
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["message"] == '操作成功'
        notify_save = Notify.query.get(notify_id)
        assert notify_save
        assert notify_save.popup_mode == popup_mode_val_save

        # 清理
        Notify.query.filter_by(id=notify_id).delete()
        db.session.commit()