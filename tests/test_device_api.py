"""设备 API 测试模块"""

import json

import pytest

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.notify import Notify, UserNotify
from app.models.project import Project
from app.models.project_file import ProjectFile
from app.models.service_config import ServiceConfig
from app.models.user import User
import json
import pytest

@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def test_user(app, request):
    """创建测试用户"""
    with app.app_context():
        # 清理可能存在的用户
        User.query.filter_by(username="testuser").delete()
        db.session.commit()

        # 创建新用户
        user = User(username="testuser", email="<EMAIL>", role="admin")
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()
        user_id = user.id

    yield db.session.get(User, user_id)

    # 清理测试用户
    with app.app_context():
        User.query.filter_by(username="testuser").delete()
        db.session.commit()


@pytest.fixture
def normal_user(app, request):
    """创建普通用户"""
    with app.app_context():
        # 清理可能存在的用户
        User.query.filter_by(username="normal_user").delete()
        db.session.commit()

        # 创建新用户
        user = User(username="normal_user", email="<EMAIL>", role="user")
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()
        user_id = user.id

    yield db.session.get(User, user_id)

    # 清理测试用户
    with app.app_context():
        User.query.filter_by(username="normal_user").delete()
        db.session.commit()


@pytest.fixture
def normal_user_headers(client, normal_user):
    """获取普通用户认证头"""
    response = client.post(
        "/api/auth/login", json={"username": "normal_user", "password": "password123"}
    )
    data = json.loads(response.data)
    return {"Authorization": f"Bearer {data['data']['token']}"}


def test_create_device_existing_bound_device_message_sent(client, normal_user_headers, test_user, normal_user, db_session):
    """
    测试创建设备接口：
    当设备的 mac_address 已经被其他用户（test_user）绑定时，
    普通用户（normal_user）创建设备应返回 400 错误，并且触发安全消息发送给设备所有者（test_user）。
    """
    # 使用 test_user 预先创建一台设备
    existing_mac = "00:11:22:33:44:55"
    device_data = {
        "name": "Existing Device",
        "ip_address": "*************",
        "mac_address": existing_mac,
        "owner_id": test_user.id
    }
    device = Device(**device_data)
    db_session.add(device)
    db_session.commit()

    # 普通用户尝试创建相同 mac_address 的设备
    payload = {
        "name": "Attempted Device",
        "ip_address": "*************",
        "mac_address": existing_mac
    }
    response = client.post("/api/devices", json=payload, headers=normal_user_headers)
    # 期望返回 400 错误，提示设备 mac 地址已存在
    assert response.status_code == 400
    data = response.get_json() or response.json
    assert "设备 mac 地址已存在" in data["message"]

    # 验证安全消息是否已发送到设备所有者（test_user）
    notify = db_session.query(Notify).first()

    assert notify is not None
    # 可选：检查消息内容是否包含设备 mac 地址后四位
    mac_suffix = existing_mac[-4:]
    assert mac_suffix in notify.content['en_US']


@pytest.fixture
def test_device(app, normal_user):
    """创建测试设备"""
    with app.app_context():
        # 清理可能存在的设备
        device_name = "test-device"
        Device.query.filter_by(name=device_name).delete()
        db.session.commit()

        # 创建新设备
        device = Device(
            name=device_name,
            description="Test Device",
            status="active",
            tags="test,device",
            ip_address="*************",
            mac_address="00:11:22:33:44:55",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()
        return device


@pytest.fixture
def auth_headers(client, test_user):
    """获取认证头"""
    response = client.post(
        "/api/auth/login", json={"username": "testuser", "password": "password123"}
    )
    data = json.loads(response.data)
    return {"Authorization": f"Bearer {data['data']['token']}"}


@pytest.fixture
def service_config(app):
    """创建服务配置"""
    with app.app_context():
        # 清理可能存在的配置
        ServiceConfig.query.filter_by(docker_compose="test-service").delete()
        db.session.commit()

        # 创建新配置
        config = ServiceConfig(
            name="test-service",
            description="Test Service Config",
            docker_compose="test-service",
        )
        db.session.add(config)
        db.session.commit()

        yield config

        # 清理
        ServiceConfig.query.filter_by(docker_compose="test-service").delete()
        db.session.commit()


@pytest.fixture
def test_project(app, service_config):
    """创建测试项目"""
    with app.app_context():
        project_name = "test-device-project"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        yield project

        # 清理
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()


def test_search_devices(app, client, auth_headers, normal_user):
    """测试搜索设备"""
    with app.app_context():
        # 创建测试设备
        devices = []
        for i in range(3):
            device = Device(
                name=f"search-device-{i}",
                description=f"Search Device {i}",
                status="active",
                tags="test,search",
                ip_address=f"192.168.1.{10 + i}",
                mac_address=f"00:11:22:33:44:{0x37 + i:02x}",
                owner_id=normal_user.id
            )
            db.session.add(device)
            devices.append(device)

        # 创建一个不匹配的设备
        other_device = Device(
            name="other-device",
            description="Other Device",
            status="inactive",
            tags="test",
            ip_address="*************",
            mac_address="00:11:22:33:44:66",
            owner_id=normal_user.id
        )
        db.session.add(other_device)
        db.session.commit()

        # 测试按名称搜索
        response = client.get(
            "/api/devices?name=search-device",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 3

        # 测试按状态搜索
        response = client.get(
            "/api/devices?status=active",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 3


def test_device_status_search(app, client, auth_headers, normal_user):
    """测试设备状态搜索"""
    with app.app_context():
        # 创建不同状态的测试设备
        devices = []
        statuses = ["active", "inactive", "maintenance", "error"]
        for i, status in enumerate(statuses):
            device = Device(
                name=f"status-device-{i}",
                description=f"Status Test Device {i}",
                status=status,
                tags="test",
                ip_address=f"192.168.1.{10 + i}",
                mac_address=f"00:11:22:33:44:{55 + i:02x}",
                owner_id=normal_user.id
            )
            db.session.add(device)
            devices.append(device)
        db.session.commit()

        # 测试每个状态的搜索
        for status in statuses:
            response = client.get(
                f"/api/devices?status={status}",
                headers=auth_headers
            )
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data["code"] == 200
            assert len(data["data"]["items"]) == 1


def test_device_permission(app, client, auth_headers, normal_user_headers):
    """测试设备权限"""
    with app.app_context():
        # 创建测试设备
        devices = []
        for i in range(3):
            device = Device(
                name=f"permission-device-{i}",
                description=f"Permission Test Device {i}",
                status="active",
                tags="test",
                ip_address=f"192.168.1.{10 + i}",
                mac_address=f"00:11:22:33:44:{55 + i:02x}",
                owner_id=1  # 属于管理员
            )
            db.session.add(device)
            devices.append(device)
        db.session.commit()

        # 测试管理员可以看到所有设备
        response = client.get(
            "/api/devices",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 3

        # 测试普通用户初始看不到任何设备
        response = client.get(
            "/api/devices",
            headers=normal_user_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 0

        # 将一个设备分配给普通用户
        normal_user = User.query.filter_by(username="normal_user").first()
        devices[0].owner_id = normal_user.id
        db.session.commit()

        # 测试普通用户可以看到自己的设备
        response = client.get(
            "/api/devices",
            headers=normal_user_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 1

        # 清理测试数据
        for device in devices:
            db.session.delete(device)
        db.session.commit()


def test_authorize_device(app, client, test_user, test_device, auth_headers):
    """测试授权设备"""
    with app.app_context():
        # 清理已存在的用户
        User.query.filter_by(username="normal_user").delete()
        db.session.commit()

        # 创建一个普通用户作为被授权对象
        normal_user = User(
            username="normal_user", email="<EMAIL>", role="user"
        )
        normal_user.set_password("password123")
        db.session.add(normal_user)
        db.session.commit()

        # 将 test_device 添加到会话中
        db.session.add(test_device)
        db.session.commit()

        # 测试授权
        response = client.post(
            f"/api/devices/{test_device.id}/authorize",
            headers=auth_headers,
            json={"user_id": normal_user.id},
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["message"] == "设备授权成功"


def test_batch_authorize_by_tags(app, client, normal_user, auth_headers):
    """测试按标签批量授权设备"""
    with app.app_context():
        # 创建测试设备
        devices = []
        for i in range(3):
            device = Device(
                name=f"test-device-batch-{i}",
                description=f"Test Device {i}",
                status="active",
                tags="test,batch",
                ip_address=f"192.168.1.{10 + i}",
                mac_address=f"00:11:22:33:44:{55 + i:02x}",
                owner_id=normal_user.id
            )
            db.session.add(device)
            devices.append(device)
        db.session.commit()

        # 创建另一个普通用户作为被授权对象
        other_user = User(
            username="other_user",
            email="<EMAIL>",
            role="user"
        )
        other_user.set_password("password123")
        db.session.add(other_user)
        db.session.commit()

        # 测试批量授权
        response = client.post(
            "/api/devices/batch_authorize",
            headers=auth_headers,
            json={"user_id": other_user.id, "tags": ["test", "batch"]},
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200

        # 验证授权结果
        for device in devices:
            db.session.refresh(device)
            assert device.owner_id == other_user.id

        # 清理
        for device in devices:
            db.session.delete(device)
        db.session.delete(other_user)
        db.session.commit()


def test_device_project_service_config(app, client, test_device, test_project, auth_headers, test_user):
    """测试设备项目的服务配置生成"""
    with app.app_context():
        # 设置设备所有者为测试用户
        test_device.owner_id = test_user.id
        db.session.add(test_device)
        db.session.commit()

        # 创建项目文件
        project_file = ProjectFile(
            name="config.yml",
            content="name: {{ name }}\nport: {{ port }}",
            project_id=test_project.id
        )
        db.session.add(project_file)
        db.session.commit()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="created",
            data={
                "name": "test-service",
                "port": 8080
            }
        )
        db.session.add(device_project)
        db.session.commit()

        # 获取服务配置
        response = client.get(
            f"/api/devices/{test_device.id}/projects/{test_project.id}/service_config",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert "config" in data["data"]
        assert "name: test-service" in data["data"]["config"]["files"]["config.yml"]
        assert "port: 8080" in data["data"]["config"]["files"]["config.yml"]

        # 清理
        db.session.delete(project_file)
        db.session.delete(device_project)
        db.session.commit()


def test_device_pagination(app, client, auth_headers, normal_user):
    """测试设备列表分页"""
    with app.app_context():
        # 创建测试设备
        devices = []
        for i in range(15):  # 创建15个设备以测试分页
            device = Device(
                name=f"pagination-device-{i}",
                description=f"Pagination Device {i}",
                status="active",
                tags="test,pagination",
                ip_address=f"192.168.1.{10 + i}",
                mac_address=f"00:11:22:33:44:{0x37 + i:02x}",
                owner_id=normal_user.id
            )
            db.session.add(device)
            devices.append(device)
        db.session.commit()

        # 测试默认分页（第1页，每页10条）
        response = client.get(
            "/api/devices",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 10
        assert data["data"]["total"] >= 15
        assert data["data"]["page"] == 1
        assert data["data"]["per_page"] == 10

        # 测试自定义分页（第2页，每页5条）
        response = client.get(
            "/api/devices?page=2&per_page=5",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 5
        assert data["data"]["total"] >= 15
        assert data["data"]["page"] == 2
        assert data["data"]["per_page"] == 5

        # 测试超出范围的页码
        response = client.get(
            "/api/devices?page=999",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) == 0
        assert data["data"]["total"] >= 15
        assert data["data"]["page"] == 999

        # 测试每页数量限制
        response = client.get(
            "/api/devices?per_page=200",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) <= 100  # 不应超过100
        assert data["data"]["per_page"] == 100

        # 清理测试数据
        for device in devices:
            db.session.delete(device)
        db.session.commit()


def test_regenerate_system_app_configs(app, client, test_device, auth_headers, normal_user_headers):
    """测试重新生成设备的系统应用配置"""
    with app.app_context():
        # 确保测试设备在数据库会话中
        db.session.add(test_device)
        db.session.commit()

        # 测试普通用户无权限访问
        response = client.post(
            f"/api/devices/{test_device.id}/system_app_configs/regenerate",
            headers=normal_user_headers
        )
        assert response.status_code == 403
        data = json.loads(response.data)
        assert data["code"] == 403
        assert "需要管理员权限" in data["message"]

        # 测试设备不存在的情况
        response = client.post(
            "/api/devices/99999/system_app_configs/regenerate",
            headers=auth_headers
        )
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data["code"] == 404
        assert "设备不存在" in data["message"]

        # 测试成功重新生成配置
        response = client.post(
            f"/api/devices/{test_device.id}/system_app_configs/regenerate",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert "系统应用配置重新生成成功" in data["message"]

        # 验证生成的配置内容
        device_name = f"depra{test_device.id}"
        system_app_configs = data["data"]["system_app_configs"]
        assert "system_apps" in system_app_configs
        assert len(system_app_configs["system_apps"]) == 1

        frpc_config = system_app_configs["system_apps"][0]
        assert frpc_config["name"] == "frpc"
        assert frpc_config["command"] == "frpc"
        assert frpc_config["args"] == ["-c", "frpc.toml"]
        assert "frpc.toml" in frpc_config["configs"]
        assert device_name in frpc_config["configs"]["frpc.toml"]

        # 清理测试数据
        db.session.delete(test_device)
        db.session.commit()


def test_get_device_runtime(app, client, test_device, auth_headers):
    """测试获取设备运行时信息"""
    with app.app_context():
        # 准备测试数据
        from datetime import datetime, timezone

        from app.models.base import db
        from app.models.device_runtime import DeviceRuntime

        # 将 test_device 添加到会话中
        db.session.add(test_device)
        db.session.commit()

        device_id = test_device.id
        current_time = datetime.now(timezone.utc)

        # 创建设备运行时数据
        runtime = DeviceRuntime(
            device_id=device_id,
            started_at=current_time,
            running_time=3600,
            lan_ip="*************",
            public_ip="*******",
            network={
                "download_speed": 1000000,
                "upload_speed": 500000
            },
            disk={
                "total": 1000000000,
                "used": 500000000,
                "free": 500000000
            },
            cpu={
                "usage": 50.5,
                "cores": 4
            },
            memory={
                "total": 8000000000,
                "used": 4000000000,
                "free": 4000000000
            },
            updated_at=current_time
        )
        db.session.add(runtime)
        db.session.commit()

        # 发送请求
        response = client.get(
            f"/api/devices/{device_id}/runtime",
            headers=auth_headers
        )

        # 验证响应
        assert response.status_code == 200
        data = response.json
        assert data["code"] == 200
        assert data["message"] == "success"
        assert data["data"] is not None
        assert data["data"]["device_id"] == device_id
        assert data["data"]["running_time"] == 3600
        assert data["data"]["lan_ip"] == "*************"
        assert data["data"]["public_ip"] == "*******"
        assert data["data"]["network"] == {
            "download_speed": 1000000,
            "upload_speed": 500000
        }

        # 清理测试数据
        db.session.delete(runtime)
        db.session.commit()


def test_get_device_runtime_no_data(app, client, test_device, auth_headers):
    """测试获取不存在的设备运行时信息"""
    with app.app_context():
        # 将 test_device 添加到会话中
        db.session.add(test_device)
        db.session.commit()

        device_id = test_device.id
        response = client.get(
            f"/api/devices/{device_id}/runtime",
            headers=auth_headers
        )

        assert response.status_code == 200
        data = response.json
        assert data["code"] == 200
        assert data["message"] == "success"
        assert data["data"] is None


def test_get_device_runtime_device_not_found(client, auth_headers):
    """测试获取不存在设备的运行时信息"""
    response = client.get(
        "/api/devices/99999/runtime",
        headers=auth_headers
    )

    assert response.status_code == 404
    data = response.json
    assert data["code"] == 404
    assert data["message"] == "设备不存在"
    assert data["data"] is None


def test_sensitive_fields_not_exposed(app, client, test_device, auth_headers):
    """测试敏感字段不会被暴露给前端"""
    sensitive_fields = ["token", "system_app_configs", "service_configs"]

    with app.app_context():
        # 确保测试设备在数据库会话中
        db.session.add(test_device)
        db.session.commit()

        # 测试设备列表接口
        response = client.get("/api/devices", headers=auth_headers)
        assert response.status_code == 200
        data = json.loads(response.data)
        device_data = data["data"]["items"][0]
        for field in sensitive_fields:
            assert field not in device_data, f"敏感字段 {field} 不应该在设备列表接口中返回"

        # 测试获取单个设备详情接口
        response = client.get(f"/api/devices/{test_device.id}", headers=auth_headers)
        assert response.status_code == 200
        data = json.loads(response.data)
        device_data = data["data"]
        for field in sensitive_fields:
            assert field not in device_data, f"敏感字段 {field} 不应该在设备详情接口中返回"

        # 测试管理员接口应该返回敏感字段
        response = client.post(
            f"/api/devices/{test_device.id}/system_app_configs/regenerate",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        device_data = data["data"]
        for field in sensitive_fields:
            assert field in device_data, f"敏感字段 {field} 应该在管理员接口中返回"

        # 清理测试数据
        db.session.delete(test_device)
        db.session.commit()


def test_get_device_with_runtime(app, client, auth_headers, normal_user):
    """测试获取设备详情时包含 runtime 信息"""
    with app.app_context():
        from app.models.metrics import ServiceMetrics

        # 创建测试设备
        device = Device(
            name="test-device",
            description="Test Device",
            status="active",
            tags="test,device",
            ip_address="*************",
            mac_address="00:11:22:33:44:55",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()

        try:
            # 创建测试数据：3个服务，每个服务有不同的运行时间
            services = [
                ServiceMetrics(
                    device_id=device.id,
                    service_name=f"service_{i}",
                    running_time=3600 * (i + 1)  # 1小时、2小时、3小时
                )
                for i in range(3)
            ]
            db.session.add_all(services)
            db.session.commit()

            # 测试获取设备详情
            response = client.get(
                f"/api/devices/{device.id}",
                headers=auth_headers
            )
            assert response.status_code == 200

            data = json.loads(response.data)
            device_info = data["data"]

            # 验证设备基本信息
            assert device_info["id"] == device.id
            assert device_info["name"] == device.name
            assert device_info["ip_address"] == device.ip_address
            assert device_info["mac_address"] == device.mac_address

            # 验证 runtime 结构
            assert "runtime" in device_info
            assert "running_time" in device_info["runtime"]
            # 验证总运行时间是否正确（1小时 + 2小时 + 3小时 = 6小时 = 21600秒）
            assert device_info["runtime"]["running_time"] == 21600

            # 测试不存在的设备
            response = client.get(
                "/api/devices/999999",
                headers=auth_headers
            )
            assert response.status_code == 404

        finally:
            # 清理测试数据
            ServiceMetrics.query.filter_by(device_id=device.id).delete()
            db.session.delete(device)
            db.session.commit()

def test_update_service(app, client, auth_headers, normal_user):
    with app.app_context():
        # 创建测试设备
        device = Device(
            name="update-device-1",
            description="update Device 1",
            status="active",
            tags="test,update",
            ip_address="************",
            mac_address=f"00:11:22:33:44:{0x37 + 1:02x}",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()
        try:
            response = client.put(
                "/api/devices/1",
                headers=auth_headers,
                json={"service_configs": {"proxy": "9999"}}
            )
            assert response.status_code == 200
            device = db.session.get(Device, 1)
            assert device.service_configs["proxy"] == "9999"

        finally:
            db.session.delete(device)
            db.session.commit()

def test_get_service(app, client, auth_headers, normal_user):
    with app.app_context():
        # 创建测试设备
        device = Device(
            name="get-device-1",
            description="get Device 1",
            status="active",
            tags="test,get",
            ip_address="************",
            mac_address=f"00:11:22:33:44:{0x37 + 1:02x}",
            owner_id=normal_user.id,
            service_configs = {
                "proxy": "127.0.0.1"
            }
        )
        db.session.add(device)
        db.session.commit()
        try:
            response = client.get(
                "/api/devices/1",
                headers=auth_headers,
            )
            assert response.status_code == 200
            print(response.data)
            data = json.loads(response.data)
            assert data["code"] == 200
            assert data["data"]["name"] == "get-device-1"
            assert data["data"]["proxy"] == "127.0.0.1"

        finally:
            db.session.delete(device)
            db.session.commit()

def test_bind_auto_created_device(app, client, normal_user_headers, normal_user, db_session):
    """测试绑定自动创建的无所有者设备"""
    with app.app_context():
        # 创建一个无所有者的设备（模拟自动创建的设备）
        from app.models.device import Device
        auto_device = Device(
            name="Device-1234",
            mac_address="AA:BB:CC:DD:12:34",
            ip_address="0.0.0.0",
            owner_id=None  # 无所有者
        )
        db_session.add(auto_device)
        db_session.commit()

        # 尝试绑定这个设备
        response = client.post(
            "/api/devices",
            headers=normal_user_headers,
            json={
                "name": "My Device",
                "ip_address": "*************",
                "mac_address": "AA:BB:CC:DD:12:34",
                "description": "My new device"
            }
        )

        # 应该成功绑定
        assert response.status_code == 200
        data = response.get_json()
        assert data["message"] == "设备绑定成功"
        assert data["data"]["name"] == "My Device"
        assert data["data"]["ip_address"] == "*************"
        assert data["data"]["owner_id"] == normal_user.id

        # 验证设备确实被更新了
        updated_device = Device.query.filter_by(mac_address="AA:BB:CC:DD:12:34").first()
        assert updated_device.owner_id == normal_user.id
        assert updated_device.name == "My Device"
        assert updated_device.ip_address == "*************"
        assert updated_device.description == "My new device"