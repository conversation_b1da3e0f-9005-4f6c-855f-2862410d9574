#!/usr/bin/env python3
"""
测试 monitoring 优化后的逻辑
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.base import db
from app.models.device import Device
from app.models.project import Project
from app.models.device_project import DeviceProject
from app.models.blockchain import AssetType, AssetTypeEnum
from app.enums.biz_enums import DeviceStatus, FixtureProjectName
from app.tasks.monitoring import check_device_status, handle_lighter_project


def test_monitoring_optimization():
    """测试 monitoring 优化逻辑"""
    app = create_app()
    
    with app.app_context():
        try:
            # 清理测试数据
            DeviceProject.query.delete()
            AssetType.query.delete()
            Project.query.delete()
            Device.query.delete()
            db.session.commit()
            
            print("🧪 开始测试 monitoring 优化逻辑...")
            
            # 1. 创建测试设备（状态为 running）
            device = Device()
            device.name = "test-device"
            device.description = "Test Device"
            device.status = DeviceStatus.RUNNING.code  # 设置为 running 状态
            device.owner_id = 1  # 假设用户ID为1
            db.session.add(device)
            db.session.commit()
            print(f"✅ 创建测试设备: ID={device.id}, status={device.status}")
            
            # 2. 创建 Lighter 项目
            lighter_project = Project()
            lighter_project.name = FixtureProjectName.LIGHTER_PROJECT_NAME
            lighter_project.description = "Lighter Project for Testing"
            lighter_project.service_config_id = 1  # 假设存在的配置ID
            db.session.add(lighter_project)
            db.session.commit()
            print(f"✅ 创建 Lighter 项目: ID={lighter_project.id}, name={lighter_project.name}")
            
            # 3. 创建 Lighter 资产类型
            lighter_asset_type = AssetType()
            lighter_asset_type.name = "Lighter积分"
            lighter_asset_type.type = AssetTypeEnum.POINTS
            lighter_asset_type.decimals = 2
            lighter_asset_type.project_id = lighter_project.id
            db.session.add(lighter_asset_type)
            db.session.commit()
            print(f"✅ 创建 Lighter 资产类型: ID={lighter_asset_type.id}")
            
            # 4. 验证初始状态：设备没有 Lighter device_project
            initial_device_project = DeviceProject.query.filter_by(
                device_id=device.id,
                project_id=lighter_project.id
            ).first()
            print(f"📊 初始状态 - Lighter device_project 存在: {initial_device_project is not None}")
            
            # 5. 模拟调用 check_device_status（这应该会自动创建 device_project）
            print("🔄 调用 check_device_status...")
            
            # 模拟 handle_lighter_project 函数（避免实际的积分操作）
            def mock_handle_lighter_project(device_project):
                print(f"🎯 模拟处理 Lighter 项目: device_id={device_project.device_id}, project_id={device_project.project_id}")
                device_project.continuous_running_checks = getattr(device_project, 'continuous_running_checks', 0) + 1
                return True
            
            # 替换原函数
            import app.tasks.monitoring
            original_handle = app.tasks.monitoring.handle_lighter_project
            app.tasks.monitoring.handle_lighter_project = mock_handle_lighter_project
            
            try:
                # 调用优化后的监控逻辑
                check_device_status()
                
                # 6. 验证结果：应该自动创建了 device_project
                created_device_project = DeviceProject.query.filter_by(
                    device_id=device.id,
                    project_id=lighter_project.id
                ).first()
                
                if created_device_project:
                    print(f"✅ 成功创建 Lighter device_project: ID={created_device_project.id}")
                    print(f"   - device_id: {created_device_project.device_id}")
                    print(f"   - project_id: {created_device_project.project_id}")
                    print(f"   - state: {created_device_project.state}")
                    print(f"   - continuous_running_checks: {created_device_project.continuous_running_checks}")
                else:
                    print("❌ 未能创建 Lighter device_project")
                    return False
                
                # 7. 再次调用，验证不会重复创建
                print("🔄 再次调用 check_device_status...")
                check_device_status()
                
                device_projects_count = DeviceProject.query.filter_by(
                    device_id=device.id,
                    project_id=lighter_project.id
                ).count()
                
                if device_projects_count == 1:
                    print("✅ 验证通过：没有重复创建 device_project")
                else:
                    print(f"❌ 验证失败：创建了 {device_projects_count} 个 device_project")
                    return False
                
                print("🎉 所有测试通过！优化逻辑工作正常")
                return True
                
            finally:
                # 恢复原函数
                app.tasks.monitoring.handle_lighter_project = original_handle
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 清理测试数据
            try:
                DeviceProject.query.delete()
                AssetType.query.delete()
                Project.query.delete()
                Device.query.delete()
                db.session.commit()
                print("🧹 清理测试数据完成")
            except Exception as e:
                print(f"⚠️ 清理测试数据失败: {e}")


def test_device_not_running():
    """测试设备状态不是 running 时的逻辑"""
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🧪 测试设备状态不是 running 的情况...")
            
            # 创建状态不是 running 的设备
            device = Device()
            device.name = "test-device-not-running"
            device.description = "Test Device Not Running"
            device.status = DeviceStatus.WAIT_TO_CONFIGURE.code  # 不是 running 状态
            device.owner_id = 1
            db.session.add(device)
            db.session.commit()
            print(f"✅ 创建非 running 状态设备: ID={device.id}, status={device.status}")
            
            # 创建 Lighter 项目
            lighter_project = Project()
            lighter_project.name = FixtureProjectName.LIGHTER_PROJECT_NAME
            lighter_project.description = "Lighter Project for Testing"
            lighter_project.service_config_id = 1
            db.session.add(lighter_project)
            db.session.commit()
            
            # 调用监控逻辑
            check_device_status()
            
            # 验证没有创建 device_project
            device_project = DeviceProject.query.filter_by(
                device_id=device.id,
                project_id=lighter_project.id
            ).first()
            
            if device_project is None:
                print("✅ 验证通过：非 running 状态设备没有创建 Lighter device_project")
                return True
            else:
                print("❌ 验证失败：非 running 状态设备创建了 Lighter device_project")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        
        finally:
            # 清理测试数据
            try:
                DeviceProject.query.delete()
                Project.query.delete()
                Device.query.delete()
                db.session.commit()
            except Exception as e:
                print(f"⚠️ 清理测试数据失败: {e}")


if __name__ == "__main__":
    print("🚀 开始测试 monitoring 优化...")
    
    # 测试1：基本优化逻辑
    success1 = test_monitoring_optimization()
    
    # 测试2：非 running 状态设备
    success2 = test_device_not_running()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！monitoring 优化成功")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
