#!/usr/bin/env python3
"""
简单的 Ganache 连接测试
只测试连接和基本信息，不执行交易
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.ganache_test_utils import create_ganache_utils


def simple_connection_test():
    """简单连接测试，不执行任何交易"""
    print("🔍 简单 Ganache 连接测试...")
    print("=" * 50)
    
    try:
        # 创建工具实例
        ganache = create_ganache_utils()
        
        print(f"\n📊 网络信息:")
        print(f"  区块高度: {ganache.web3.eth.block_number}")
        print(f"  网络ID: {ganache.web3.eth.chain_id}")
        print(f"  Gas 价格: {ganache.web3.eth.gas_price} wei")
        
        # 获取账户信息
        accounts = ganache.get_all_accounts_info()
        print(f"\n💰 账户信息:")
        print(f"  总账户数: {len(accounts)}")
        
        # 显示前5个账户的详细信息
        for i, account in enumerate(accounts[:5]):
            print(f"\n  账户 {i}:")
            print(f"    地址: {account['address']}")
            print(f"    余额: {account['balance_eth']} ETH")
            print(f"    Nonce: {account['nonce']}")
        
        # 检查 Web3 版本
        import web3
        print(f"\n🔧 技术信息:")
        print(f"  Web3.py 版本: {web3.__version__}")
        print(f"  连接状态: {'✅ 已连接' if ganache.web3.is_connected() else '❌ 未连接'}")
        
        # 获取最新区块信息
        try:
            latest_block = ganache.web3.eth.get_block('latest')
            print(f"  最新区块哈希: {latest_block.hash.hex()}")
            print(f"  区块时间戳: {latest_block.timestamp}")
        except Exception as e:
            print(f"  获取区块信息失败: {e}")
        
        print(f"\n✅ 连接测试成功!")
        print(f"🎯 Ganache 运行正常，可以进行开发测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        print(f"\n💡 解决建议:")
        print(f"1. 确保 Ganache 已启动")
        print(f"2. 检查端口是否为 7545")
        print(f"3. 确认 RPC URL: http://127.0.0.1:7545")
        print(f"4. 如果使用 Ganache GUI，确保 RPC 服务器已启动")
        
        return False


def check_ganache_type():
    """检测 Ganache 类型（CLI 还是 GUI）"""
    print("\n🔍 检测 Ganache 类型...")
    
    try:
        ganache = create_ganache_utils()
        
        # 尝试获取客户端版本信息
        try:
            client_version = ganache.web3.client_version
            print(f"客户端版本: {client_version}")
            
            if "TestRPC" in client_version or "ganache" in client_version.lower():
                if "cli" in client_version.lower():
                    print("🖥️  检测到: Ganache CLI")
                    print("✅ 推荐：CLI 版本对开发更友好，账户默认解锁")
                else:
                    print("🖼️  检测到: Ganache GUI")
                    print("⚠️  注意：GUI 版本可能需要手动解锁账户才能发送交易")
            else:
                print(f"🤔 未知客户端: {client_version}")
                
        except Exception as e:
            print(f"无法获取客户端版本: {e}")
            
        # 测试账户是否解锁
        print(f"\n🔐 测试账户解锁状态...")
        accounts = ganache.accounts
        if accounts:
            test_account = accounts[0]
            try:
                # 尝试构建一个简单交易（不发送）
                transaction = {
                    'to': accounts[1] if len(accounts) > 1 else test_account,
                    'value': ganache.web3.to_wei(0.001, 'ether'),
                    'gas': 21000,
                    'gasPrice': ganache.web3.to_wei('20', 'gwei'),
                    'nonce': ganache.web3.eth.get_transaction_count(test_account),
                }
                
                # 尝试估算 gas（这不会发送交易）
                gas_estimate = ganache.web3.eth.estimate_gas(transaction)
                print(f"✅ 账户可用于交易（Gas 估算: {gas_estimate}）")
                
            except Exception as e:
                print(f"⚠️  账户可能被锁定: {e}")
                
    except Exception as e:
        print(f"❌ 检测失败: {e}")


if __name__ == "__main__":
    print("🚀 Ganache 简单测试工具")
    print("=" * 50)
    
    # 基本连接测试
    if simple_connection_test():
        # 如果连接成功，进行更详细的检测
        check_ganache_type()
    
    print("\n" + "=" * 50)
    print("测试完成!")
