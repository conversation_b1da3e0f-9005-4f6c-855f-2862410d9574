-- 积分归档相关表结构创建脚本
-- 执行前请备份现有数据

-- 1. 积分记录归档表
CREATE TABLE IF NOT EXISTS point_records_archive (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id INT NOT NULL COMMENT '用户ID',
    record_type VARCHAR(20) NOT NULL COMMENT '记录类型：task, invite, project',
    archive_type ENUM('daily', 'weekly', 'monthly') NOT NULL COMMENT '归档类型',
    archive_date DATE NOT NULL COMMENT '归档日期',
    total_points DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '总积分',
    record_count INT NOT NULL DEFAULT 0 COMMENT '记录数量',
    extra_data JSON COMMENT '扩展数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_user_type_date (user_id, archive_type, archive_date),
    INDEX idx_archive_date (archive_date),
    INDEX idx_user_record_type (user_id, record_type),
    INDEX idx_archive_type_date (archive_type, archive_date),
    
    -- 唯一约束
    UNIQUE KEY uk_user_type_date (user_id, record_type, archive_type, archive_date),
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分记录归档表';

-- 2. 积分统计快照表
CREATE TABLE IF NOT EXISTS point_statistics_snapshot (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id INT NOT NULL COMMENT '用户ID',
    snapshot_date DATE NOT NULL COMMENT '快照日期',
    total_points DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '总积分',
    task_points DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '任务积分',
    invite_points DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '邀请积分',
    project_points DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '项目积分',
    daily_increase DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '日增长',
    weekly_increase DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '周增长',
    monthly_increase DECIMAL(18,8) NOT NULL DEFAULT 0 COMMENT '月增长',
    rank_position INT COMMENT '排名位置',
    rank_change INT DEFAULT 0 COMMENT '排名变化',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_user_date (user_id, snapshot_date),
    INDEX idx_snapshot_date (snapshot_date),
    INDEX idx_total_points (total_points DESC),
    INDEX idx_rank_position (rank_position),
    
    -- 唯一约束
    UNIQUE KEY uk_user_date (user_id, snapshot_date),
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分统计快照表';

-- 3. 积分迁移日志表
CREATE TABLE IF NOT EXISTS point_migration_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    migration_type VARCHAR(50) NOT NULL COMMENT '迁移类型',
    migration_date DATE NOT NULL COMMENT '迁移日期',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status VARCHAR(20) NOT NULL DEFAULT 'running' COMMENT '状态：running, success, failed, partial_success',
    total_records INT DEFAULT 0 COMMENT '总记录数',
    processed_records INT DEFAULT 0 COMMENT '已处理记录数',
    success_records INT DEFAULT 0 COMMENT '成功记录数',
    failed_records INT DEFAULT 0 COMMENT '失败记录数',
    error_message TEXT COMMENT '错误信息',
    error_details JSON COMMENT '错误详情',
    config JSON COMMENT '迁移配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_migration_date (migration_date),
    INDEX idx_status (status),
    INDEX idx_migration_type (migration_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分迁移日志表';

-- 4. 积分归档配置表
CREATE TABLE IF NOT EXISTS point_archive_config (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型',
    config_value JSON NOT NULL COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_config_type (config_type),
    UNIQUE KEY uk_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分归档配置表';

-- 5. 插入默认配置
INSERT INTO point_archive_config (config_key, config_type, config_value, description) VALUES
('archive.retention.daily', 'retention', '{"days": 90}', '日归档数据保留天数'),
('archive.retention.weekly', 'retention', '{"weeks": 52}', '周归档数据保留周数'),
('archive.retention.monthly', 'retention', '{"months": 24}', '月归档数据保留月数'),
('archive.batch_size', 'performance', '{"size": 1000}', '归档批处理大小'),
('archive.schedule.daily', 'schedule', '{"hour": 2, "minute": 0}', '日归档执行时间'),
('archive.schedule.weekly', 'schedule', '{"day": 1, "hour": 3, "minute": 0}', '周归档执行时间（周一）'),
('archive.schedule.monthly', 'schedule', '{"day": 1, "hour": 4, "minute": 0}', '月归档执行时间（每月1号）'),
('migration.batch_size', 'performance', '{"size": 5000}', '迁移批处理大小'),
('migration.parallel_workers', 'performance', '{"workers": 4}', '并行迁移工作线程数'),
('archive.enabled', 'feature', '{"daily": true, "weekly": true, "monthly": true}', '归档功能开关')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;

-- 6. 为原始 point_records 表添加归档状态字段（如果不存在）
ALTER TABLE point_records 
ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP NULL COMMENT '归档时间',
ADD COLUMN IF NOT EXISTS archive_status ENUM('pending', 'archived', 'migrated') DEFAULT 'pending' COMMENT '归档状态';

-- 7. 为原始表添加归档相关索引
CREATE INDEX IF NOT EXISTS idx_archive_status ON point_records(archive_status);
CREATE INDEX IF NOT EXISTS idx_archived_at ON point_records(archived_at);
CREATE INDEX IF NOT EXISTS idx_created_archive ON point_records(created_at, archive_status);

-- 8. 创建分区表（可选，用于大数据量优化）
-- 注意：分区表需要根据实际情况调整
/*
ALTER TABLE point_records_archive 
PARTITION BY RANGE (YEAR(archive_date)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

ALTER TABLE point_statistics_snapshot 
PARTITION BY RANGE (YEAR(snapshot_date)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- 9. 创建视图，用于兼容性查询
CREATE OR REPLACE VIEW v_user_point_summary AS
SELECT 
    u.id as user_id,
    u.email,
    COALESCE(ua.available_balance, 0) as total_points,
    COALESCE(ps.rank_position, 0) as rank_position,
    COALESCE(ps.daily_increase, 0) as daily_increase,
    ps.snapshot_date as last_update_date
FROM users u
LEFT JOIN user_assets ua ON u.id = ua.user_id 
    AND ua.asset_type_id = (
        SELECT id FROM asset_types 
        WHERE name = 'UBI积分' AND type = 'POINTS' 
        LIMIT 1
    )
LEFT JOIN point_statistics_snapshot ps ON u.id = ps.user_id 
    AND ps.snapshot_date = (
        SELECT MAX(snapshot_date) 
        FROM point_statistics_snapshot ps2 
        WHERE ps2.user_id = u.id
    );

-- 10. 创建存储过程用于数据归档
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS ArchivePointRecords(
    IN archive_date_param DATE,
    IN archive_type_param VARCHAR(10),
    IN batch_size_param INT DEFAULT 1000
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_user_id INT;
    DECLARE v_record_type VARCHAR(20);
    DECLARE v_total_points DECIMAL(18,8);
    DECLARE v_record_count INT;
    
    -- 游标定义
    DECLARE cur CURSOR FOR 
        SELECT 
            user_id, 
            record_type, 
            SUM(points) as total_points,
            COUNT(*) as record_count
        FROM point_records 
        WHERE DATE(created_at) = archive_date_param
            AND archive_status = 'pending'
        GROUP BY user_id, record_type;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 打开游标
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_user_id, v_record_type, v_total_points, v_record_count;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 插入归档记录
        INSERT INTO point_records_archive (
            user_id, record_type, archive_type, archive_date,
            total_points, record_count
        ) VALUES (
            v_user_id, v_record_type, archive_type_param, archive_date_param,
            v_total_points, v_record_count
        ) ON DUPLICATE KEY UPDATE
            total_points = total_points + VALUES(total_points),
            record_count = record_count + VALUES(record_count),
            updated_at = CURRENT_TIMESTAMP;
        
    END LOOP;
    
    -- 关闭游标
    CLOSE cur;
    
    -- 更新原始记录状态
    UPDATE point_records 
    SET archive_status = 'archived', archived_at = CURRENT_TIMESTAMP
    WHERE DATE(created_at) = archive_date_param
        AND archive_status = 'pending';
    
    -- 提交事务
    COMMIT;
    
END //

DELIMITER ;

-- 执行完成提示
SELECT 'Point archive tables created successfully!' as message;
