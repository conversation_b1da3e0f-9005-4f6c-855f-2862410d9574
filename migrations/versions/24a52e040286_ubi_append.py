"""ubi版本迭代

Revision ID: 24a52e040286
Create Date: 2025-06-10 14:23:53.806023

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '24a52e040286'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('notify', schema=None) as batch_op:
        batch_op.add_column(sa.Column('popup_show', sa.Integer(), nullable=True, comment='显示弹窗'))
        batch_op.add_column(sa.Column('popup_mode', sa.Integer(), nullable=True, comment='弹窗方式'))
        batch_op.add_column(sa.Column('popup_date_end', sa.DateTime(), nullable=True, comment='弹窗结束时间'))

    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.add_column(sa.Column('special_login_hint', sa.String(length=240), nullable=True, comment='特殊登录提示'))
        batch_op.add_column(sa.Column('show_new_label', sa.Integer(), nullable=True, comment='显示新增标签'))
        batch_op.add_column(sa.Column('show_day', sa.Integer(), nullable=True, comment='显示时长'))
        batch_op.add_column(sa.Column('show_date_end', sa.DateTime(), nullable=True, comment='新增标签显示时长结束'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('projects', schema=None) as batch_op:
        batch_op.drop_column('show_date_end')
        batch_op.drop_column('show_day')
        batch_op.drop_column('show_new_label')
        batch_op.drop_column('special_login_hint')

    with op.batch_alter_table('notify', schema=None) as batch_op:
        batch_op.drop_column('popup_date_end')
        batch_op.drop_column('popup_mode')
        batch_op.drop_column('popup_show')

    # ### end Alembic commands ###
