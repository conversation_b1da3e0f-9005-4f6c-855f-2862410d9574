"""积分市场

Revision ID: 178eacd6e11f
Revises: e4d02f3dba9a
Create Date: 2025-06-23 09:13:01.270840

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '178eacd6e11f'
down_revision = 'e4d02f3dba9a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('address_mappings',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='映射ID'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('address', sa.String(length=128), nullable=False, comment='区块链地址'),
    sa.Column('chain_type', sa.String(length=20), nullable=False, comment='链类型(如eth,btc)'),
    sa.Column('address_type', sa.Enum('DEPOSIT', 'WITHDRAW', 'BOTH', name='addressmappingtypeenum'), server_default='BOTH', nullable=True, comment='地址类型'),
    sa.Column('is_active', sa.Boolean(), server_default='1', nullable=True, comment='是否激活'),
    sa.Column('last_activity_block', sa.BigInteger(), server_default='0', nullable=True, comment='最后活动区块'),
    sa.Column('last_activity_time', sa.DateTime(), nullable=True, comment='最后活动时间'),
    sa.Column('total_deposits', sa.Integer(), server_default='0', nullable=True, comment='总充值次数'),
    sa.Column('total_withdrawals', sa.Integer(), server_default='0', nullable=True, comment='总提现次数'),
    sa.Column('total_deposit_amount', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=True, comment='总充值金额'),
    sa.Column('total_withdrawal_amount', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=True, comment='总提现金额'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('address', 'chain_type', name='uk_address_chain')
    )
    with op.batch_alter_table('address_mappings', schema=None) as batch_op:
        batch_op.create_index('idx_address_chain', ['address', 'chain_type'], unique=False)
        batch_op.create_index('idx_user_chain', ['user_id', 'chain_type'], unique=False)

    op.create_table('asset_freeze_logs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('asset_type_id', sa.Integer(), nullable=False, comment='资产类型ID'),
    sa.Column('operation_type', sa.Enum('FREEZE', 'UNFREEZE', 'OCCUPY', 'RELEASE'), nullable=False, comment='操作类型'),
    sa.Column('amount', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='操作金额'),
    sa.Column('related_order_id', sa.String(length=64), nullable=True, comment='关联订单ID'),
    sa.Column('remark', sa.String(length=255), nullable=True, comment='操作备注'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('asset_freeze_logs', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_asset_freeze_logs_asset_type_id'), ['asset_type_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_asset_freeze_logs_user_id'), ['user_id'], unique=False)

    op.create_table('asset_transaction',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='交易ID'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('asset_type_id', sa.Integer(), nullable=False, comment='资产类型ID'),
    sa.Column('amount', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='交易金额'),
    sa.Column('balance_before', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='交易前余额'),
    sa.Column('balance_after', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='交易后余额'),
    sa.Column('transaction_type', sa.Enum('DEPOSIT', 'WITHDRAW', 'TRADE', 'MINING', 'TRANSFER', 'SYSTEM_ADJUST', name='transactiontypeenum'), nullable=False, comment='交易类型'),
    sa.Column('reference_id', sa.String(length=64), nullable=True, comment='关联ID'),
    sa.Column('status', sa.Enum('PENDING', 'SUCCESS', 'FAILED', name='transactionstatusenum'), server_default='SUCCESS', nullable=False, comment='交易状态'),
    sa.Column('extend_field', sa.JSON(), nullable=True, comment='扩展字段'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('blockchains',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('chain_protocol', sa.Enum('EVM', 'SOL', name='chainprotocol'), nullable=False, comment='链协议类型'),
    sa.Column('chain_name', sa.String(length=255), nullable=False, comment='链名称'),
    sa.Column('chain_code', sa.String(length=50), nullable=False, comment='链代码'),
    sa.Column('coin_type', sa.Integer(), nullable=False, comment='BIP类型'),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('extend_field', sa.JSON(), nullable=True, comment='扩展字段'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('chain_code')
    )
    op.create_table('order_logs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False, comment='订单ID'),
    sa.Column('status_before', sa.Enum('PENDING', 'PARTIAL', 'FILLED', 'CANCELED', 'FAILED', name='orderstatusenum'), nullable=True, comment='变更前状态'),
    sa.Column('status_after', sa.Enum('PENDING', 'PARTIAL', 'FILLED', 'CANCELED', 'FAILED', name='orderstatusenum'), nullable=False, comment='变更后状态'),
    sa.Column('operator', sa.String(length=50), nullable=True, comment='操作人'),
    sa.Column('remark', sa.String(length=255), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('order_logs', schema=None) as batch_op:
        batch_op.create_index('idx_log_order_id', ['order_id'], unique=False)

    op.create_table('user_asset',
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('asset_type_id', sa.Integer(), nullable=False, comment='资产类型ID'),
    sa.Column('available_balance', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=False, comment='可用余额'),
    sa.Column('frozen_balance', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=False, comment='冻结余额'),
    sa.Column('occupied_balance', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=False, comment='占用余额'),
    sa.Column('extend_field', sa.JSON(), nullable=True, comment='扩展字段'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('user_id', 'asset_type_id')
    )
    op.create_table('tokens',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('token_code', sa.String(length=255), nullable=False),
    sa.Column('token_symbol', sa.String(length=50), nullable=False),
    sa.Column('token_name', sa.String(length=255), nullable=False),
    sa.Column('chain_id', sa.Integer(), nullable=False),
    sa.Column('decimals', sa.Integer(), nullable=False),
    sa.Column('token_type', sa.Enum('NATIVE', 'ERC20', 'SPL', name='tokentype'), nullable=False),
    sa.Column('contract_address', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chain_id'], ['blockchains.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token_code')
    )
    op.create_table('asset_types',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='资产类型ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='资产名称'),
    sa.Column('type', sa.Enum('POINTS', 'TOKEN', name='assettypeenum'), nullable=False, comment='资产类型：积分、加密货币'),
    sa.Column('decimals', sa.Integer(), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=True, comment='项目ID（积分类型必填）'),
    sa.Column('token_id', sa.Integer(), nullable=True, comment='加密币ID（加密货币类型必填）'),
    sa.Column('chain_type', sa.String(length=20), nullable=True, comment='链类型（加密货币类型必填）'),
    sa.Column('status', sa.SmallInteger(), server_default='1', nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['token_id'], ['tokens.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('trading_pairs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('base_asset_id', sa.Integer(), nullable=False, comment='基础资产ID'),
    sa.Column('quote_asset_id', sa.Integer(), nullable=False, comment='计价资产ID'),
    sa.Column('pair_name', sa.String(length=50), nullable=False, comment='交易对名称(如GRASS/USDT)'),
    sa.Column('min_price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='最小价格'),
    sa.Column('max_price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='最大价格'),
    sa.Column('price_precision', sa.SmallInteger(), nullable=False, comment='价格精度'),
    sa.Column('amount_precision', sa.SmallInteger(), nullable=False, comment='数量精度'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='tradingpairstatusenum'), server_default='ACTIVE', nullable=True, comment='状态'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['base_asset_id'], ['asset_types.id'], ),
    sa.ForeignKeyConstraint(['quote_asset_id'], ['asset_types.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('pair_name')
    )
    op.create_table('kline_data',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='K线ID'),
    sa.Column('pair_id', sa.Integer(), nullable=False, comment='交易对ID'),
    sa.Column('interval_type', sa.String(length=10), nullable=False, comment='时间间隔类型'),
    sa.Column('open_time', sa.BigInteger(), nullable=False, comment='开盘时间(毫秒时间戳)'),
    sa.Column('close_time', sa.BigInteger(), nullable=False, comment='收盘时间(毫秒时间戳)'),
    sa.Column('open_price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='开盘价'),
    sa.Column('high_price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='最高价'),
    sa.Column('low_price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='最低价'),
    sa.Column('close_price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='收盘价'),
    sa.Column('volume', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=False, comment='成交量(基础货币)'),
    sa.Column('quote_volume', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=False, comment='成交额(计价货币)'),
    sa.Column('trade_count', sa.Integer(), server_default='0', nullable=False, comment='成交笔数'),
    sa.Column('vwap', sa.DECIMAL(precision=18, scale=8), nullable=True, comment='成交量加权平均价格'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['pair_id'], ['trading_pairs.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('pair_id', 'interval_type', 'open_time', name='uk_kline_unique')
    )
    with op.batch_alter_table('kline_data', schema=None) as batch_op:
        batch_op.create_index('idx_close_time', ['close_time'], unique=False)
        batch_op.create_index('idx_pair_interval_close', ['pair_id', 'interval_type', 'close_time'], unique=False)
        batch_op.create_index('idx_pair_interval_time', ['pair_id', 'interval_type', 'open_time'], unique=False)

    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False, comment='订单ID(UUID)'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('order_type', sa.Enum('LIMIT', 'MARKET', name='ordertypeenum'), nullable=False, comment='订单类型'),
    sa.Column('side', sa.Enum('BUY', 'SELL', name='ordersideenum'), nullable=False, comment='买卖方向'),
    sa.Column('pair_id', sa.Integer(), nullable=False, comment='交易对ID'),
    sa.Column('price', sa.DECIMAL(precision=18, scale=8), nullable=True, comment='委托价格'),
    sa.Column('original_amount', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='委托数量'),
    sa.Column('executed_amount', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=True, comment='已成交数量'),
    sa.Column('executed_value', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=True, comment='已成交金额'),
    sa.Column('fee', sa.DECIMAL(precision=18, scale=8), server_default='0', nullable=True, comment='手续费'),
    sa.Column('status', sa.Enum('PENDING', 'PARTIAL', 'FILLED', 'CANCELED', 'FAILED', name='orderstatusenum'), server_default='PENDING', nullable=True, comment='订单状态'),
    sa.Column('order_source', sa.Enum('CREATE', 'INSTANT_TRADE', name='ordersourceenum'), server_default='CREATE', nullable=True, comment='订单来源'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['pair_id'], ['trading_pairs.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('orders', schema=None) as batch_op:
        batch_op.create_index('idx_pair_status', ['pair_id', 'status'], unique=False)
        batch_op.create_index('idx_user_time', ['user_id', 'created_at'], unique=False)

    op.create_table('order_matches',
    sa.Column('id', sa.Integer(), nullable=False, comment='成交ID(UUID)'),
    sa.Column('order_id', sa.Integer(), nullable=False, comment='订单ID'),
    sa.Column('maker_order_id', sa.Integer(), nullable=False, comment='挂单方订单ID'),
    sa.Column('taker_order_id', sa.Integer(), nullable=False, comment='吃单方订单ID'),
    sa.Column('price', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='成交价格'),
    sa.Column('amount', sa.DECIMAL(precision=18, scale=8), nullable=False, comment='成交数量'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['maker_order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['taker_order_id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('order_matches', schema=None) as batch_op:
        batch_op.create_index('idx_order_id', ['order_id'], unique=False)

    op.drop_table('messages')
    with op.batch_alter_table('device_projects', schema=None) as batch_op:
        batch_op.drop_index('idx_device_project_device')
        batch_op.drop_index('idx_device_project_state')

    with op.batch_alter_table('notify', schema=None) as batch_op:
        batch_op.alter_column('notify_status',
               existing_type=mysql.INTEGER(),
               nullable=True,
               comment='通知状态',
               existing_comment='通知状态 0 - 草稿; 1 - 已发布; 2 - 撤回',
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('notify_priority',
               existing_type=mysql.INTEGER(),
               nullable=True,
               comment='通知优先级',
               existing_comment='通知优先级 0 - 低; 1 - 普通; 2 - 高',
               existing_server_default=sa.text("'1'"))
        batch_op.alter_column('is_deleted',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               comment=None,
               existing_comment='是否删除',
               existing_server_default=sa.text("'0'"))

    with op.batch_alter_table('point_records', schema=None) as batch_op:
        batch_op.alter_column('points',
               existing_type=mysql.DECIMAL(precision=10, scale=2),
               type_=sa.Float(),
               nullable=False)

    with op.batch_alter_table('service_metric_details', schema=None) as batch_op:
        batch_op.drop_index('idx_service_metric_details_device')
        batch_op.drop_index('idx_service_metric_details_service')
        batch_op.drop_index('idx_service_metric_details_time')

    with op.batch_alter_table('service_metrics', schema=None) as batch_op:
        batch_op.drop_index('idx_service_metrics_device')
        batch_op.drop_index('idx_service_metrics_service')
        batch_op.drop_column('yesterday_running_time')
        batch_op.drop_column('yesterday_points')

    with op.batch_alter_table('service_metrics_snapshots', schema=None) as batch_op:
        batch_op.drop_index('idx_device_service_day')
        batch_op.create_index('idx_device_service_day', ['device_id', 'day', 'service_name'], unique=False)
        batch_op.drop_index('idx_service_name_day')
        batch_op.create_index('idx_service_name_day', ['day', 'service_name'], unique=False)

    with op.batch_alter_table('task_types', schema=None) as batch_op:
        batch_op.alter_column('points',
               existing_type=mysql.INTEGER(),
               type_=sa.Float(),
               existing_nullable=False)

    with op.batch_alter_table('user_notify', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'notify', ['notify_id'], ['id'])
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('wallet_generated', schema=None) as batch_op:
        batch_op.alter_column('encrypt_key',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=255),
               type_=sa.String(length=2048),
               existing_nullable=False)

    with op.batch_alter_table('wallet_group', schema=None) as batch_op:
        batch_op.alter_column('nonce',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=255),
               nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('wallet_group', schema=None) as batch_op:
        batch_op.alter_column('nonce',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=255),
               nullable=True)

    with op.batch_alter_table('wallet_generated', schema=None) as batch_op:
        batch_op.alter_column('encrypt_key',
               existing_type=sa.String(length=2048),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=255),
               existing_nullable=False)

    with op.batch_alter_table('user_notify', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('task_types', schema=None) as batch_op:
        batch_op.alter_column('points',
               existing_type=sa.Float(),
               type_=mysql.INTEGER(),
               existing_nullable=False)

    with op.batch_alter_table('service_metrics_snapshots', schema=None) as batch_op:
        batch_op.drop_index('idx_service_name_day')
        batch_op.create_index('idx_service_name_day', ['service_name', 'day'], unique=False)
        batch_op.drop_index('idx_device_service_day')
        batch_op.create_index('idx_device_service_day', ['device_id', 'service_name', 'day'], unique=False)

    with op.batch_alter_table('service_metrics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('yesterday_points', mysql.INTEGER(), server_default=sa.text("'0'"), autoincrement=False, nullable=False))
        batch_op.add_column(sa.Column('yesterday_running_time', mysql.INTEGER(), server_default=sa.text("'0'"), autoincrement=False, nullable=False))
        batch_op.create_index('idx_service_metrics_service', ['service_name'], unique=False)
        batch_op.create_index('idx_service_metrics_device', ['device_id'], unique=False)

    with op.batch_alter_table('service_metric_details', schema=None) as batch_op:
        batch_op.create_index('idx_service_metric_details_time', ['started_at'], unique=False)
        batch_op.create_index('idx_service_metric_details_service', ['service_name'], unique=False)
        batch_op.create_index('idx_service_metric_details_device', ['device_id'], unique=False)

    with op.batch_alter_table('point_records', schema=None) as batch_op:
        batch_op.alter_column('points',
               existing_type=sa.Float(),
               type_=mysql.DECIMAL(precision=10, scale=2),
               nullable=True)

    with op.batch_alter_table('notify', schema=None) as batch_op:
        batch_op.alter_column('is_deleted',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               comment='是否删除',
               existing_server_default=sa.text("'0'"))
        batch_op.alter_column('notify_priority',
               existing_type=mysql.INTEGER(),
               nullable=False,
               comment='通知优先级 0 - 低; 1 - 普通; 2 - 高',
               existing_comment='通知优先级',
               existing_server_default=sa.text("'1'"))
        batch_op.alter_column('notify_status',
               existing_type=mysql.INTEGER(),
               nullable=False,
               comment='通知状态 0 - 草稿; 1 - 已发布; 2 - 撤回',
               existing_comment='通知状态',
               existing_server_default=sa.text("'0'"))

    with op.batch_alter_table('device_projects', schema=None) as batch_op:
        batch_op.create_index('idx_device_project_state', ['project_id', 'state'], unique=False)
        batch_op.create_index('idx_device_project_device', ['device_id'], unique=False)

    op.create_table('messages',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('msg_type', mysql.ENUM('airdrop', 'trade', 'security'), nullable=False),
    sa.Column('subject', mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=255), nullable=False),
    sa.Column('content', mysql.TEXT(charset='utf8mb4', collation='utf8mb4_general_ci'), nullable=True),
    sa.Column('is_read', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('is_deleted', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_user'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    with op.batch_alter_table('order_matches', schema=None) as batch_op:
        batch_op.drop_index('idx_order_id')

    op.drop_table('order_matches')
    with op.batch_alter_table('orders', schema=None) as batch_op:
        batch_op.drop_index('idx_user_time')
        batch_op.drop_index('idx_pair_status')

    op.drop_table('orders')
    with op.batch_alter_table('kline_data', schema=None) as batch_op:
        batch_op.drop_index('idx_pair_interval_time')
        batch_op.drop_index('idx_pair_interval_close')
        batch_op.drop_index('idx_close_time')

    op.drop_table('kline_data')
    op.drop_table('trading_pairs')
    op.drop_table('asset_types')
    op.drop_table('tokens')
    op.drop_table('user_asset')
    with op.batch_alter_table('order_logs', schema=None) as batch_op:
        batch_op.drop_index('idx_log_order_id')

    op.drop_table('order_logs')
    op.drop_table('blockchains')
    op.drop_table('asset_transaction')
    with op.batch_alter_table('asset_freeze_logs', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_asset_freeze_logs_user_id'))
        batch_op.drop_index(batch_op.f('ix_asset_freeze_logs_asset_type_id'))

    op.drop_table('asset_freeze_logs')
    with op.batch_alter_table('address_mappings', schema=None) as batch_op:
        batch_op.drop_index('idx_user_chain')
        batch_op.drop_index('idx_address_chain')

    op.drop_table('address_mappings')
    # ### end Alembic commands ###
