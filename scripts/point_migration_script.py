#!/usr/bin/env python3
"""
积分系统无感迁移脚本

这个脚本将执行以下操作：
1. 创建新的归档表结构
2. 迁移用户积分到资产系统
3. 生成历史数据归档
4. 验证数据一致性
5. 切换到新系统

使用方法：
python scripts/point_migration_script.py --phase=prepare
python scripts/point_migration_script.py --phase=migrate
python scripts/point_migration_script.py --phase=validate
python scripts/point_migration_script.py --phase=switch
"""

import sys
import os
import argparse
import logging
from datetime import datetime, date, timedelta

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.models.base import db
from app.services.point_migration_service import PointMigrationService
from app.services.point_archive_service import PointArchiveService
from app.models.point_archive import PointMigrationLog

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('point_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PointMigrationScript:
    """积分迁移脚本"""
    
    def __init__(self, app):
        self.app = app
        
    def run_phase(self, phase: str, **kwargs):
        """运行指定阶段的迁移"""
        with self.app.app_context():
            if phase == 'prepare':
                return self.prepare_migration(**kwargs)
            elif phase == 'migrate':
                return self.migrate_data(**kwargs)
            elif phase == 'archive':
                return self.archive_historical_data(**kwargs)
            elif phase == 'validate':
                return self.validate_migration(**kwargs)
            elif phase == 'switch':
                return self.switch_to_new_system(**kwargs)
            elif phase == 'rollback':
                return self.rollback_migration(**kwargs)
            else:
                raise ValueError(f"未知的迁移阶段: {phase}")
    
    def prepare_migration(self, **kwargs):
        """准备阶段：创建表结构和配置"""
        logger.info("🚀 开始准备阶段...")
        
        try:
            # 1. 执行数据库迁移脚本
            logger.info("📊 创建归档表结构...")
            migration_sql_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                'migrations/create_point_archive_tables.sql'
            )
            
            if os.path.exists(migration_sql_path):
                logger.info(f"执行迁移脚本: {migration_sql_path}")
                # 注意：这里需要手动执行SQL脚本
                logger.warning("请手动执行 migrations/create_point_archive_tables.sql")
            else:
                logger.error("迁移脚本不存在")
                return False
            
            # 2. 验证表结构
            logger.info("🔍 验证表结构...")
            try:
                from app.models.point_archive import (
                    PointRecordArchive, PointStatisticsSnapshot,
                    PointMigrationLog, PointArchiveConfig
                )
                
                # 测试表是否可访问
                PointRecordArchive.query.count()
                PointStatisticsSnapshot.query.count()
                PointMigrationLog.query.count()
                PointArchiveConfig.query.count()
                
                logger.info("✅ 表结构验证成功")
            except Exception as e:
                logger.error(f"❌ 表结构验证失败: {e}")
                return False
            
            # 3. 检查现有数据
            from app.models.points import PointRecord
            total_records = PointRecord.query.count()
            logger.info(f"📈 当前积分记录总数: {total_records:,}")
            
            # 4. 创建迁移日志
            migration_log = PointMigrationLog(
                migration_type='prepare_migration',
                migration_date=date.today(),
                start_time=datetime.utcnow(),
                status='success',
                total_records=total_records,
                config={'phase': 'prepare'}
            )
            migration_log.mark_completed('success')
            db.session.add(migration_log)
            db.session.commit()
            
            logger.info("🎉 准备阶段完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 准备阶段失败: {e}")
            return False
    
    def migrate_data(self, batch_size=5000, dry_run=False, **kwargs):
        """迁移阶段：将积分数据迁移到资产系统"""
        logger.info("🔄 开始数据迁移阶段...")
        
        try:
            # 1. 迁移用户积分到资产系统
            logger.info("💰 迁移用户积分到资产系统...")
            result = PointMigrationService.migrate_user_points_to_assets(
                batch_size=batch_size,
                dry_run=dry_run
            )
            
            logger.info(f"迁移结果: {result}")
            
            if result['failed'] > 0:
                logger.warning(f"⚠️ 有 {result['failed']} 个用户迁移失败")
                for error in result['errors']:
                    logger.error(f"错误: {error}")
            
            # 2. 生成积分统计快照
            if not dry_run:
                logger.info("📸 生成积分统计快照...")
                snapshot_result = PointMigrationService.generate_point_statistics_snapshot()
                logger.info(f"快照结果: {snapshot_result}")
            
            logger.info("🎉 数据迁移阶段完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据迁移阶段失败: {e}")
            return False
    
    def archive_historical_data(self, days_back=90, **kwargs):
        """归档阶段：归档历史数据"""
        logger.info("📦 开始历史数据归档阶段...")
        
        try:
            end_date = date.today() - timedelta(days=1)  # 昨天
            start_date = end_date - timedelta(days=days_back)
            
            current_date = start_date
            daily_success = 0
            daily_failed = 0
            
            # 按天归档
            logger.info(f"📅 开始按天归档 {start_date} 到 {end_date}")
            while current_date <= end_date:
                try:
                    result = PointArchiveService.archive_daily_records(current_date)
                    logger.info(f"日归档 {current_date}: {result['success']} 成功, {result['failed']} 失败")
                    daily_success += result['success']
                    daily_failed += result['failed']
                except Exception as e:
                    logger.error(f"日归档 {current_date} 失败: {e}")
                    daily_failed += 1
                
                current_date += timedelta(days=1)
            
            # 按周归档（最近几周）
            logger.info("📅 开始按周归档...")
            weekly_success = 0
            weekly_failed = 0
            
            for weeks_ago in range(1, 5):  # 最近4周
                target_date = end_date - timedelta(weeks=weeks_ago)
                try:
                    result = PointArchiveService.archive_weekly_records(target_date)
                    logger.info(f"周归档 {target_date}: {result['success']} 成功, {result['failed']} 失败")
                    weekly_success += result['success']
                    weekly_failed += result['failed']
                except Exception as e:
                    logger.error(f"周归档 {target_date} 失败: {e}")
                    weekly_failed += 1
            
            # 按月归档（最近几个月）
            logger.info("📅 开始按月归档...")
            monthly_success = 0
            monthly_failed = 0
            
            for months_ago in range(1, 7):  # 最近6个月
                target_date = end_date.replace(day=1) - timedelta(days=1)
                for _ in range(months_ago - 1):
                    target_date = target_date.replace(day=1) - timedelta(days=1)
                
                try:
                    result = PointArchiveService.archive_monthly_records(target_date)
                    logger.info(f"月归档 {target_date}: {result['success']} 成功, {result['failed']} 失败")
                    monthly_success += result['success']
                    monthly_failed += result['failed']
                except Exception as e:
                    logger.error(f"月归档 {target_date} 失败: {e}")
                    monthly_failed += 1
            
            logger.info(f"📦 归档完成 - 日: {daily_success}/{daily_success+daily_failed}, "
                       f"周: {weekly_success}/{weekly_success+weekly_failed}, "
                       f"月: {monthly_success}/{monthly_success+monthly_failed}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 历史数据归档失败: {e}")
            return False
    
    def validate_migration(self, sample_users=100, **kwargs):
        """验证阶段：验证数据一致性"""
        logger.info("🔍 开始数据验证阶段...")
        
        try:
            # 1. 全量数据验证
            logger.info("📊 执行全量数据验证...")
            validation_result = PointMigrationService.validate_migration_data()
            
            logger.info(f"验证结果:")
            logger.info(f"  总用户数: {validation_result['total_users']}")
            logger.info(f"  一致用户数: {validation_result['consistent_users']}")
            logger.info(f"  不一致用户数: {validation_result['inconsistent_users']}")
            logger.info(f"  资产系统缺失: {validation_result['missing_in_assets']}")
            logger.info(f"  积分系统缺失: {validation_result['missing_in_points']}")
            
            # 2. 抽样验证
            if sample_users > 0:
                logger.info(f"🎯 执行抽样验证 ({sample_users} 个用户)...")
                from app.models.user import User
                import random
                
                all_users = User.query.all()
                sample_user_ids = random.sample([u.id for u in all_users], 
                                              min(sample_users, len(all_users)))
                
                sample_errors = 0
                for user_id in sample_user_ids:
                    try:
                        user_validation = PointMigrationService.validate_migration_data(user_id)
                        if user_validation['inconsistent_users'] > 0:
                            sample_errors += 1
                            logger.warning(f"用户 {user_id} 数据不一致")
                    except Exception as e:
                        sample_errors += 1
                        logger.error(f"用户 {user_id} 验证失败: {e}")
                
                logger.info(f"抽样验证完成: {sample_errors}/{len(sample_user_ids)} 个用户有问题")
            
            # 3. 性能测试
            logger.info("⚡ 执行性能测试...")
            start_time = datetime.utcnow()
            
            # 测试排名查询性能
            from app.services.points_service import PointsService
            rank_list = PointsService.get_rank_list(page=1, per_page=50)
            
            end_time = datetime.utcnow()
            query_time = (end_time - start_time).total_seconds()
            
            logger.info(f"排名查询性能: {query_time:.3f}秒 (50条记录)")
            
            # 判断验证是否通过
            success_rate = validation_result['consistent_users'] / validation_result['total_users'] if validation_result['total_users'] > 0 else 0
            
            if success_rate >= 0.99:  # 99%以上一致性
                logger.info("✅ 数据验证通过")
                return True
            else:
                logger.error(f"❌ 数据验证失败，一致性仅为 {success_rate:.2%}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 数据验证阶段失败: {e}")
            return False
    
    def switch_to_new_system(self, **kwargs):
        """切换阶段：切换到新系统"""
        logger.info("🔄 开始系统切换阶段...")
        
        try:
            # 这个阶段主要是配置切换，代码已经支持新旧系统兼容
            logger.info("✅ 系统已支持新数据源，无需额外切换操作")
            
            # 记录切换日志
            migration_log = PointMigrationLog(
                migration_type='switch_system',
                migration_date=date.today(),
                start_time=datetime.utcnow(),
                status='success',
                config={'phase': 'switch'}
            )
            migration_log.mark_completed('success')
            db.session.add(migration_log)
            db.session.commit()
            
            logger.info("🎉 系统切换完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统切换失败: {e}")
            return False
    
    def rollback_migration(self, **kwargs):
        """回滚阶段：回滚到原系统"""
        logger.info("⏪ 开始回滚操作...")
        
        try:
            # 这里可以实现回滚逻辑
            # 由于我们的设计是兼容的，主要是清理新数据
            logger.warning("⚠️ 回滚操作需要手动处理")
            logger.info("建议操作:")
            logger.info("1. 停止新的积分写入")
            logger.info("2. 清理 UserAsset 中的积分数据")
            logger.info("3. 清理归档表数据")
            logger.info("4. 恢复原有查询逻辑")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 回滚操作失败: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description='积分系统迁移脚本')
    parser.add_argument('--phase', required=True, 
                       choices=['prepare', 'migrate', 'archive', 'validate', 'switch', 'rollback'],
                       help='迁移阶段')
    parser.add_argument('--batch-size', type=int, default=5000, help='批处理大小')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式')
    parser.add_argument('--days-back', type=int, default=90, help='归档天数')
    parser.add_argument('--sample-users', type=int, default=100, help='抽样验证用户数')
    
    args = parser.parse_args()
    
    # 创建应用
    app = create_app()
    script = PointMigrationScript(app)
    
    logger.info(f"开始执行迁移阶段: {args.phase}")
    
    try:
        success = script.run_phase(
            args.phase,
            batch_size=args.batch_size,
            dry_run=args.dry_run,
            days_back=args.days_back,
            sample_users=args.sample_users
        )
        
        if success:
            logger.info(f"✅ 阶段 {args.phase} 执行成功")
            sys.exit(0)
        else:
            logger.error(f"❌ 阶段 {args.phase} 执行失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 执行过程中发生异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
