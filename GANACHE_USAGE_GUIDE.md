# Ganache 工具使用指南

根据你的 Ganache 设置，我们提供了不同的工具来满足不同的需求。

## 🎯 工具选择指南

### 1. 只读操作（推荐用于 Ganache GUI）

**适用场景**：
- 使用 Ganache GUI
- 只需要查询账户、区块、交易信息
- 监控区块链状态
- 测试区块链监听服务

**使用工具**：`ganache_readonly_utils.py`

```python
from ganache_readonly_utils import create_readonly_utils

# 创建只读工具
ganache = create_readonly_utils()

# 查询账户信息
accounts = ganache.get_all_accounts_info()
print(f"账户数量: {len(accounts)}")

# 获取网络信息
network_info = ganache.get_network_info()
print(f"当前区块: {network_info['block_number']}")

# 监控新区块
ganache.monitor_new_blocks(max_blocks=5)

# 打印摘要
ganache.print_summary()
```

### 2. 完整操作（需要 ganache-cli）

**适用场景**：
- 需要发送交易（ETH 转账、合约部署）
- 需要 airdrop 和 mint 操作
- 完整的测试环境

**使用工具**：`app/utils/ganache_test_utils.py`

**前提条件**：使用 ganache-cli 而不是 Ganache GUI

```bash
# 启动 ganache-cli
ganache-cli --host 0.0.0.0 --port 7545 --accounts 10 --deterministic
```

```python
from app.utils.ganache_test_utils import create_ganache_utils

# 创建完整工具
ganache = create_ganache_utils()

# ETH 转账
tx_hash = ganache.airdrop_eth("0x接收地址", 1.0)

# 部署 ERC20 代币
token_info = ganache.deploy_erc20_token(
    name="Test Token",
    symbol="TEST", 
    initial_supply=1000000
)

# 代币转账
ganache.transfer_tokens(
    contract_address=token_info['contract_address'],
    to_address="0x接收地址",
    amount=100.0
)
```

## 🔧 当前环境分析

根据测试结果，你的环境是：

- ✅ **Ganache GUI v7.7.3** 运行在端口 7545
- ✅ **连接正常**，可以查询所有信息
- ✅ **10 个账户**，每个有 10000 ETH
- ⚠️ **账户被锁定**，无法直接发送交易

## 📋 推荐使用方案

### 方案 1：只读监控（当前可用）

使用 `ganache_readonly_utils.py` 进行：

```bash
# 快速测试
python ganache_readonly_utils.py

# 或者在代码中使用
python -c "
from ganache_readonly_utils import create_readonly_utils
ganache = create_readonly_utils()
ganache.print_summary()
"
```

**优点**：
- ✅ 立即可用，无需额外配置
- ✅ 适合测试区块链监听服务
- ✅ 可以监控区块和交易
- ✅ 完整的查询功能

### 方案 2：完整功能（需要切换到 ganache-cli）

如果需要发送交易，建议切换到 ganache-cli：

```bash
# 1. 关闭 Ganache GUI
# 2. 启动 ganache-cli
ganache-cli --host 0.0.0.0 --port 7545 --accounts 10 --deterministic

# 3. 使用完整工具
python test_ganache_utils.py
```

**优点**：
- ✅ 支持所有操作（转账、部署、mint）
- ✅ 账户自动解锁
- ✅ 更适合开发测试

### 方案 3：配置 Ganache GUI（高级）

如果坚持使用 Ganache GUI，可以尝试：

1. **在 Ganache GUI 中启用账户解锁**：
   - 打开 Ganache GUI
   - 进入 Settings → Server
   - 启用 "Unlock accounts" 选项

2. **或者使用私钥签名**（需要修改代码）

## 🎯 针对区块链监听服务的建议

对于你的区块链监听服务测试，推荐使用**方案 1（只读工具）**：

```python
# 在区块链监听服务中使用
from ganache_readonly_utils import create_readonly_utils

def test_blockchain_listener():
    # 连接到 Ganache
    ganache = create_readonly_utils()
    
    # 获取当前区块高度
    start_block = ganache.web3.eth.block_number
    
    # 监控新区块（模拟监听服务）
    def block_callback(block_info):
        print(f"监听到新区块: {block_info['number']}")
        print(f"交易数量: {block_info['transaction_count']}")
        
        # 这里可以测试你的区块链监听逻辑
        for tx_hash in block_info['transactions']:
            tx_info = ganache.get_transaction_info(tx_hash)
            print(f"交易: {tx_hash} -> {tx_info}")
    
    # 开始监控
    ganache.monitor_new_blocks(callback=block_callback, max_blocks=10)
```

## 🚀 快速开始

1. **立即可用的只读测试**：
   ```bash
   python ganache_readonly_utils.py
   ```

2. **测试区块链监听**：
   ```python
   from ganache_readonly_utils import create_readonly_utils
   ganache = create_readonly_utils()
   ganache.monitor_new_blocks(max_blocks=5)
   ```

3. **如需完整功能**：
   ```bash
   # 切换到 ganache-cli
   ganache-cli --port 7545 --accounts 10 --deterministic
   
   # 然后使用
   python test_ganache_utils.py
   ```

## 💡 总结

- ✅ **当前环境完全支持只读操作**
- ✅ **可以立即开始测试区块链监听服务**
- ✅ **所有查询功能都正常工作**
- ⚠️ **如需发送交易，建议切换到 ganache-cli**

你现在可以使用只读工具来测试你的区块链监听服务，这对于大部分开发和测试需求已经足够了！
