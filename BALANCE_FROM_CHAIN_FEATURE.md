# 从链上查询余额功能实现

## 🎯 功能目标

为钱包服务添加从区块链上直接查询余额的功能：

- **配置控制**：通过 `blockchain.extend_field.balance_from_chain` 控制是否启用
- **智能切换**：根据配置自动选择查询方式（链上 vs 内部数据库）
- **兼容性**：保持现有功能不变，新功能作为可选项

## 🔧 实现方案

### 核心逻辑

```python
# 检查是否需要从链上查询余额
balance_from_chain = False
if chain.extend_field and chain.extend_field.get("balance_from_chain") is True:
    balance_from_chain = True

if balance_from_chain:
    # 从链上查询余额
    web3_facade = Web3Facade.get_instance()
    balance_wei = web3_facade.get_balance(wallet.address, token)
    balance = Decimal(balance_wei) / Decimal(10 ** token.decimals)
else:
    # 使用内部余额（原有逻辑）
    user_asset = UserAsset.query.filter_by(
        user_id=user_id,
        asset_type_id=asset_type.id
    ).first()
    balance = user_asset.available_balance if user_asset else Decimal(0)
```

### 配置示例

```json
{
  "rpc_url": "https://mainnet.infura.io/v3/YOUR_PROJECT_ID",
  "explorer": "https://etherscan.io",
  "balance_from_chain": true
}
```

## 📊 功能特性

### 1. **配置驱动**

| 配置值 | 行为 | 说明 |
|--------|------|------|
| `true` | 链上查询 | 使用 Web3Facade.get_balance 查询实时余额 |
| `false` | 内部查询 | 使用 UserAsset 表中的余额数据 |
| 未设置 | 内部查询 | 默认行为，向后兼容 |
| `null` | 内部查询 | 空值处理 |

### 2. **错误处理**

```python
try:
    web3_facade = Web3Facade.get_instance()
    balance_wei = web3_facade.get_balance(wallet.address, token)
    balance = Decimal(balance_wei) / Decimal(10 ** token.decimals)
    current_app.logger.debug(f"从链上查询余额: {wallet.address} - {token.token_symbol}: {balance}")
except Exception as e:
    current_app.logger.error(f"从链上查询余额失败: {wallet.address} - {token.token_symbol}: {e}")
    balance = Decimal(0)  # 失败时返回0余额
```

**错误处理策略**：
- 链上查询失败时返回 0 余额
- 记录详细的错误日志
- 不影响其他钱包的查询

### 3. **性能考虑**

**优势**：
- 实时准确的余额数据
- 无需同步延迟
- 支持所有链上资产

**注意事项**：
- 网络延迟影响响应时间
- API 调用可能有频率限制
- 需要稳定的 RPC 连接

## 🔍 使用场景

### 场景1：实时交易应用
```json
{
  "chain_name": "Ethereum",
  "chain_code": "ETH",
  "extend_field": {
    "rpc_url": "https://mainnet.infura.io/v3/YOUR_PROJECT_ID",
    "balance_from_chain": true
  }
}
```
**适用于**：需要实时余额的交易应用

### 场景2：内部积分系统
```json
{
  "chain_name": "Internal Points",
  "chain_code": "POINTS",
  "extend_field": {
    "balance_from_chain": false
  }
}
```
**适用于**：内部积分、游戏币等非链上资产

### 场景3：混合模式
```json
{
  "chains": [
    {
      "chain_code": "ETH",
      "extend_field": {"balance_from_chain": true}
    },
    {
      "chain_code": "POINTS",
      "extend_field": {"balance_from_chain": false}
    }
  ]
}
```
**适用于**：同时支持链上资产和内部资产

## 🛠️ 技术实现

### 文件修改

**`app/services/wallet_service.py`**
- 修改 `get_wallet_groups` 方法
- 添加链上余额查询逻辑
- 保持向后兼容性

### 关键代码变更

```python
# 第108-138行：新增余额查询逻辑
# 检查是否需要从链上查询余额
balance_from_chain = False
if chain.extend_field and chain.extend_field.get("balance_from_chain") is True:
    balance_from_chain = True

if balance_from_chain:
    # 从链上查询余额
    try:
        web3_facade = Web3Facade.get_instance()
        balance_wei = web3_facade.get_balance(wallet.address, token)
        # 转换为可读格式
        balance = Decimal(balance_wei) / Decimal(10 ** token.decimals)
        current_app.logger.debug(f"从链上查询余额: {wallet.address} - {token.token_symbol}: {balance}")
    except Exception as e:
        current_app.logger.error(f"从链上查询余额失败: {wallet.address} - {token.token_symbol}: {e}")
        balance = Decimal(0)
else:
    # 原有的内部余额查询逻辑
    user_asset = db.session.query(UserAsset).filter(
        UserAsset.user_id == user_id,
        UserAsset.asset_type_id == asset_type.id
    ).first()
    
    if user_asset:
        balance = user_asset.available_balance
    else:
        balance = Decimal(0)
```

## 🧪 测试验证

### 测试脚本

创建了 `test_balance_from_chain.py` 测试脚本，包含：

1. **基本功能测试**：
   - 创建启用链上查询的区块链
   - 测试 Web3Facade.get_balance 调用
   - 验证逻辑分支

2. **集成功能测试**：
   - 测试不同配置组合
   - 验证配置解析逻辑
   - 确保向后兼容性

3. **Web3Facade 测试**：
   - 验证方法存在性
   - 测试调用逻辑

### 运行测试

```bash
python test_balance_from_chain.py
```

### 预期结果

```
✅ 检测到 balance_from_chain = True，会调用链上查询
✅ 检测到 balance_from_chain = False，会使用内部余额
✅ 空 extend_field 正确处理，使用内部余额
🎉 余额查询逻辑测试完成！
```

## 📋 配置管理

### 数据库配置

```sql
-- 启用链上余额查询
UPDATE blockchains 
SET extend_field = JSON_SET(extend_field, '$.balance_from_chain', true)
WHERE chain_code = 'ETH';

-- 禁用链上余额查询
UPDATE blockchains 
SET extend_field = JSON_SET(extend_field, '$.balance_from_chain', false)
WHERE chain_code = 'POINTS';
```

### API 配置

```python
# 通过 API 更新配置
from app.services.blockchain_service import BlockchainService

BlockchainService.update('ETH', {
    'extend_field': {
        'rpc_url': 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
        'balance_from_chain': True
    }
})
```

## 🔄 工作流程

```mermaid
flowchart TD
    A[开始查询余额] --> B[获取区块链配置]
    B --> C{检查 extend_field}
    C -->|存在| D{balance_from_chain = true?}
    C -->|不存在| E[使用内部余额]
    D -->|是| F[调用 Web3Facade.get_balance]
    D -->|否| E
    F --> G{链上查询成功?}
    G -->|是| H[返回链上余额]
    G -->|否| I[记录错误，返回0]
    E --> J[查询 UserAsset 表]
    J --> K[返回内部余额]
    H --> L[结束]
    I --> L
    K --> L
```

## 🎯 优势与注意事项

### ✅ 优势

1. **实时性**：获取最新的链上余额
2. **准确性**：直接从区块链查询，无同步延迟
3. **灵活性**：可按链配置，支持混合模式
4. **兼容性**：不影响现有功能

### ⚠️ 注意事项

1. **性能影响**：链上查询比数据库查询慢
2. **网络依赖**：需要稳定的 RPC 连接
3. **成本考虑**：可能产生 API 调用费用
4. **错误处理**：需要妥善处理网络异常

### 💡 最佳实践

1. **选择性启用**：只对需要实时余额的链启用
2. **监控告警**：监控 RPC 调用成功率
3. **缓存策略**：考虑添加短期缓存减少调用
4. **降级机制**：链上查询失败时的降级策略

通过这个功能，系统现在可以灵活地选择余额查询方式，既支持实时的链上查询，也保持了原有的高性能内部查询能力！
