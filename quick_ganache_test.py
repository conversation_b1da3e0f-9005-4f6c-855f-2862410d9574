#!/usr/bin/env python3
"""
快速 Ganache 测试脚本
用于快速验证 Ganache 连接和基本功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.ganache_test_utils import create_ganache_utils


def quick_test():
    """快速测试函数"""
    print("🔍 快速 Ganache 连接测试...")
    
    try:
        # 创建工具实例
        ganache = create_ganache_utils()
        
        # 获取基本信息
        accounts = ganache.get_all_accounts_info()
        print(f"✅ 成功连接! 发现 {len(accounts)} 个账户")
        
        # 显示前3个账户
        for i, account in enumerate(accounts[:3]):
            print(f"  账户 {i}: {account['address'][:10]}... 余额: {account['balance_eth']} ETH")
        
        # 执行一次简单转账
        if len(accounts) >= 2:
            print(f"\n💸 测试转账: 0.1 ETH 从账户0到账户1")
            tx_hash = ganache.airdrop_eth(accounts[1]["address"], 0.1)
            print(f"✅ 转账成功: {tx_hash[:20]}...")
        
        print(f"\n📊 当前区块高度: {ganache.web3.eth.block_number}")
        print(f"🌐 网络ID: {ganache.web3.eth.chain_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def deploy_test_token():
    """部署测试代币"""
    print("\n🪙 部署测试代币...")
    
    try:
        ganache = create_ganache_utils()
        
        # 部署简单的测试代币
        token_info = ganache.deploy_erc20_token(
            name="Quick Test Token",
            symbol="QTT",
            initial_supply=10000
        )
        
        print(f"✅ 代币部署成功!")
        print(f"📍 合约地址: {token_info['contract_address']}")
        print(f"🏷️  代币信息: {token_info['name']} ({token_info['symbol']})")
        print(f"💰 初始供应量: {token_info['initial_supply']} QTT")
        
        # 查询部署者余额
        deployer = ganache.accounts[0]
        balance = ganache.get_token_balance(token_info['contract_address'], deployer)
        print(f"🏦 部署者余额: {balance} QTT")
        
        return token_info
        
    except Exception as e:
        print(f"❌ 代币部署失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 Ganache 快速测试工具")
    print("="*50)
    
    # 基本连接测试
    if not quick_test():
        print("\n💡 提示:")
        print("1. 确保 Ganache 已启动")
        print("2. 检查端口是否为 8545")
        print("3. 确认 RPC URL: http://127.0.0.1:8545")
        return
    
    # 询问是否部署测试代币
    print("\n" + "="*50)
    response = input("是否部署测试代币? (y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        token_info = deploy_test_token()
        if token_info:
            print(f"\n💡 你可以使用以下信息进行测试:")
            print(f"合约地址: {token_info['contract_address']}")
            print(f"代币符号: {token_info['symbol']}")
    
    print("\n✅ 快速测试完成!")


if __name__ == "__main__":
    main()
