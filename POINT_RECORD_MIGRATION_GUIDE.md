# PointRecord 表优化迁移指南

## 🎯 项目概述

本项目旨在解决 PointRecord 表数据量过大（接近900万条）导致的性能问题，通过以下两个主要方向进行优化：

1. **积分账户迁移**：将用户积分账户和排名数据迁移到现有的资产系统
2. **历史数据归档**：将分钟级历史记录按天、周、月合并归档

## 📋 迁移前准备

### 1. 环境要求
- Python 3.8+
- MySQL 5.7+ 或 PostgreSQL 12+
- 足够的磁盘空间（建议预留原数据量的50%空间）
- 数据库备份

### 2. 备份数据
```bash
# MySQL 备份
mysqldump -u username -p database_name point_records > point_records_backup.sql

# 完整数据库备份
mysqldump -u username -p database_name > full_backup.sql
```

### 3. 检查依赖
```bash
# 安装测试依赖
pip install -r requirements.txt

# 检查数据库连接
python -c "from app import create_app; app = create_app(); print('数据库连接正常')"
```

## 🚀 迁移步骤

### 第一步：准备阶段
```bash
# 创建归档表结构
python scripts/point_migration_script.py --phase=prepare

# 验证表结构
mysql -u username -p database_name -e "SHOW TABLES LIKE '%point%'"
```

**预期结果**：
- 创建 `point_records_archive` 表
- 创建 `point_statistics_snapshot` 表
- 创建 `point_migration_log` 表
- 创建 `point_archive_config` 表

### 第二步：数据迁移
```bash
# 试运行（推荐先执行）
python scripts/point_migration_script.py --phase=migrate --dry-run --batch-size=1000

# 正式迁移
python scripts/point_migration_script.py --phase=migrate --batch-size=5000
```

**预期结果**：
- 用户积分数据迁移到 `user_assets` 表
- 生成积分统计快照
- 创建迁移日志

### 第三步：历史数据归档
```bash
# 归档最近90天的数据
python scripts/point_migration_script.py --phase=archive --days-back=90
```

**预期结果**：
- 按天归档历史数据
- 按周归档数据
- 按月归档数据
- 更新原始记录状态

### 第四步：数据验证
```bash
# 验证数据一致性
python scripts/point_migration_script.py --phase=validate --sample-users=1000

# 运行完整测试套件
python tests/test_point_migration.py --mode=test

# 性能基准测试
python tests/test_point_migration.py --mode=benchmark
```

**预期结果**：
- 数据一致性 > 99%
- 排名查询 < 200ms
- 用户查询 < 100ms

### 第五步：系统切换
```bash
# 切换到新系统
python scripts/point_migration_script.py --phase=switch
```

**预期结果**：
- API 接口使用新数据源
- 性能显著提升
- 用户无感知切换

## 📊 性能对比

### 迁移前
| 操作 | 响应时间 | 数据量 |
|------|----------|--------|
| 用户积分查询 | 500-2000ms | 900万条记录 |
| 排名查询 | 1000-5000ms | 全表扫描 |
| 历史查询 | 2000-10000ms | 分钟级记录 |

### 迁移后
| 操作 | 响应时间 | 数据量 |
|------|----------|--------|
| 用户积分查询 | 50-100ms | 索引查询 |
| 排名查询 | 100-200ms | 快照数据 |
| 历史查询 | 200-500ms | 归档数据 |

## 🔧 新功能使用

### 1. 积分历史查询 API
```bash
# 获取用户30天积分历史
GET /api/points/history?days=30&archive_type=daily

# 获取用户积分统计
GET /api/points/statistics
```

### 2. 管理员工具
```python
from app.services.point_migration_service import PointMigrationService
from app.services.point_archive_service import PointArchiveService

# 验证数据一致性
result = PointMigrationService.validate_migration_data()

# 获取归档统计
stats = PointArchiveService.get_archive_statistics(days=30)

# 清理旧数据
cleanup_result = PointMigrationService.cleanup_old_point_records(days_to_keep=90)
```

### 3. 定时任务配置
```python
# 在 Celery 中添加定时任务
from celery.schedules import crontab

CELERYBEAT_SCHEDULE = {
    'daily-point-archive': {
        'task': 'app.tasks.archive_daily_points',
        'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点
    },
    'weekly-point-archive': {
        'task': 'app.tasks.archive_weekly_points',
        'schedule': crontab(hour=3, minute=0, day_of_week=1),  # 每周一凌晨3点
    },
    'generate-point-snapshots': {
        'task': 'app.tasks.generate_point_snapshots',
        'schedule': crontab(hour=1, minute=0),  # 每天凌晨1点
    }
}
```

## 🔍 监控和维护

### 1. 数据一致性监控
```bash
# 每日数据验证
python tests/test_point_migration.py --mode=validate

# 检查迁移状态
python -c "
from app.services.point_migration_service import PointMigrationService
status = PointMigrationService.get_migration_status()
print(f'积分记录: {status[\"statistics\"][\"total_point_records\"]:,}')
print(f'用户资产: {status[\"statistics\"][\"total_user_assets\"]:,}')
"
```

### 2. 性能监控
```bash
# 性能基准测试
python tests/test_point_migration.py --mode=benchmark

# 数据库查询分析
EXPLAIN SELECT * FROM user_assets WHERE asset_type_id = ? ORDER BY available_balance DESC LIMIT 50;
```

### 3. 存储空间监控
```sql
-- 检查表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'your_database_name' 
    AND table_name LIKE '%point%'
ORDER BY (data_length + index_length) DESC;
```

## 🚨 故障处理

### 1. 迁移失败回滚
```bash
# 回滚到原系统
python scripts/point_migration_script.py --phase=rollback

# 手动清理（如果需要）
mysql -u username -p database_name << EOF
DELETE FROM user_assets WHERE asset_type_id = (SELECT id FROM asset_types WHERE name = 'UBI积分');
DELETE FROM point_records_archive;
DELETE FROM point_statistics_snapshot;
DELETE FROM point_migration_log;
EOF
```

### 2. 数据不一致修复
```python
from app.services.point_migration_service import PointMigrationService

# 重新迁移特定用户
result = PointMigrationService.migrate_user_points_to_assets(dry_run=False)

# 验证并修复
validation = PointMigrationService.validate_migration_data()
if validation['inconsistent_users'] > 0:
    # 查看详细信息并手动修复
    print(validation['inconsistent_details'])
```

### 3. 性能问题排查
```sql
-- 检查慢查询
SHOW PROCESSLIST;

-- 检查索引使用情况
EXPLAIN SELECT * FROM user_assets WHERE user_id = ? AND asset_type_id = ?;

-- 优化建议
CREATE INDEX idx_user_asset_type ON user_assets(user_id, asset_type_id);
CREATE INDEX idx_snapshot_user_date ON point_statistics_snapshot(user_id, snapshot_date);
```

## 📈 预期收益

### 1. 性能提升
- **查询速度提升 80%+**：从秒级降到毫秒级
- **数据库负载降低 60%+**：减少全表扫描
- **存储空间节省 70%+**：归档压缩历史数据

### 2. 用户体验改善
- **页面加载更快**：积分相关页面响应时间显著缩短
- **实时排名**：支持更频繁的排名更新
- **历史分析**：提供更丰富的积分趋势分析

### 3. 系统稳定性
- **减少慢查询**：避免数据库锁等待
- **提高并发能力**：支持更多用户同时访问
- **便于扩展**：为未来功能扩展奠定基础

## 📞 技术支持

如果在迁移过程中遇到问题，请：

1. **查看日志**：检查 `point_migration.log` 文件
2. **运行测试**：执行测试套件确认问题
3. **数据验证**：使用验证工具检查数据一致性
4. **性能测试**：运行基准测试确认性能

## 🎉 迁移完成检查清单

- [ ] 所有表结构创建成功
- [ ] 用户积分数据迁移完成
- [ ] 历史数据归档完成
- [ ] 数据一致性验证通过（>99%）
- [ ] 性能测试达标
- [ ] API 接口正常工作
- [ ] 定时任务配置完成
- [ ] 监控告警设置完成
- [ ] 文档更新完成
- [ ] 团队培训完成

完成以上检查清单后，PointRecord 表优化迁移即告成功！🎊
