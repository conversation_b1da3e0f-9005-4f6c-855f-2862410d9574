#!/usr/bin/env python3
"""
Ganache 测试工具使用示例
确保你已经启动了 Ganache，默认端口 8545
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.ganache_test_utils import create_ganache_utils


def main():
    """主测试函数"""
    print("🚀 开始 Ganache 测试...")
    
    try:
        # 1. 创建 Ganache 工具实例
        ganache = create_ganache_utils("http://127.0.0.1:8545")
        
        # 2. 获取所有账户信息
        print("\n" + "="*60)
        print("📋 账户信息")
        print("="*60)
        accounts = ganache.get_all_accounts_info()
        for i, account in enumerate(accounts[:5]):  # 只显示前5个账户
            print(f"账户 {i}: {account['address']}")
            print(f"  余额: {account['balance_eth']} ETH")
            print(f"  Nonce: {account['nonce']}")
            print()
        
        # 3. 执行一些 ETH 转账测试
        print("\n" + "="*60)
        print("💸 ETH 转账测试")
        print("="*60)
        
        if len(accounts) >= 3:
            # 从账户0向账户1转账 2 ETH
            tx1 = ganache.airdrop_eth(accounts[1]["address"], 2.0)
            print(f"转账1完成: {tx1}")
            
            # 从账户0向账户2转账 1.5 ETH
            tx2 = ganache.airdrop_eth(accounts[2]["address"], 1.5)
            print(f"转账2完成: {tx2}")
            
            # 查看转账后的余额
            print("\n转账后余额:")
            for i in [0, 1, 2]:
                info = ganache.get_account_info(accounts[i]["address"])
                print(f"账户 {i}: {info['balance_eth']} ETH")
        
        # 4. 部署 ERC20 代币
        print("\n" + "="*60)
        print("🪙 ERC20 代币测试")
        print("="*60)
        
        # 部署测试代币
        token_info = ganache.deploy_erc20_token(
            name="Ganache Test Token",
            symbol="GTT",
            initial_supply=1000000  # 100万个代币
        )
        
        print(f"代币部署成功!")
        print(f"合约地址: {token_info['contract_address']}")
        print(f"代币名称: {token_info['name']}")
        print(f"代币符号: {token_info['symbol']}")
        print(f"初始供应量: {token_info['initial_supply']} {token_info['symbol']}")
        
        # 5. 查询代币信息
        print("\n查询代币信息:")
        contract_address = token_info['contract_address']
        token_details = ganache.get_token_info(contract_address)
        print(f"链上代币信息: {json.dumps(token_details, indent=2)}")
        
        # 6. 查询部署者的代币余额
        deployer_address = accounts[0]["address"]
        deployer_balance = ganache.get_token_balance(contract_address, deployer_address)
        print(f"\n部署者代币余额: {deployer_balance} {token_info['symbol']}")
        
        # 7. 代币转账测试
        if len(accounts) >= 2:
            print("\n代币转账测试:")
            recipient_address = accounts[1]["address"]
            
            # 转账 1000 个代币
            transfer_amount = 1000.0
            tx_hash = ganache.transfer_tokens(
                contract_address=contract_address,
                to_address=recipient_address,
                amount=transfer_amount,
                from_address=deployer_address
            )
            
            print(f"代币转账完成: {tx_hash}")
            
            # 查询转账后的余额
            print("\n转账后代币余额:")
            deployer_balance_after = ganache.get_token_balance(contract_address, deployer_address)
            recipient_balance_after = ganache.get_token_balance(contract_address, recipient_address)
            
            print(f"部署者余额: {deployer_balance_after} {token_info['symbol']}")
            print(f"接收者余额: {recipient_balance_after} {token_info['symbol']}")
        
        # 8. 创建完整测试场景
        print("\n" + "="*60)
        print("🎭 创建测试场景")
        print("="*60)
        
        scenario = ganache.create_test_scenario()
        print("测试场景创建完成!")
        print(f"当前区块高度: {scenario['block_number']}")
        print(f"网络ID: {scenario['network_id']}")
        print(f"测试转账数量: {len(scenario['test_transfers'])}")
        
        # 9. 显示最终状态
        print("\n" + "="*60)
        print("📊 最终状态")
        print("="*60)
        
        final_accounts = ganache.get_all_accounts_info()
        for i, account in enumerate(final_accounts[:5]):
            print(f"账户 {i}: {account['address']}")
            print(f"  ETH 余额: {account['balance_eth']}")
            if 'contract_address' in locals():
                token_balance = ganache.get_token_balance(contract_address, account['address'])
                print(f"  {token_info['symbol']} 余额: {token_balance}")
            print()
        
        print("✅ 所有测试完成!")
        
    except ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        print("请确保 Ganache 已启动并运行在 http://127.0.0.1:8545")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
