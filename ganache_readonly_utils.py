#!/usr/bin/env python3
"""
Ganache 只读工具类
专门用于查询和监控，不执行交易
适用于 Ganache GUI 环境
"""

import json
from decimal import Decimal
from typing import Optional, Dict, List, Any
from web3 import Web3


class GanacheReadOnlyUtils:
    """Ganache 只读工具类"""
    
    def __init__(self, ganache_url: str = "http://127.0.0.1:7545"):
        """
        初始化 Ganache 连接
        
        Args:
            ganache_url: Ganache RPC URL
        """
        self.web3 = Web3(Web3.HTTPProvider(ganache_url))
        
        # 验证连接
        if not self.web3.is_connected():
            raise ConnectionError(f"无法连接到 Ganache: {ganache_url}")
        
        print(f"✅ 已连接到 Ganache: {ganache_url}")
        print(f"📊 当前区块高度: {self.web3.eth.block_number}")
        
        # 获取默认账户（Ganache 预设账户）
        self.accounts = self.web3.eth.accounts
        self.default_account = self.accounts[0] if self.accounts else None
        
        print(f"💰 可用账户数量: {len(self.accounts)}")
        if self.default_account:
            balance = self.web3.eth.get_balance(self.default_account)
            print(f"🏦 默认账户余额: {self.web3.from_wei(balance, 'ether')} ETH")

    def get_account_info(self, address: Optional[str] = None) -> Dict[str, Any]:
        """
        获取账户信息
        
        Args:
            address: 账户地址，默认使用第一个账户
            
        Returns:
            账户信息字典
        """
        if not address:
            address = self.default_account
            
        if not address:
            raise ValueError("没有可用的账户地址")
            
        balance_wei = self.web3.eth.get_balance(address)
        balance_eth = self.web3.from_wei(balance_wei, 'ether')
        nonce = self.web3.eth.get_transaction_count(address)
        
        return {
            "address": address,
            "balance_wei": balance_wei,
            "balance_eth": str(balance_eth),
            "nonce": nonce
        }

    def get_all_accounts_info(self) -> List[Dict[str, Any]]:
        """
        获取所有 Ganache 账户信息
        
        Returns:
            账户信息列表
        """
        accounts_info = []
        
        for i, address in enumerate(self.accounts):
            info = self.get_account_info(address)
            info["index"] = i
            accounts_info.append(info)
            
        return accounts_info

    def get_network_info(self) -> Dict[str, Any]:
        """
        获取网络信息
        
        Returns:
            网络信息字典
        """
        try:
            latest_block = self.web3.eth.get_block('latest')
            
            return {
                "block_number": self.web3.eth.block_number,
                "network_id": self.web3.eth.chain_id,
                "gas_price": self.web3.eth.gas_price,
                "latest_block_hash": latest_block.hash.hex(),
                "latest_block_timestamp": latest_block.timestamp,
                "connected": self.web3.is_connected()
            }
        except Exception as e:
            return {
                "error": str(e),
                "connected": False
            }

    def get_transaction_info(self, tx_hash: str) -> Dict[str, Any]:
        """
        获取交易信息
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            交易信息字典
        """
        try:
            tx = self.web3.eth.get_transaction(tx_hash)
            receipt = self.web3.eth.get_transaction_receipt(tx_hash)
            
            return {
                "hash": tx.hash.hex(),
                "from": tx["from"],
                "to": tx.to,
                "value": str(self.web3.from_wei(tx.value, 'ether')),
                "gas": tx.gas,
                "gas_price": tx.gasPrice,
                "nonce": tx.nonce,
                "block_number": tx.blockNumber,
                "status": receipt.status,
                "gas_used": receipt.gasUsed,
                "logs": len(receipt.logs)
            }
        except Exception as e:
            return {"error": str(e)}

    def get_block_info(self, block_number: int = None) -> Dict[str, Any]:
        """
        获取区块信息
        
        Args:
            block_number: 区块号，默认为最新区块
            
        Returns:
            区块信息字典
        """
        try:
            if block_number is None:
                block = self.web3.eth.get_block('latest')
            else:
                block = self.web3.eth.get_block(block_number)
            
            return {
                "number": block.number,
                "hash": block.hash.hex(),
                "parent_hash": block.parentHash.hex(),
                "timestamp": block.timestamp,
                "gas_limit": block.gasLimit,
                "gas_used": block.gasUsed,
                "transaction_count": len(block.transactions),
                "transactions": [tx.hex() for tx in block.transactions]
            }
        except Exception as e:
            return {"error": str(e)}

    def monitor_new_blocks(self, callback=None, max_blocks=10):
        """
        监控新区块
        
        Args:
            callback: 回调函数，接收区块信息
            max_blocks: 最大监控区块数
        """
        print(f"🔍 开始监控新区块（最多 {max_blocks} 个）...")
        
        start_block = self.web3.eth.block_number
        monitored_blocks = 0
        
        while monitored_blocks < max_blocks:
            current_block = self.web3.eth.block_number
            
            if current_block > start_block:
                for block_num in range(start_block + 1, current_block + 1):
                    block_info = self.get_block_info(block_num)
                    
                    print(f"📦 新区块 {block_num}: {block_info.get('transaction_count', 0)} 笔交易")
                    
                    if callback:
                        callback(block_info)
                    
                    monitored_blocks += 1
                    if monitored_blocks >= max_blocks:
                        break
                
                start_block = current_block
            
            # 等待一秒
            import time
            time.sleep(1)
        
        print(f"✅ 监控完成，共监控了 {monitored_blocks} 个区块")

    def estimate_gas(self, transaction: Dict[str, Any]) -> int:
        """
        估算交易 Gas
        
        Args:
            transaction: 交易参数
            
        Returns:
            估算的 Gas 数量
        """
        try:
            return self.web3.eth.estimate_gas(transaction)
        except Exception as e:
            print(f"❌ Gas 估算失败: {e}")
            return 0

    def get_contract_info(self, contract_address: str, abi: List[Dict]) -> Dict[str, Any]:
        """
        获取合约信息
        
        Args:
            contract_address: 合约地址
            abi: 合约 ABI
            
        Returns:
            合约信息字典
        """
        try:
            # 检查是否是合约地址
            code = self.web3.eth.get_code(contract_address)
            is_contract = len(code) > 2  # 0x 之后有内容
            
            info = {
                "address": contract_address,
                "is_contract": is_contract,
                "code_size": len(code)
            }
            
            if is_contract and abi:
                # 创建合约实例
                contract = self.web3.eth.contract(address=contract_address, abi=abi)
                info["contract_instance"] = True
                
                # 尝试调用一些常见的只读方法
                try:
                    if hasattr(contract.functions, 'name'):
                        info["name"] = contract.functions.name().call()
                    if hasattr(contract.functions, 'symbol'):
                        info["symbol"] = contract.functions.symbol().call()
                    if hasattr(contract.functions, 'decimals'):
                        info["decimals"] = contract.functions.decimals().call()
                    if hasattr(contract.functions, 'totalSupply'):
                        info["total_supply"] = contract.functions.totalSupply().call()
                except Exception as e:
                    info["call_error"] = str(e)
            
            return info
            
        except Exception as e:
            return {"error": str(e)}

    def print_summary(self):
        """打印网络摘要信息"""
        print("\n" + "="*60)
        print("📊 Ganache 网络摘要")
        print("="*60)
        
        # 网络信息
        network_info = self.get_network_info()
        print(f"🌐 网络ID: {network_info.get('network_id', 'N/A')}")
        print(f"📦 当前区块: {network_info.get('block_number', 'N/A')}")
        print(f"⛽ Gas 价格: {network_info.get('gas_price', 'N/A')} wei")
        
        # 账户信息
        accounts = self.get_all_accounts_info()
        print(f"\n💰 账户信息 ({len(accounts)} 个账户):")
        
        total_balance = Decimal('0')
        for i, account in enumerate(accounts[:5]):  # 只显示前5个
            balance = Decimal(account['balance_eth'])
            total_balance += balance
            print(f"  账户 {i}: {account['address'][:10]}... {balance} ETH")
        
        if len(accounts) > 5:
            print(f"  ... 还有 {len(accounts) - 5} 个账户")
        
        print(f"\n💎 总余额: {total_balance} ETH")
        
        # 最新区块信息
        latest_block = self.get_block_info()
        if 'error' not in latest_block:
            print(f"\n📦 最新区块信息:")
            print(f"  区块号: {latest_block['number']}")
            print(f"  交易数: {latest_block['transaction_count']}")
            print(f"  Gas 使用: {latest_block['gas_used']}/{latest_block['gas_limit']}")
        
        print("="*60)


def create_readonly_utils(ganache_url: str = "http://127.0.0.1:7545") -> GanacheReadOnlyUtils:
    """
    创建只读 Ganache 工具实例
    
    Args:
        ganache_url: Ganache RPC URL
        
    Returns:
        GanacheReadOnlyUtils 实例
    """
    return GanacheReadOnlyUtils(ganache_url)


if __name__ == "__main__":
    # 创建只读工具实例
    ganache = create_readonly_utils()
    
    # 打印摘要信息
    ganache.print_summary()
    
    print(f"\n💡 这是只读工具，适用于 Ganache GUI 环境")
    print(f"🔍 可以查询账户、区块、交易等信息")
    print(f"⚠️  如需发送交易，请使用 ganache-cli 或配置 Ganache GUI 的账户解锁")
