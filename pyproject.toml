[project]
name = "box-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "alembic==1.13.1",
    "click==8.1.7",
    "cryptography>=44.0.0",
    "fakeredis>=2.26.2",
    "flask==3.0.1",
    "flask-cors==4.0.0",
    "flask-jwt-extended==4.6.0",
    "flask-migrate==4.0.5",
    "flask-sqlalchemy==3.1.1",
    "itsdangerous==2.1.2",
    "jinja2==3.1.3",
    "jsonschema[format]>=4.23.0",
    "markupsafe==2.1.4",
    "packaging==23.2",
    "pymysql==1.1.0",
    "pytest==7.4.4",
    "pytest-cov==4.1.0",
    "python-dateutil==2.8.2",
    "python-dotenv==1.0.0",
    "pyyaml>=6.0.2",
    "redis>=5.2.1",
    "requests>=2.32.3",
    "sqlalchemy==2.0.25",
    "typing-extensions==4.9.0",
    "werkzeug==3.0.1",
    "Pillow==10.3.0",
    "celery>=5.4.0",
    "gunicorn>=23.0.0",
    "bip32==4.0",
    "bip32utils==0.3.post4",
    "ecdsa==0.19.0",
    "mnemonic==0.21",
    "web3>=7.9.0",
    "solana>=0.36.6",
    "flask-babel>=4.0.0",
    "eth_abi>=5.1.0",
    "base58>=2.1.1",
]
