# Ganache 测试工具使用指南

这个工具集提供了与本地 Ganache 区块链进行交互的便捷方法，支持 ETH 转账、ERC20 代币部署和转账等操作。

## 前置条件

1. **安装 Ganache**
   ```bash
   npm install -g ganache-cli
   # 或者下载 Ganache GUI
   ```

2. **启动 Ganache**
   ```bash
   ganache-cli --host 0.0.0.0 --port 7545 --accounts 10 --deterministic
   ```
   
   或者使用 Ganache GUI，确保设置：
   - RPC Server: HTTP://127.0.0.1:7545
   - Network ID: 1337 (默认)

3. **安装 Python 依赖**
   ```bash
   pip install web3 eth-account
   ```

## 工具文件说明

### 1. `app/utils/ganache_test_utils.py`
主要的工具类，提供以下功能：

- **连接管理**: 自动连接到 Ganache
- **账户操作**: 获取账户信息、余额查询
- **ETH 操作**: 转账、空投
- **ERC20 操作**: 部署代币、转账、余额查询
- **测试场景**: 创建完整的测试环境

### 2. `test_ganache_utils.py`
完整的测试示例，演示所有功能：

```bash
python test_ganache_utils.py
```

### 3. `quick_ganache_test.py`
快速连接测试，验证 Ganache 是否正常工作：

```bash
python quick_ganache_test.py
```

## 使用示例

### 基本使用

```python
from app.utils.ganache_test_utils import create_ganache_utils

# 创建工具实例
ganache = create_ganache_utils("http://127.0.0.1:7545")

# 获取账户信息
accounts = ganache.get_all_accounts_info()
print(f"可用账户: {len(accounts)}")

# ETH 转账
tx_hash = ganache.airdrop_eth("0x接收地址", 1.0)  # 转账 1 ETH
print(f"转账完成: {tx_hash}")
```

### 部署和使用 ERC20 代币

```python
# 部署代币
token_info = ganache.deploy_erc20_token(
    name="My Test Token",
    symbol="MTT", 
    initial_supply=1000000  # 100万个代币
)

contract_address = token_info['contract_address']
print(f"代币合约地址: {contract_address}")

# 查询余额
balance = ganache.get_token_balance(contract_address, "0x账户地址")
print(f"代币余额: {balance} MTT")

# 转账代币
tx_hash = ganache.transfer_tokens(
    contract_address=contract_address,
    to_address="0x接收地址",
    amount=100.0  # 转账 100 个代币
)
```

### 创建测试场景

```python
# 创建完整的测试环境
scenario = ganache.create_test_scenario()

print(f"测试账户数: {len(scenario['accounts'])}")
print(f"测试代币: {scenario['token']['name']}")
print(f"测试转账: {len(scenario['test_transfers'])} 笔")
```

## API 参考

### GanacheTestUtils 类

#### 初始化
```python
ganache = GanacheTestUtils(ganache_url="http://127.0.0.1:7545")
```

#### 账户操作
- `get_account_info(address)` - 获取单个账户信息
- `get_all_accounts_info()` - 获取所有账户信息
- `fund_account(address, amount)` - 为账户充值 ETH

#### ETH 操作
- `airdrop_eth(to_address, amount_eth, from_address=None)` - ETH 转账

#### ERC20 操作
- `deploy_erc20_token(name, symbol, initial_supply)` - 部署 ERC20 代币
- `transfer_tokens(contract_address, to_address, amount)` - 转账代币
- `get_token_balance(contract_address, account_address)` - 查询代币余额
- `get_token_info(contract_address)` - 获取代币信息

#### 测试工具
- `create_test_scenario()` - 创建完整测试场景

## 常见问题

### 1. 连接失败
```
❌ 无法连接到 Ganache: http://127.0.0.1:7545
```
**解决方案:**
- 确保 Ganache 已启动
- 检查端口号是否正确
- 确认防火墙设置

### 2. 交易失败
```
❌ 余额不足: 需要 1.0 ETH，当前余额 0.5 ETH
```
**解决方案:**
- 检查账户余额
- 使用有足够余额的账户
- 降低转账金额

### 3. 合约部署失败
```
❌ 部署失败: gas required exceeds allowance
```
**解决方案:**
- 增加 gas limit
- 检查账户余额是否足够支付 gas
- 简化合约代码

## 高级用法

### 自定义 Gas 设置
```python
# 在工具类中修改默认 gas 设置
transaction = {
    'gas': 500000,  # 增加 gas limit
    'gasPrice': ganache.web3.to_wei('50', 'gwei'),  # 增加 gas price
    # ...
}
```

### 监听事件
```python
# 监听代币转账事件
contract = ganache.web3.eth.contract(address=contract_address, abi=abi)
event_filter = contract.events.Transfer.create_filter(fromBlock='latest')

for event in event_filter.get_new_entries():
    print(f"转账事件: {event}")
```

### 批量操作
```python
# 批量转账
accounts = ganache.get_all_accounts_info()
for i in range(1, 5):  # 向账户1-4转账
    ganache.airdrop_eth(accounts[i]['address'], 0.5)
```

## 注意事项

1. **仅用于测试**: 这些工具仅适用于本地 Ganache 测试环境
2. **私钥安全**: Ganache 的私钥是公开的，不要在主网使用
3. **Gas 费用**: 测试环境中 gas 费用很低，主网会有很大差异
4. **网络重置**: 重启 Ganache 会重置所有数据

## 扩展功能

你可以根据需要扩展工具类：

1. **添加更多 ERC 标准**: ERC721 (NFT)、ERC1155 等
2. **批量操作**: 批量转账、批量部署
3. **事件监听**: 实时监听区块链事件
4. **数据分析**: 交易统计、gas 使用分析

## 支持

如果遇到问题，请检查：
1. Ganache 是否正常运行
2. Python 依赖是否正确安装
3. 网络连接是否正常
4. 账户余额是否充足
