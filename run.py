import os

from dotenv import load_dotenv

# 加载环境变量
basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, ".env"))

from app import create_app
from app.celery_app import create_celery_app
# 导入任务模块
import app.tasks.monitoring

# 创建 Flask 应用
app = create_app(os.getenv("FLASK_ENV", "default"))

# 创建 Celery 应用并确保配置正确加载
celery = create_celery_app(app)

# 确保 Celery 配置被正确应用
celery.conf.update(app.config['CELERY_CONFIG'])

if __name__ == "__main__":
    app.run()
